<template>
  <div class="system-data-audit">
    <h2>系统采集数据审核</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form
        :model="queryForm"
        class="query-form"
        label-width="120px"
        label-position="left"
      >
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="数据开始日期">
              <el-date-picker
                v-model="queryForm.startDate"
                type="month"
                placeholder="选择开始月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="数据结束日期">
              <el-date-picker
                v-model="queryForm.endDate"
                type="month"
                placeholder="选择结束月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button class="query-btn" @click="handleQuery">查询</el-button>
              <el-button class="export-btn" @click="handleExport"
                >导出</el-button
              >
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
      >
        <el-table-column prop="data_date" label="数据日期" />
        <el-table-column prop="source_system" label="源系统" />
        <el-table-column prop="source_report_name" label="源系统报表名称" />
        <el-table-column prop="source_field_name" label="源系统字段名称" />
        <el-table-column prop="indicator_name" label="指标名称" />
        <el-table-column prop="brh_cd" label="分支机构代码" />
        <el-table-column prop="brh_name" label="分支机构名称" />
        <el-table-column prop="indicator_value" label="指标值" align="right" />
        <el-table-column prop="audt_department" label="指标审核部门" />
        <el-table-column prop="etl_time" label="采集时间">
          <template #default="scope">
            {{ formatDateTime(scope.row.etl_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="audtor" label="审核人" />
        <el-table-column prop="audt_time" label="审核时间">
          <template #default="scope">
            {{ formatDateTime(scope.row.audt_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="audt_relt" label="审核结果">
          <template #default="scope">
            <el-tag
              :type="getAuditStatusTagType(scope.row.audt_relt)"
              size="small"
            >
              {{ scope.row.audt_relt || "待审核" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="audt_nopass_reason" label="审核不通过原因" />
        <el-table-column label="操作" align="center" fixed="right">
          <template #default="scope">
            <el-button
              v-if="
                !scope.row.audt_relt ||
                scope.row.audt_relt === '待审核' ||
                scope.row.audt_relt === ''
              "
              class="audit-pass-btn"
              size="small"
              @click="handleAuditPass(scope.row)"
            >
              审核通过
            </el-button>
            <el-button
              v-if="
                !scope.row.audt_relt ||
                scope.row.audt_relt === '待审核' ||
                scope.row.audt_relt === ''
              "
              class="audit-reject-btn"
              size="small"
              @click="handleAuditReject(scope.row)"
            >
              审核不通过
            </el-button>
            <span
              v-if="
                scope.row.audt_relt &&
                scope.row.audt_relt !== '待审核' &&
                scope.row.audt_relt !== ''
              "
            >
              已审核
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 审核不通过原因对话框 -->
    <el-dialog
      v-model="rejectDialogVisible"
      title="审核不通过原因"
      width="500px"
      @close="handleRejectDialogClose"
    >
      <el-form :model="rejectForm" label-width="100px">
        <el-form-item label="不通过原因" required>
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            :rows="4"
            placeholder="请输入审核不通过的原因"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button
            class="audit-reject-confirm-btn"
            @click="handleRejectConfirm"
            :loading="auditLoading"
            >确定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import * as XLSX from "xlsx";

// 查询表单
const queryForm = reactive({
  startDate: "",
  endDate: "",
});

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const totalCount = ref(0);
const loading = ref(false);

// 表格数据
const tableData = ref([]);

// 审核相关
const rejectDialogVisible = ref(false);
const auditLoading = ref(false);
const currentAuditRow = ref(null);
const rejectForm = reactive({
  reason: "",
});

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search);
  return {
    oacde: params.get("oacde") || "current_user",
    roleid: params.get("roleid") || "001",
  };
};

const urlParams = getUrlParams();

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return "";
  return new Date(dateTime).toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });
};

// 获取审核状态标签类型
const getAuditStatusTagType = (status) => {
  switch (status) {
    case "已通过":
      return "success";
    case "待审核":
      return "warning";
    case "":
      return "warning";
    case null:
      return "warning";
    case undefined:
      return "warning";
    default:
      return "info";
  }
};

// 查询
const handleQuery = async () => {
  loading.value = true;

  try {
    // 构建查询参数
    const params = new URLSearchParams();

    // 添加分页参数
    const offset = (currentPage.value - 1) * pageSize.value;
    params.append("limit", pageSize.value.toString());
    params.append("offset", offset.toString());

    // 添加排序参数
    params.append("order", "data_date.desc,etl_time.desc");

    // 添加查询条件
    if (queryForm.startDate) {
      params.append("data_date", `gte.${queryForm.startDate}`);
    }

    if (queryForm.endDate) {
      params.append("data_date", `lte.${queryForm.endDate}`);
    }

    // 构建完整的URL
    const url = `http://10.242.194.62:7300/etl_data_audt_list?${params.toString()}`;

    // 发送请求
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Accept-Profile": "mkt_mang",
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // 获取总数（需要单独请求）
    try {
      const countParams = new URLSearchParams();
      countParams.append("select", "count");

      // 添加相同的过滤条件
      if (queryForm.startDate) {
        countParams.append("data_date", `gte.${queryForm.startDate}`);
      }

      if (queryForm.endDate) {
        countParams.append("data_date", `lte.${queryForm.endDate}`);
      }

      const countResponse = await fetch(
        `http://10.242.194.62:7300/etl_data_audt_list?${countParams.toString()}`,
        {
          headers: {
            "Accept-Profile": "mkt_mang",
            "Content-Type": "application/json",
            Prefer: "count=exact",
          },
        }
      );

      if (countResponse.ok) {
        const countData = await countResponse.json();
        totalCount.value = parseInt(countData[0]?.count || 0);
      } else {
        totalCount.value = data.length;
      }
    } catch (countError) {
      console.warn("获取总数失败:", countError);
      totalCount.value = data.length;
    }

    tableData.value = data;
  } catch (error) {
    console.error("查询数据时发生错误:", error);
    ElMessage.error("查询数据失败，请检查网络连接");
    tableData.value = [];
    totalCount.value = 0;
  } finally {
    loading.value = false;
  }
};

// 审核通过
const handleAuditPass = async (row) => {
  try {
    await ElMessageBox.confirm("确认审核通过该条数据？", "确认审核", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    auditLoading.value = true;

    // 调用审核API - 使用uuid主键进行更新
    if (!row.uuid) {
      ElMessage.error("记录缺少UUID，无法进行审核操作");
      return;
    }

    const response = await fetch(
      `http://10.242.194.62:7300/etl_data_audt_list?uuid=eq.${row.uuid}`,
      {
        method: "PATCH",
        headers: {
          "Content-Profile": "mkt_mang",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          audtor: urlParams.oacde,
          audt_time: new Date().toISOString(),
          audt_relt: "已通过",
          audt_nopass_reason: null,
        }),
      }
    );

    if (response.ok) {
      ElMessage.success("审核通过成功");
      handleQuery(); // 重新查询数据
    } else {
      ElMessage.error("审核操作失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("审核通过失败:", error);
      ElMessage.error("审核操作失败");
    }
  } finally {
    auditLoading.value = false;
  }
};

// 审核不通过
const handleAuditReject = (row) => {
  currentAuditRow.value = row;
  rejectForm.reason = "";
  rejectDialogVisible.value = true;
};

// 审核不通过确认
const handleRejectConfirm = async () => {
  if (!rejectForm.reason.trim()) {
    ElMessage.warning("请输入审核不通过原因");
    return;
  }

  try {
    auditLoading.value = true;

    // 调用审核API - 使用uuid主键进行更新
    if (!currentAuditRow.value.uuid) {
      ElMessage.error("记录缺少UUID，无法进行审核操作");
      return;
    }

    const response = await fetch(
      `http://10.242.194.62:7300/etl_data_audt_list?uuid=eq.${currentAuditRow.value.uuid}`,
      {
        method: "PATCH",
        headers: {
          "Content-Profile": "mkt_mang",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          audtor: urlParams.oacde,
          audt_time: new Date().toISOString(),
          audt_relt: "不通过",
          audt_nopass_reason: rejectForm.reason.trim(),
        }),
      }
    );

    if (response.ok) {
      ElMessage.success("审核不通过操作成功");
      rejectDialogVisible.value = false;
      handleQuery(); // 重新查询数据
    } else {
      ElMessage.error("审核操作失败");
    }
  } catch (error) {
    console.error("审核不通过失败:", error);
    ElMessage.error("审核操作失败");
  } finally {
    auditLoading.value = false;
  }
};

// 审核不通过对话框关闭
const handleRejectDialogClose = () => {
  rejectForm.reason = "";
  currentAuditRow.value = null;
};

// 导出
const handleExport = async () => {
  try {
    ElMessage.info("正在导出数据，请稍候...");

    // 构建查询参数，获取所有数据（不分页）
    const params = new URLSearchParams();
    params.append("order", "data_date.desc,etl_time.desc");

    // 添加查询条件
    if (queryForm.startDate) {
      params.append("data_date", `gte.${queryForm.startDate}`);
    }

    if (queryForm.endDate) {
      params.append("data_date", `lte.${queryForm.endDate}`);
    }

    // 获取所有数据
    const response = await fetch(
      `http://10.242.194.62:7300/etl_data_audt_list?${params.toString()}`,
      {
        method: "GET",
        headers: {
          "Accept-Profile": "mkt_mang",
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const allData = await response.json();

    if (allData.length === 0) {
      ElMessage.warning("没有数据可导出");
      return;
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        "数据日期",
        "源系统",
        "源系统报表名称",
        "源系统字段名称",
        "指标名称",
        "分支机构代码",
        "分支机构名称",
        "指标值",
        "指标审核部门",
        "采集时间",
        "审核人",
        "审核时间",
        "审核结果",
        "审核不通过原因",
      ],
      // 数据行
      ...allData.map((item) => [
        item.data_date || "",
        item.source_system || "",
        item.source_report_name || "",
        item.source_field_name || "",
        item.indicator_name || "",
        item.brh_cd || "",
        item.brh_name || "",
        item.indicator_value || "",
        item.audt_department || "",
        formatDateTime(item.etl_time),
        item.audtor || "",
        formatDateTime(item.audt_time),
        item.audt_relt || "待审核",
        item.audt_nopass_reason || "",
      ]),
    ];

    // 创建工作簿
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(exportData);

    // 设置列宽
    const colWidths = [
      { wch: 12 }, // 数据日期
      { wch: 15 }, // 源系统
      { wch: 25 }, // 源系统报表名称
      { wch: 25 }, // 源系统字段名称
      { wch: 25 }, // 指标名称
      { wch: 15 }, // 分支机构代码
      { wch: 20 }, // 分支机构名称
      { wch: 15 }, // 指标值
      { wch: 20 }, // 指标审核部门
      { wch: 20 }, // 采集时间
      { wch: 12 }, // 审核人
      { wch: 20 }, // 审核时间
      { wch: 12 }, // 审核结果
      { wch: 25 }, // 审核不通过原因
    ];
    ws["!cols"] = colWidths;

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, "系统采集数据审核");

    // 生成文件名
    const now = new Date();
    const fileName = `系统采集数据审核_${now.getFullYear()}${String(
      now.getMonth() + 1
    ).padStart(2, "0")}${String(now.getDate()).padStart(2, "0")}.xlsx`;

    // 下载文件
    XLSX.writeFile(wb, fileName);

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`);
  } catch (error) {
    console.error("导出数据时发生错误:", error);
    ElMessage.error("数据导出失败，请检查网络连接");
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  handleQuery();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  handleQuery();
};

// 初始化数据
onMounted(() => {
  handleQuery();
});
</script>

<style lang="scss" scoped>
.system-data-audit {
  padding: 20px;
  width: 100%;
  min-height: 100vh;
  background-color: var(--ep-bg-color);
  box-sizing: border-box;
  text-align: left;

  :deep(.el-table) {
    // 滚动条加粗
    .el-scrollbar__bar.is-horizontal {
      height: 10px;
      left: 2px;
    }

    .el-scrollbar__bar.is-vertical {
      top: 2px;
      width: 10px;
    }

    .cell {
      display: inline-block; // 确保 max-width 生效
      white-space: nowrap;
      max-width: 400px;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .cell:hover {
      white-space: pre-wrap;
      /* 使用pre-wrap来允许换行但保留空白字符 */
      overflow: visible;
      text-overflow: clip;
      word-break: keep-all;
      /* 尽量保持单词完整，不强制断开 */
      max-width: 400px;
      /* 保持最大宽度不变 */
      width: 100%;
      /* 确保宽度一致 */
      word-wrap: break-word;
      /* 当单词超过容器宽度时允许换行 */
      display: inline-block;
      /* 确保元素可以正确处理宽度 */
    }

    // 表头样式
    th .cell {
      white-space: nowrap !important; // 强制表头内容不换行
      display: flex;
      align-items: center; // 垂直居中对齐
    }
  }
}

.query-card {
  margin-bottom: 20px;
}

.query-form {
  padding: 10px 0;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  gap: 10px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 自定义按钮颜色 */
.query-btn {
  background-color: #16d585 !important;
  border-color: #16d585 !important;
  color: white !important;
  padding: 8px 16px;
  min-width: auto;
}

.query-btn:hover {
  background-color: #14c578 !important;
  border-color: #14c578 !important;
}

.export-btn {
  background-color: #f4cd33 !important;
  border-color: #f4cd33 !important;
  color: #333 !important;
  padding: 8px 16px;
  min-width: auto;
}

.export-btn:hover {
  background-color: #f3c620 !important;
  border-color: #f3c620 !important;
}

.audit-pass-btn {
  background-color: #67c23a !important;
  border-color: #67c23a !important;
  color: white !important;
  padding: 4px 8px;
  font-size: 12px;
}

.audit-pass-btn:hover {
  background-color: #5daf34 !important;
  border-color: #5daf34 !important;
}

.audit-reject-btn {
  background-color: #f56c6c !important;
  border-color: #f56c6c !important;
  color: white !important;
  padding: 4px 8px;
  font-size: 12px;
  margin-left: 5px;
}

.audit-reject-btn:hover {
  background-color: #f45656 !important;
  border-color: #f45656 !important;
}

.audit-reject-confirm-btn {
  background-color: #f56c6c !important;
  border-color: #f56c6c !important;
  color: white !important;
}

.audit-reject-confirm-btn:hover {
  background-color: #f45656 !important;
  border-color: #f45656 !important;
}

/* 表格样式优化 */
.el-table {
  font-size: 12px;
}

.el-table th {
  background-color: #f5f7fa;
  font-weight: bold;
}

.el-table td {
  padding: 6px 0;
}

/* 状态标签样式 */
.el-tag {
  font-weight: bold;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
