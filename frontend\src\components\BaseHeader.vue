<script>

export default {
  name: 'BaseHeader',
  data() {
    return {
      // 如果需要使用 package.json 的内容，可以通过其他方式获取，例如直接读取文件或通过环境变量注入
      version: '', // 示例：存储版本号
    }
  },
  mounted() {
    // 替代方案：通过构建工具注入 package.json 内容
    this.version = process.env.PACKAGE_VERSION || 'unknown'
  },
}
</script>

<template>
  <header>
    <h1>Base Header</h1>
    <p v-if="version">Version: {{ version }}</p>
  </header>
</template>

<style scoped>
/* 样式保持不变 */
</style>
