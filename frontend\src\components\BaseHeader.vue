<script>
export default {
  name: 'BaseHeader',
  data() {
    return {
      version: '', // 示例：存储版本号
    }
  },
  mounted() {
    // 替代方案：通过 Vite 环境变量注入 package.json 内容
    // this.version = import.meta.env.VITE_PACKAGE_VERSION || 'unknown'
  },
}
</script>

<template>
  <header>
    <!-- <h1>Base Header</h1> -->
    <p v-if="version">Version: {{ version }}</p>
  </header>
</template>

<style scoped>
/* 样式保持不变 */
</style>