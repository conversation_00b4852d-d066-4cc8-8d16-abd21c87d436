<template>
  <div class="etl-task-record">
    <h2>数据计算任务历史记录</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-width="140px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="数据开始日期">
              <el-date-picker
                v-model="queryForm.startDate"
                type="date"
                placeholder="选择日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="数据结束日期">
              <el-date-picker
                v-model="queryForm.endDate"
                type="date"
                placeholder="选择日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="任务状态">
              <el-select v-model="queryForm.runsStats" placeholder="请选择任务状态" clearable style="width: 100%">
                <el-option label="全部" value="" />
                <el-option label="未运行" value="0" />
                <el-option label="运行中" value="1" />
                <el-option label="已完成" value="2" />
                <el-option label="失败" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button class="query-btn" @click="handleQuery">查询</el-button>
              <el-button class="back-btn" @click="goBack">返回</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" border style="width: 100%" v-loading="loading">
        <el-table-column prop="log_id" label="日志编号" width="100" />
        <el-table-column prop="task_name" label="任务名称" min-width="150" />
        <el-table-column prop="dept_name" label="部门" width="120" />
        <el-table-column prop="params" label="参数" width="160" />
        <el-table-column prop="operator" label="操作人" width="120" />
        <el-table-column prop="runs_stats" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="statusTagType(row.runs_stats)">
              {{ statusLabel(row.runs_stats) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="log_date" label="触发时间" width="160" />
        <el-table-column prop="end_time" label="结束时间" width="160" />
        <el-table-column prop="run_duration" label="运行时长(秒)" width="120" />
        <el-table-column prop="error_info" label="错误信息" min-width="180" />

        <el-table-column label="操作" width="100" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" @click="handleView(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="任务日志详情" width="35%">
      <el-descriptions class="detail-descriptions" :column="1" border>
        <el-descriptions-item label="任务编号">{{ currentRow.task_id }}</el-descriptions-item>
        <el-descriptions-item label="任务名称">{{ currentRow.task_name }}</el-descriptions-item>
        <el-descriptions-item label="期间">{{ currentRow.params || '-' }}</el-descriptions-item>    
        <el-descriptions-item label="执行人">{{ currentRow.operator || '-' }}</el-descriptions-item>
        <el-descriptions-item label="执行状态">
          <el-tag :type="statusTagType(currentRow.runs_stats)">
            {{ statusLabel(currentRow.runs_stats) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="执行开始时间">{{ currentRow.log_date }}</el-descriptions-item>
        <el-descriptions-item label="执行结束时间">{{ currentRow.end_time || '-' }}</el-descriptions-item>
        <el-descriptions-item label="耗时(毫秒)">{{ currentRow.run_duration || '-' }}</el-descriptions-item>
        <el-descriptions-item label="错误信息">{{ currentRow.error_info || '-' }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRoute, useRouter  } from 'vue-router'
import { ElMessage } from 'element-plus'
import http from '~/http/http.js'

const router = useRouter()
const route = useRoute()

// 查询条件
const queryForm = reactive({
  startDate: '',
  endDate: '',
  runsStats: '',
  taskId: ''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 当前行数据
const currentRow = ref({})

// 对话框
const detailDialogVisible = ref(false)

// 初始化加载
onMounted(() => {
  queryForm.taskId = route.query.task_id
  handleQuery()
})

// 监听路由变化，动态更新 taskId
watch(
  () => route.query.task_id,
  (newVal) => {
    if (newVal) {
      queryForm.taskId = newVal
      handleQuery()
    }
  }
)

// 返回上一页
const goBack = () => {
  router.back()
}

// 查询方法
const handleQuery = () => {
  loading.value = true

  const filters = {}
  if (queryForm.startDate) filters.log_date = `gte.${queryForm.startDate}`
  if (queryForm.endDate) filters.log_date = `lte.${queryForm.endDate}`
  if (queryForm.runsStats !== '') filters.runs_stats = `eq.${queryForm.runsStats}`
  if (queryForm.taskId) filters.task_id = `eq.${queryForm.taskId}`

  const offset = (currentPage.value - 1) * pageSize.value
  const limit = pageSize.value

  const config = {
    params: {
      ...filters,
      order: 'log_date.desc'
    },
    headers: {
      'Accept': 'application/json',
      'Range': `items=${offset}-${offset + limit - 1}`,
      'Prefer': 'count=exact',
      'Accept-Profile': 'mkt_mang'
    }
  }

  http.get('/etl_task_run_log', {}, config)
    .then(response => {
      tableData.value = response.data || []

      const contentRange = response.headers['content-range']?.toLowerCase().split('/')
      totalCount.value = contentRange && contentRange.length > 1
        ? parseInt(contentRange[1], 10)
        : tableData.value.length
    })
    .catch(error => {
      console.error('API请求失败:', error)
      ElMessage.error('获取数据失败')
    })
    .finally(() => {
      loading.value = false
    })
}

// 重置查询
const resetQuery = () => {
  queryForm.startDate = ''
  queryForm.endDate = ''
  queryForm.runsStats = ''
  handleQuery()
}

// 查看详情
const handleView = (row) => {
  currentRow.value = { ...row }
  detailDialogVisible.value = true
}

// 状态标签类型
const statusTagType = (status) => {
  switch (status) {
    case '0':
      return ''
    case '1':
      return 'warning'
    case '2':
      return 'success'
    case '3':
      return 'danger'
    default:
      return ''
  }
}

// 状态显示文字
const statusLabel = (status) => {
  switch (status) {
    case '0':
      return '未运行'
    case '1':
      return '运行中'
    case '2':
      return '已完成'
    case '3':
      return '失败'
    default:
      return '未知'
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}
</script>

<style scoped>
.etl-task-record {
  padding: 20px;
  width: 100%;
  min-height: 100vh;
  background-color: var(--ep-bg-color);
  box-sizing: border-box;
  text-align: left;
}

.query-card {
  margin-bottom: 20px;
}

.query-form {
  padding: 10px 0;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  gap: 10px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.query-btn {
  background-color: #16D585 !important;
  border-color: #16D585 !important;
  color: white !important;
  padding: 8px 16px;
  min-width: auto;
}

.reset-btn {
  background-color: #8FF6C8 !important;
  border-color: #8FF6C8 !important;
  color: #333 !important;
  padding: 8px 16px;
  min-width: auto;
}

.back-btn {
  background-color: #808080 !important;
  border-color: #808080 !important;
  color: white !important;
  padding: 8px 16px;
  min-width: auto;
}

.back-btn:hover {
  background-color: #666666 !important;
  border-color: #666666 !important;
}
</style>