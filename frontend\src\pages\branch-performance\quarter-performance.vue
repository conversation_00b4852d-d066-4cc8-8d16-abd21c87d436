<template>
  <div class="branch-rating-setting">
    <h2>分支机构季度绩效奖金分配</h2>
    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" :rules="rules" ref="form" inline>
        <el-form-item label="统计开始日期:" :required="true">
          <el-date-picker
            v-model="queryForm.startDate"
            type="month"
            placeholder="选择开始月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="统计截止日期:" :required="true">
          <el-date-picker
            v-model="queryForm.endDate"
            type="month"
            placeholder="选择截止月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="分支机构:">
          <el-select
            v-model="queryForm.brh_cd"
            style="width: 180px"
            filterable
            remote
            clearable
            reserve-keyword
            :remote-method="loadDeptList" >
          <el-option
            v-for="dot in deptOptions"
            :key="dot.value"
            :label="dot.label"
            :value="dot.value">
          </el-option>
          </el-select>
        </el-form-item>

      </el-form>
      <div class="button-group">
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button type="warning" @click="handleExport">导出</el-button>
      </div>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" border style="width: 100%" v-loading="loading">
        <el-table-column prop="data_date" label="日期"  align="center"/>
        <el-table-column prop="brh_cd" label="分支机构代码" align="center"/>
        <el-table-column prop="brh_name" label="分支机构名称"  align="center"/>
        <el-table-column prop="qua_perf_bonus" label="季度绩效奖金金额"  align="center"/>
        <el-table-column prop="oa_id" label="员工编号"  align="center"/>
        <el-table-column prop="brok_cd" label="业务人员代码"  align="center" />
        <el-table-column prop="oa_name" label="员工姓名"  align="center"/>
        <el-table-column prop="oa_lvl" label="员工职级" align="center"/>
        <el-table-column prop="assn_rati" label="分配比例"  align="center" />
        <el-table-column prop="assn_amt" label="分配金额"  align="center"/>
        <el-table-column prop="creator" label="创建人" align="center"/>
        <el-table-column prop="create_time" label="创建时间" align="center"/>
        <el-table-column prop="audtor" label="审核人" align="center"/>
        <el-table-column prop="audt_time" label="审核时间" align="center"/>
        <el-table-column prop="audt_relt" label="审核结果" align="center"/>
        <el-table-column prop="audt_nopass_reason" label="审核不通过原因" align="center"/>
        <el-table-column label="操作" align="center" width="180" fixed="right" >
          <template #default="scope">
            <el-button type="danger" size="small" @click="handleEdit(scope.row)">修改</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/修改对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="700px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分配比例" prop="assn_rati">
              <el-input type="number" min="0" max="100" v-model="formData.assn_rati" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
        </span>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'
import { format } from 'date-fns';
import {now} from "@vueuse/core";
import * as XLSX from 'xlsx';

// 查询表单
const queryForm = reactive({
  startDate:'',
  endDate:'',
  formattedDate: '',
  brh_cd:''
})

const formData = reactive({
  id: '',
  assn_rati: ''
})


// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 分支机构选项
const deptOptions = ref([])

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()
const isEdit = ref(false)

const rules = {
  startDate: [
    { required: true, message: '请选择开始日期', trigger: 'blur' }
  ],
  endDate: [
    { required: true, message: '请选择截止日期', trigger: 'blur' }
  ]
};

// 初始化数据
onMounted(() => {
  // 初始化表格数据，使用查询方法以应用分页逻辑
  handleQuery()
  //loadDeptList()
})

// 表单验证规则
const formRules = {
  assn_rati: [
    { required: true, message: '请输入分配比例', trigger: 'change' }
  ]
}

const loadDeptList = async(query) => {
  let url = "http://10.242.194.62:7300/dc$org";
  axios.get(url, {
    headers:{
      'Accept-Profile':'public'
    },
    params:{
      'department_nam': 'like.%'+query+'%',
    }
  }).then(response => {
    const data = response.data
    deptOptions.value = data.map(item => ({
      value: item.department_id,
      label: item.department_nam
    }))
  })
}

// 查询
const handleQuery = () => {

  loading.value = true
  //调用接口
  let url = "http://10.242.194.62:7300/brh_qua_perf_bonus";
  //处理月份
 /* if (!queryForm.startDate || !queryForm.endDate) {
    ElMessage.error('请选择开始日期和截止日期!')
  }*/
  if (queryForm.endDate) {
    let date = queryForm.endDate;
    let year = date.split('-')[0];
    let month = date.split('-')[1];
    const c = (Math.floor((month -1) / 3 ) + 1) * 3
    queryForm.formattedDate = year+ '-' +c.toString().padStart(2, '0')
  }

  axios.get(url,{
    headers:{
      'Accept-Profile':'mkt_mang',
      'Prefer': 'count=exact'
    },
    params:{
      'data_date':'like.%' + queryForm.formattedDate + '%',
      'brh_cd': 'like.%'+queryForm.brh_cd+ '%',
      'order': 'id',
      'limit': pageSize.value,
      'offset': (currentPage.value - 1) * pageSize.value
    }
  }).then(response=> {
    tableData.value = response.data
    tableData.value.forEach(item=>{
      item.create_time = format(item.create_time, 'yyyy-MM-dd HH:mm:ss')
      item.assn_rati = item.assn_rati * 100 + '%'
      if (item.upd_time) {
        item.audt_time = format(item.audt_time, 'yyyy-MM-dd HH:mm:ss')
      }
      if (queryForm.startDate || queryForm.endDate) {
        item.data_date = queryForm.startDate + '~' + queryForm.endDate
      }
    })
    const rangeHeader = response.headers.get('Content-Range')
    if (rangeHeader) {
      const match = rangeHeader.match(/\/(\d+)$/)
      totalCount.value = match ? parseInt(match[1]) : response.data.length
    } else {
      totalCount.value = response.data.length
    }
    loading.value = false
  })
}

// 重置查询
const resetQuery = () => {
  queryForm.startDate = ''
  queryForm.endDate = ''
  queryForm.formattedDate = ''
  queryForm.brh_name = ''
  handleQuery()
}

// 修改
const handleEdit = (row) => {
  dialogTitle.value = '修改绩效分配比例'
  if (row.audt_relt == '待审核') {
    isEdit.value = true
    resetForm()
    // 填充表单数据
    Object.assign(formData, row)

    dialogVisible.value = true
  } else {
    ElMessage.error('已审核,禁止修改!')
    return
  }

}

// 导出
const handleExport = () => {
  ElMessage.info('正在导出数据，请稍候...')
  // 获取所有数据
  axios.get('http://10.242.194.62:7300/brh_qua_perf_bonus',{
    headers:{
      'Accept-Profile':'mkt_mang',
      'Content-Type': 'application/json'
    },
  }).then(response=> {
    console.log(response)
    if (response.status != 200) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const allData = response.data

    if (allData.length === 0) {
      ElMessage.warning('没有数据可导出')
    }
    // 准备导出数据
    const exportData = [

      // 表头
      [
        '日期', '分支机构代码', '分支机构名称', '季度绩效奖金金额', '员工编号',
        '业务人员代码', '员工姓名', '员工职级','分配比例', '分配金额', '创建人',
          '创建时间',  '审核人',  '审核时间', '审核结果', '审核不通过原因'
      ],
      // 数据行
      ...allData.map(item => [
        item.data_date,
        item.brh_cd,
        item.brh_name ,
        item.qua_perf_bonus ,
        item.oa_id,
        item.brok_cd ,
        item.oa_name,
        item.oa_lvl,
        item.assn_rati,
        item.assn_amt,
        item.creator|| '',
        format(item.create_time, 'yyyy-MM-dd HH:mm:ss'),
        item.audtor || '',
        format(item.audt_time, 'yyyy-MM-dd HH:mm:ss')|| '',
        item.audt_relt || '',
        item.audt_nopass_reason || ''
      ])
    ]
    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)
    // 设置列宽
    const colWidths = [
      { wch: 12 }, // 数据日期
      { wch: 15 }, // 数据来源类型
      { wch: 15 }, // 源系统
      { wch: 15 }, // 报表名称
      { wch: 15 }, // 数据条目数
      { wch: 15 }, // 部门
      { wch: 12 }, // 数据状态
      { wch: 12 }, // 准备人
      { wch: 12 }, // 审核人
      { wch: 15 },  // 表英文名
      { wch: 15 },  // 表英文名
      { wch: 12 }, // 数据状态
      { wch: 12 }, // 准备人
      { wch: 12 }, // 审核人
      { wch: 15 },  // 表英文名
      { wch: 15 }  // 表英文名
    ]
    ws['!cols'] = colWidths
    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '分支机构季度绩效奖金分配')

    // 生成文件名
    const now = new Date()
    const fileName = `分支机构季度绩效奖金分配表_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`)
  })

}

// 提交表单
const handleSubmit = () => {
  if (!formRef.value) return

   formRef.value.validate((valid) => {
    if (valid) {
      const config = {
        headers:{
          'Content-Profile':'mkt_mang'
        }
      };
      let url = "http://10.242.194.62:7300/brh_qua_perf_bonus"+'?id=eq.'+ formData.id;
      const assn = formData.assn_rati;
      const dassn = assn / 100;
      const putData = {
        'id': formData.id,
        'assn_rati': dassn
      };
      axios.patch(url, putData, config).then( ElMessage.success('修改成功'));
      handleQuery()
      dialogVisible.value = false
    }
  })
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(formData, {
    tect_strt_date: '',
    tect_end_date: '',
    brch_grad: '',
    indx_cd: '',
    indx_name: '',
    exam_indx_wght: ''
  })
}

// 处理对话框关闭
const handleDialogClose = () => {
  resetForm()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}
</script>

<style scoped>
.branch-rating-setting {
  padding: 20px;
  width: 100%;
  min-height: 100vh;
  background-color: var(--ep-bg-color);
  box-sizing: border-box;
  text-align: left;
}

.query-card {
  margin-bottom: 20px;
}

.query-form {
  padding: 10px 0;
}

.button-group {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.button-group .el-button {
  margin: 0 10px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>