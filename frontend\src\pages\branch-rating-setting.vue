<template>
  <div class="branch-rating-setting">
    <h2>分支机构分类评级设置</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-width="140px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="分支机构代码">
              <el-select
                v-model="queryForm.branchCodes"
                multiple
                filterable
                remote
                reserve-keyword
                placeholder="请选择分支机构"
                :remote-method="remoteSearchBranch"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in branchOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span>{{ item.value }} - {{ item.label }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button class="query-btn" @click="handleQuery">查询</el-button>
              <el-button class="reset-btn" @click="resetQuery">重置</el-button>
              <el-button class="add-btn" @click="handleAdd">新增</el-button>
              <el-button class="export-btn" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

      <!-- 表格区域 -->
      <el-card class="table-card">
        <el-table :data="tableData" border style="width: 100%" v-loading="loading">
          <el-table-column prop="tect_strt_date" label="生效开始日期" width="120" />
          <el-table-column prop="tect_end_date" label="生效结束日期" width="120" />
          <el-table-column prop="brch_cd" label="分支机构代码" width="120" />
          <el-table-column prop="brch_name" label="分支机构名称" min-width="180" />
          <el-table-column prop="clas_rat_grad" label="分类评定等级" width="120" />
          <el-table-column prop="crt_time" label="创建时间" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.crt_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="upd_time" label="更新时间" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.upd_time) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="260" fixed="right">
            <template #default="scope">
              <el-button class="edit-btn" size="small" @click="handleEdit(scope.row)">修改</el-button>
              <el-button class="history-btn" size="small" @click="handleHistory(scope.row)">历史记录</el-button>
              <el-button class="delete-btn" size="small" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            :current-page="currentPage"
            :page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalCount"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>

      <!-- 新增/修改对话框 -->
      <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="600px"
        @close="handleDialogClose"
      >
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="生效开始日期" prop="tect_strt_date">
                <el-date-picker
                  v-model="formData.tect_strt_date"
                  type="month"
                  placeholder="选择月份"
                  format="YYYY-MM"
                  value-format="YYYY-MM"
                  :disabled="isEdit"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="生效结束日期" prop="tect_end_date">
                <el-date-picker
                  v-model="formData.tect_end_date"
                  type="month"
                  placeholder="选择月份"
                  format="YYYY-MM"
                  value-format="YYYY-MM"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="分支机构代码" prop="brch_cd">
                <el-select
                  v-model="formData.brch_cd"
                  filterable
                  remote
                  reserve-keyword
                  placeholder="请选择分支机构"
                  :remote-method="remoteSearchBranch"
                  :disabled="isEdit"
                  style="width: 100%"
                  @change="handleBranchChange"
                >
                  <el-option
                    v-for="item in branchOptions"
                    :key="item.value"
                    :label="item.value"
                    :value="item.value"
                  >
                    <span>{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="分支机构名称">
                <el-input v-model="formData.brch_name" disabled />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="分类评定等级" prop="clas_rat_grad">
                <el-select v-model="formData.clas_rat_grad" placeholder="请选择评级" style="width: 100%">
                  <el-option label="A" value="A" />
                  <el-option label="B" value="B" />
                  <el-option label="C" value="C" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">关闭</el-button>
            <el-button type="primary" @click="handleSubmit">保存</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 历史记录对话框 -->
      <el-dialog
        v-model="historyDialogVisible"
        title="历史记录"
        width="900px"
      >
        <el-table :data="historyData" border style="width: 100%">
          <el-table-column prop="tect_strt_date" label="生效开始日期" width="120" />
          <el-table-column prop="tect_end_date" label="生效结束日期" width="120" />
          <el-table-column prop="brch_cd" label="分支机构代码" width="120" />
          <el-table-column prop="brch_name" label="分支机构名称" min-width="180" />
          <el-table-column prop="clas_rat_grad" label="分类评定等级" width="120" />
          <el-table-column prop="crt_time" label="创建时间" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.crt_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="upd_time" label="更新时间" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.upd_time) }}
            </template>
          </el-table-column>
        </el-table>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="historyDialogVisible = false">关闭</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as XLSX from 'xlsx'

// 查询表单
const queryForm = reactive({
  branchCodes: []
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacde: params.get('oacde') || 'current_user',
    roleid: params.get('roleid') || '001'
  }
}

const urlParams = getUrlParams()

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 分支机构选项
const branchOptions = ref([])

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()
const isEdit = ref(false)

// 历史记录对话框
const historyDialogVisible = ref(false)
const historyData = ref([])

// 表单数据
const formData = reactive({
  tect_strt_date: '',
  tect_end_date: '',
  brch_cd: '',
  brch_name: '',
  clas_rat_grad: '',
  uuid: ''
})

// 表单验证规则
const formRules = {
  tect_strt_date: [
    { required: true, message: '请选择生效开始日期', trigger: 'change' }
  ],
  tect_end_date: [
    { required: true, message: '请选择生效结束日期', trigger: 'change' }
  ],
  brch_cd: [
    { required: true, message: '请选择分支机构', trigger: 'change' }
  ],
  clas_rat_grad: [
    { required: true, message: '请选择分类评定等级', trigger: 'change' }
  ]
}

// 模拟数据
const mockBranchOptions = [
  { value: '1001', label: '上海浦东营业部' },
  { value: '1002', label: '上海黄浦营业部' },
  { value: '1033', label: '北京海淀区营业部' },
  { value: '1034', label: '北京朝阳区营业部' },
  { value: '1045', label: '广州天河区营业部' },
  { value: '1046', label: '广州越秀区营业部' },
  { value: '1057', label: '深圳南山区营业部' },
  { value: '1058', label: '深圳福田区营业部' },
  { value: '1069', label: '成都锦江区营业部' },
  { value: '1070', label: '成都武侯区营业部' }
]

// 生成模拟数据
const generateMockData = () => {
  const data = []

  // 生成更多数据以便测试分页功能
  for (let i = 0; i < 85; i++) {
    const branchIndex = i % mockBranchOptions.length
    const branch = mockBranchOptions[branchIndex]
    const year = 2023 + Math.floor(i / 30)
    const month = (i % 12) + 1

    data.push({
      id: `${i + 1}`,
      effectiveStartDate: `${year}-${month.toString().padStart(2, '0')}`,
      effectiveEndDate: `${year}-${month.toString().padStart(2, '0')}`,
      branchCode: branch.value,
      branchName: branch.label,
      ratingLevel: ['A', 'B', 'C'][i % 3],
      createTime: `${year}-${month.toString().padStart(2, '0')}-01 10:21:22`,
      updateTime: `${year}-${month.toString().padStart(2, '0')}-02 08:11:22`,
      isLatest: true
    })
  }

  return data
}

// 获取分支机构选项
const loadBranchOptions = async () => {
  try {
    const response = await fetch('http://10.242.194.62:7300/dc$org', {
      headers: {
        'Accept-Profile': 'public',
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const data = await response.json()
      branchOptions.value = data.map(item => ({
        value: item.department_id,
        label: item.department_nam
      }))
    } else {
      // 如果API失败，使用模拟数据
      branchOptions.value = mockBranchOptions
    }
  } catch (error) {
    console.error('获取分支机构选项失败:', error)
    // 如果API失败，使用模拟数据
    branchOptions.value = mockBranchOptions
  }
}

// 初始化数据
onMounted(() => {
  // 初始化分支机构选项
  loadBranchOptions()

  // 初始化表格数据，使用查询方法以应用分页逻辑
  handleQuery()
})

// 远程搜索分支机构
const remoteSearchBranch = async (query) => {
  if (query) {
    try {
      const response = await fetch(`http://10.242.194.62:7300/dc$org?or=(department_id.ilike.*${query}*,department_nam.ilike.*${query}*)`, {
        headers: {
          'Accept-Profile': 'public',
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        branchOptions.value = data.map(item => ({
          value: item.department_id,
          label: item.department_nam
        }))
      }
    } catch (error) {
      console.error('搜索分支机构失败:', error)
    }
  } else {
    loadBranchOptions()
  }
}

// 处理分支机构选择变更
const handleBranchChange = (value) => {
  const selectedBranch = branchOptions.value.find(item => item.value === value)
  if (selectedBranch) {
    formData.brch_name = selectedBranch.label
  }
}

// 查询
const handleQuery = async () => {
  loading.value = true

  try {
    // 构建查询参数
    const params = new URLSearchParams()

    // 添加分页参数
    const offset = (currentPage.value - 1) * pageSize.value
    params.append('limit', pageSize.value.toString())
    params.append('offset', offset.toString())

    // 添加排序参数
    params.append('order', 'tect_strt_date.desc,crt_time.desc')

    // 添加查询条件
    if (queryForm.branchCodes && queryForm.branchCodes.length > 0) {
      // 如果有多个分支机构代码，使用in操作符
      const branchCodesStr = queryForm.branchCodes.map(code => `"${code}"`).join(',')
      params.append('brch_cd', `in.(${branchCodesStr})`)
    }

    // 构建完整的URL
    const url = `http://10.242.194.62:7300/brch_clas_rat_set?${params.toString()}`

    // 发送请求
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept-Profile': 'mkt_mang',
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()

    // 获取总数（需要单独请求）
    try {
      const countParams = new URLSearchParams()
      countParams.append('select', 'count')

      // 添加相同的过滤条件
      if (queryForm.branchCodes && queryForm.branchCodes.length > 0) {
        const branchCodesStr = queryForm.branchCodes.map(code => `"${code}"`).join(',')
        countParams.append('brch_cd', `in.(${branchCodesStr})`)
      }

      const countResponse = await fetch(`http://10.242.194.62:7300/brch_clas_rat_set?${countParams.toString()}`, {
        headers: {
          'Accept-Profile': 'mkt_mang',
          'Content-Type': 'application/json',
          'Prefer': 'count=exact'
        }
      })

      if (countResponse.ok) {
        const countData = await countResponse.json()
        totalCount.value = parseInt(countData[0]?.count || 0)
      } else {
        totalCount.value = data.length
      }
    } catch (countError) {
      console.warn('获取总数失败:', countError)
      totalCount.value = data.length
    }

    // 为数据添加分支机构名称
    const dataWithNames = await Promise.all(data.map(async (item) => {
      try {
        // 获取分支机构名称
        const orgResponse = await fetch(`http://10.242.194.62:7300/dc$org?department_id=eq.${item.brch_cd}`, {
          headers: {
            'Accept-Profile': 'public',
            'Content-Type': 'application/json'
          }
        })

        if (orgResponse.ok) {
          const orgData = await orgResponse.json()
          if (orgData.length > 0) {
            item.brch_name = orgData[0].department_nam
          }
        }
      } catch (error) {
        console.warn('获取分支机构名称失败:', error)
      }
      return item
    }))

    tableData.value = dataWithNames

  } catch (error) {
    console.error('查询数据时发生错误:', error)
    ElMessage.error('查询数据失败，请检查网络连接')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 重置查询
const resetQuery = () => {
  queryForm.branchCodes = []
  handleQuery()
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增分支机构分类评级'
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 修改
const handleEdit = (row) => {
  dialogTitle.value = '修改分支机构分类评级'
  isEdit.value = true
  resetForm()

  // 填充表单数据
  Object.assign(formData, row)

  dialogVisible.value = true
}

// 查看历史记录
const handleHistory = (row) => {
  // 模拟获取历史记录
  const mockHistory = [
    { ...row },
    {
      ...row,
      ratingLevel: row.ratingLevel === 'A' ? 'B' : 'A',
      updateTime: '2023-06-01 09:15:30',
      isLatest: false
    },
    {
      ...row,
      ratingLevel: row.ratingLevel === 'C' ? 'A' : 'C',
      updateTime: '2023-05-01 14:22:45',
      isLatest: false
    }
  ]

  historyData.value = mockHistory
  historyDialogVisible.value = true
}

// 导出
const handleExport = async () => {
  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建查询参数，获取所有数据（不分页）
    const params = new URLSearchParams()
    params.append('order', 'tect_strt_date.desc,crt_time.desc')

    // 添加查询条件
    if (queryForm.branchCodes && queryForm.branchCodes.length > 0) {
      const branchCodesStr = queryForm.branchCodes.map(code => `"${code}"`).join(',')
      params.append('brch_cd', `in.(${branchCodesStr})`)
    }

    // 获取所有数据
    const response = await fetch(`http://10.242.194.62:7300/brch_clas_rat_set?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Accept-Profile': 'mkt_mang',
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const allData = await response.json()

    if (allData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 为数据添加分支机构名称
    const dataWithNames = await Promise.all(allData.map(async (item) => {
      try {
        const orgResponse = await fetch(`http://10.242.194.62:7300/dc$org?department_id=eq.${item.brch_cd}`, {
          headers: {
            'Accept-Profile': 'public',
            'Content-Type': 'application/json'
          }
        })

        if (orgResponse.ok) {
          const orgData = await orgResponse.json()
          if (orgData.length > 0) {
            item.brch_name = orgData[0].department_nam
          }
        }
      } catch (error) {
        console.warn('获取分支机构名称失败:', error)
      }
      return item
    }))

    // 准备导出数据
    const exportData = [
      // 表头
      ['生效开始日期', '生效结束日期', '分支机构代码', '分支机构名称', '分类评定等级', '创建时间', '更新时间'],
      // 数据行
      ...dataWithNames.map(item => [
        item.tect_strt_date || '',
        item.tect_end_date || '',
        item.brch_cd || '',
        item.brch_name || '',
        item.clas_rat_grad || '',
        formatDateTime(item.crt_time),
        formatDateTime(item.upd_time)
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 15 }, // 生效开始日期
      { wch: 15 }, // 生效结束日期
      { wch: 15 }, // 分支机构代码
      { wch: 25 }, // 分支机构名称
      { wch: 15 }, // 分类评定等级
      { wch: 20 }, // 创建时间
      { wch: 20 }  // 更新时间
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '分支机构分类评级设置')

    // 生成文件名
    const now = new Date()
    const fileName = `分支机构分类评级设置_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${dataWithNames.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const requestData = {
          i_request: {
            optionflg: isEdit.value ? "2" : "1",
            oacode: urlParams.oacde,
            tect_strt_date: formData.tect_strt_date,
            tect_end_date: formData.tect_end_date,
            brch_cd: formData.brch_cd,
            clas_rat_grad: formData.clas_rat_grad
          }
        }

        // 如果是编辑，需要添加uuid
        if (isEdit.value && formData.uuid) {
          requestData.i_request.uuid = formData.uuid
        }

        const response = await fetch('http://10.242.194.62:7300/rpc/p_brch_clas_rat_set_e', {
          method: 'POST',
          headers: {
            'Content-Profile': 'mkt_mang',
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestData)
        })

        if (response.ok) {
          const result = await response.json()

          if (result.o_status === 0) {
            ElMessage.success(isEdit.value ? '修改成功' : '新增成功')
            dialogVisible.value = false
            handleQuery() // 重新查询数据
          } else {
            ElMessage.error(result.o_msg || '操作失败')
          }
        } else {
          ElMessage.error('操作失败，请检查网络连接')
        }
      } catch (error) {
        console.error('提交数据时发生错误:', error)
        ElMessage.error('操作失败，请检查网络连接')
      }
    }
  })
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确认删除该条记录？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const requestData = {
      i_request: {
        optionflg: "3",
        oacode: urlParams.oacde,
        uuid: row.uuid
      }
    }

    const response = await fetch('http://10.242.194.62:7300/rpc/p_brch_clas_rat_set_e', {
      method: 'POST',
      headers: {
        'Content-Profile': 'mkt_mang',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    })

    if (response.ok) {
      const result = await response.json()

      if (result.o_status === 0) {
        ElMessage.success('删除成功')
        handleQuery() // 重新查询数据
      } else {
        ElMessage.error(result.o_msg || '删除失败')
      }
    } else {
      ElMessage.error('删除失败，请检查网络连接')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除数据时发生错误:', error)
      ElMessage.error('删除失败，请检查网络连接')
    }
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }

  Object.assign(formData, {
    tect_strt_date: '',
    tect_end_date: '',
    brch_cd: '',
    brch_name: '',
    clas_rat_grad: '',
    uuid: ''
  })
}

// 处理对话框关闭
const handleDialogClose = () => {
  resetForm()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}
</script>

<style scoped>
.branch-rating-setting {
  padding: 20px;
  width: 100%;
  min-height: 100vh;
  background-color: var(--ep-bg-color);
  box-sizing: border-box;
  text-align: left;
}

.query-card {
  margin-bottom: 20px;
}

.query-form {
  padding: 10px 0;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  gap: 10px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 自定义按钮颜色 */
.query-btn {
  background-color: #16D585 !important;
  border-color: #16D585 !important;
  color: white !important;
  padding: 8px 16px;
  min-width: auto;
}

.query-btn:hover {
  background-color: #14c578 !important;
  border-color: #14c578 !important;
}

.reset-btn {
  background-color: #8FF6C8 !important;
  border-color: #8FF6C8 !important;
  color: #333 !important;
  padding: 8px 16px;
  min-width: auto;
}

.reset-btn:hover {
  background-color: #7ef5c0 !important;
  border-color: #7ef5c0 !important;
}

.add-btn {
  background-color: #5B7BFB !important;
  border-color: #5B7BFB !important;
  color: white !important;
  padding: 8px 16px;
  min-width: auto;
}

.add-btn:hover {
  background-color: #4a6bfa !important;
  border-color: #4a6bfa !important;
}

.export-btn {
  background-color: #F4CD33 !important;
  border-color: #F4CD33 !important;
  color: #333 !important;
  padding: 8px 16px;
  min-width: auto;
}

.export-btn:hover {
  background-color: #f3c620 !important;
  border-color: #f3c620 !important;
}

.edit-btn {
  background-color: #5B7BFB !important;
  border-color: #5B7BFB !important;
  color: white !important;
}

.edit-btn:hover {
  background-color: #4a6bfa !important;
  border-color: #4a6bfa !important;
}

.history-btn {
  background-color: #16D585 !important;
  border-color: #16D585 !important;
  color: white !important;
}

.history-btn:hover {
  background-color: #14c578 !important;
  border-color: #14c578 !important;
}

.delete-btn {
  background-color: #F05025 !important;
  border-color: #F05025 !important;
  color: white !important;
  padding: 8px 16px;
  min-width: auto;
}

.delete-btn:hover {
  background-color: #e04622 !important;
  border-color: #e04622 !important;
}

.delete-btn {
  background-color: #F05025 !important;
  border-color: #F05025 !important;
  color: white !important;
}

.delete-btn:hover {
  background-color: #ef3f1a !important;
  border-color: #ef3f1a !important;
}
</style>
