$--colors: (
  'primary': (
    'base': #5B7BFB,
  ),
  'success': (
    'base': #16D585,
  ),
  'warning': (
    'base': #F4CD33,
  ),
  'danger': (
    'base': #F05025,
  ),
  'error': (
    'base': #db2828,
  ),
  'info': (
    'base': #42b8dd,
  ),
  'success-light': (
    'base': #8FF6C8,
  ),
);

// we can add this to custom namespace, default is 'el'
@forward 'element-plus/theme-chalk/src/mixins/config.scss' with (
  $namespace: 'ep'
);

// You should use them in scss, because we calculate it by sass.
// comment next lines to use default color
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  // do not use same name, it will override.
  $colors: $--colors,
  $button-padding-horizontal: ('default': 50px)
);

// if you want to import all
// @use "element-plus/theme-chalk/src/index.scss" as *;

// You can comment it to hide debug info.
// @debug $--colors;

// custom dark variables
@use './dark.scss';

// // ========== 自定义按钮样式覆盖 ==========

// .el-button--primary {
//   --el-button-bg-color: #5B7BFB;
//   --el-button-border-color: #5B7BFB;

//   &:hover,
//   &:focus {
//     --el-button-bg-color: #4a6bfa;
//     --el-button-border-color: #4a6bfa;
//   }
// }

// .el-button--success {
//   --el-button-bg-color: #16D585;
//   --el-button-border-color: #16D585;

//   &:hover,
//   &:focus {
//     --el-button-bg-color: #14c578;
//     --el-button-border-color: #14c578;
//   }
// }

// .el-button--danger {
//   --el-button-bg-color: #F05025;
//   --el-button-border-color: #F05025;

//   &:hover,
//   &:focus {
//     --el-button-bg-color: #ef3f1a;
//     --el-button-border-color: #ef3f1a;
//   }
// }

// .el-button--warning {
//   --el-button-bg-color: #F4CD33;
//   --el-button-border-color: #F4CD33;

//   &:hover,
//   &:focus {
//     --el-button-bg-color: #eac32b;
//     --el-button-border-color: #eac32b;
//   }
// }

// .el-button--success-light {
//   @extend .el-button !optional;

//   --el-button-bg-color: #8FF6C8;
//   --el-button-border-color: #8FF6C8;
//   --el-button-hover-bg-color: #7ef5c0;
//   --el-button-hover-border-color: #7ef5c0;
//   --el-button-active-bg-color: #7ef5c0;
//   --el-button-active-border-color: #7ef5c0;
// }

// .el-table {
//   // 滚动条加粗
//   .el-scrollbar__bar.is-horizontal {
//     height: 10px;
//     left: 2px;
//   }

//   .el-scrollbar__bar.is-vertical {
//     top: 2px;
//     width: 10px;
//   }

//   .cell {
//     display: inline-block; // 确保 max-width 生效
//     white-space: nowrap;
//     max-width: 400px;
//     overflow: hidden;
//     text-overflow: ellipsis;
//   }

//   .cell:hover {
//     white-space: pre-wrap; /* 使用pre-wrap来允许换行但保留空白字符 */
//     overflow: visible;
//     text-overflow: clip;
//     word-break: keep-all; /* 尽量保持单词完整，不强制断开 */
//     max-width: 400px; /* 保持最大宽度不变 */
//     width: 100%; /* 确保宽度一致 */
//     word-wrap: break-word; /* 当单词超过容器宽度时允许换行 */
//     display: inline-block; /* 确保元素可以正确处理宽度 */
//   }
//   // 表头样式
//   th .cell {
//     white-space: nowrap !important; // 强制表头内容不换行
//     display: flex;
//     align-items: center; // 垂直居中对齐
//   }
// }

.button-group {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  gap: 10px;
}