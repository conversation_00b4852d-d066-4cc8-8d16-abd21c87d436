<template>
  <div class="etl-task">
    <h2>数据计算任务列表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-width="120px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="任务名称">
              <el-input v-model="queryForm.taskName" placeholder="请输入任务名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="任务类型">
              <el-select v-model="queryForm.taskType" placeholder="请选择任务类型" clearable style="width: 100%">
                <el-option label="类型0" value="0" />
                <el-option label="类型1" value="1" />
                <el-option label="类型2" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态">
              <el-select v-model="queryForm.runsStats" placeholder="请选择状态" clearable style="width: 100%">
                <el-option label="未运行" value="0" />
                <el-option label="运行中" value="1" />
                <el-option label="已完成" value="2" />
                <el-option label="失败" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button class="query-btn" @click="handleQuery">查询</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" border style="width: 100%" v-loading="loading">
        <el-table-column prop="task_id" label="任务编号" width="100" />
        <el-table-column prop="task_name" label="任务名称" min-width="150" />
        <el-table-column prop="task_type" label="任务类型" width="100">
          <template #default="{ row }">
            {{ taskTypeLabel(row.task_type) }}
          </template>
        </el-table-column>
        <el-table-column prop="runs_stats" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="statusTagType(row.runs_stats)">
              {{ statusLabel(row.runs_stats) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="action" label="程序" width="160" />
        <el-table-column prop="params" label="期间" width="160" />
        <el-table-column prop="start_time" label="开始时间" width="160" />
        <el-table-column prop="end_time" label="结束时间" width="160" />
        <el-table-column prop="operator" label="操作人" width="120" />
        <el-table-column prop="update_date" label="更新时间" width="160" />

        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" @click="handleExecute(scope.row)">执行</el-button>
            <el-button size="small" @click="handleViewRecord(scope.row)">查看记录</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 执行确认对话框 -->
    <el-dialog v-model="executeDialogVisible" title="确认执行任务" width="30%">
      <el-form :model="executeForm" label-width="120px">
        <el-form-item label="请选择日期">
          <el-date-picker
            v-model="executeForm.dataDate"
            type="month"
            placeholder="选择日期"
            format="YYYY-MM"
            value-format="YYYY-MM"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="executeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmExecute" :loading="isExecuting.value">确认执行</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import http from '~/http/http.js'
import { useRouter } from 'vue-router'  // 引入 useRouter

const router = useRouter()

// 查询条件
const queryForm = reactive({
  taskName: '',
  taskType: '',
  runsStats: ''
})

// 执行表单数据
const executeForm = reactive({
  dataDate: ''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 当前行数据
const currentRow = ref({})

const isExecuting = ref(false)

// 对话框
const executeDialogVisible = ref(false)

// 初始化数据
onMounted(() => {
  handleQuery()
})

// 查询方法
const handleQuery = () => {
  loading.value = true

  // 构造 PostgREST 兼容的查询参数
  const filters = {}
  if (queryForm.taskName) filters.task_name = `like.*${queryForm.taskName}*`
  if (queryForm.taskType) filters.task_type = `eq.${queryForm.taskType}`
  if (queryForm.runsStats) filters.runs_stats = `eq.${queryForm.runsStats}`

  const offset = (currentPage.value - 1) * pageSize.value
  const limit = pageSize.value

  const config = {
    params: {
      ...filters,
      order: 'update_date.desc',
      //select: 'task_id,task_name,task_type,runs_stats,start_time,end_time,operator,update_date'
    },
    headers: {
      'Accept': 'application/json',
      'Range': `${offset}-${offset + limit - 1}`, // 添加 Range 请求头
      'Prefer': 'count=exact',
      'Accept-Profile': 'mkt_mang'
    }
  }

  http.get('/etl_task_config', {}, config)
    .then(response => {
      tableData.value = response.data || []

      const contentRange = response.headers['Content-range'] || response.headers['content-range']
      totalCount.value = contentRange
        ? parseInt(contentRange.split('/')[1], 10)
        : tableData.value.length
    })
    .catch(error => {
      console.error('API请求失败:', error)
      ElMessage.error('获取数据失败')
    })
    .finally(() => {
      loading.value = false
    })
}

// 重置查询
const resetQuery = () => {
  queryForm.taskName = ''
  queryForm.taskType = ''
  queryForm.runsStats = ''
  handleQuery()
}

// 点击执行按钮
const handleExecute = (row) => {
  currentRow.value = { ...row }
  executeDialogVisible.value = true
}

// 确认执行
const confirmExecute = async () => {
  if (!executeForm.dataDate) {
    ElMessage.warning('请选择执行日期')
    return
  }

  if (isExecuting.value) {
    ElMessage.warning('任务正在执行中，请勿重复操作')
    return
  }


  isExecuting.value = true

  const deptCode = currentRow.value.dept_code
  const dataDate = executeForm.dataDate

  try {
    const response = await http.post('/rpc/p_mkt_cal_main', {
      p_data_date: dataDate,
      p_dept_code: '1021'
    }, {
      headers: {
        'Accept': 'application/json',
        // 'Content-Type': 'application/json',
        'Content-Profile': 'mkt_mang',
        // 'Prefer': 'params=single-object'
      }
    })
    executeDialogVisible.value = false
    // 后端返回结构为 { o_status: number, o_msg: string }
    const { o_status, o_msg } = response.data || {};

    if (o_status === 0) {
      ElMessage.success(`行任务成功`);
      executeDialogVisible.value = false
      handleQuery(); // 刷新列表
    } else {
      ElMessage.error(o_msg || `执行任务失败`);
    }
  } catch (error) {
    executeDialogVisible.value = false
    console.error('执行任务失败:', error)
    ElMessage.error('任务执行失败')
  } finally {
    isExecuting.value = false
  }
}

// 查看记录
const handleViewRecord = (row) => {
  const taskId = row.task_id
  router.push({
    path: '/etl-task-his',  // 对应 etl-task-his.vue 的路由路径
    query: { task_id: taskId }  // 携带 task_id 参数
  })
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

// 标签显示
const statusLabel = (status) => {
  switch (status) {
    case '0': return '未运行'
    case '1': return '运行中'
    case '2': return '已完成'
    case '3': return '失败'
    default: return '未知'
  }
}

const statusTagType = (status) => {
  switch (status) {
    case '0': return ''
    case '1': return 'warning'
    case '2': return 'success'
    case '3': return 'danger'
    default: return ''
  }
}

const taskTypeLabel = (type) => {
  switch (type) {
    case '0': return '类型0'
    case '1': return '类型1'
    case '2': return '类型2'
    default: return '未知'
  }
}
</script>

<style scoped>
.etl-task {
  padding: 20px;
  width: 100%;
  min-height: 100vh;
  background-color: var(--ep-bg-color);
  box-sizing: border-box;
  text-align: left;
}

.query-card {
  margin-bottom: 20px;
}

.query-form {
  padding: 10px 0;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  gap: 10px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.query-btn {
  background-color: #16D585 !important;
  border-color: #16D585 !important;
  color: white !important;
  padding: 8px 16px;
  min-width: auto;
}

.reset-btn {
  background-color: #8FF6C8 !important;
  border-color: #8FF6C8 !important;
  color: #333 !important;
  padding: 8px 16px;
  min-width: auto;
}
</style>