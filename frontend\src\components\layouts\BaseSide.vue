<script setup>
import {
  Document,
  Menu as IconMenu,
  Location,
  Setting,
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// const isCollapse = ref(true)
function handleOpen(key, keyPath) {
  // eslint-disable-next-line no-console
  console.log(key, keyPath)
}
function handleClose(key, keyPath) {
  // eslint-disable-next-line no-console
  console.log(key, keyPath)
}

function handleMenuClick(path) {
  router.push(path)
}
</script>

<template>
  <el-menu
    router
    default-active="1"
    class="el-menu-vertical-demo"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-sub-menu index="1">
      <template #title>
        <el-icon>
          <Location />
        </el-icon>
        <span>分支机构管理</span>
      </template>
      <el-menu-item-group>
        <el-menu-item index="/branch-rating-setting" @click="handleMenuClick('/branch-rating-setting')">
          分类评级设置
        </el-menu-item>
      </el-menu-item-group>
        <el-menu-item index="/financial-data-entry" @click="handleMenuClick('/financial-data-entry')">
          初始财务报表数据录入
        </el-menu-item>
        <el-menu-item index="/basic-data-preparation" @click="handleMenuClick('/basic-data-preparation')">
          基础数据准备情况表
        </el-menu-item>
        <el-menu-item index="/initial-financial-report" @click="handleMenuClick('/initial-financial-report')">
          初始财务报表数据
        </el-menu-item>
        <el-menu-item index="/system-data-audit" @click="handleMenuClick('/system-data-audit')">
          系统采集数据审核
        </el-menu-item>
      <el-menu-item-group title="机构管理">
        <el-menu-item index="1-3">
          机构信息维护
        </el-menu-item>
      </el-menu-item-group>
    </el-sub-menu>
  </el-menu>
</template>
