<script setup>
import {
  Document,
  Menu as IconMenu,
  Location,
  Setting,
} from '@element-plus/icons-vue'

// const isCollapse = ref(true)
function handleOpen(key, keyPath) {
  // eslint-disable-next-line no-console
  console.log(key, keyPath)
}
function handleClose(key, keyPath) {
  // eslint-disable-next-line no-console
  console.log(key, keyPath)
}
</script>

<template>
  <el-menu
    router
    default-active="1"
    class="el-menu-vertical-demo"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-sub-menu index="1">
      <template #title>
        <el-icon>
          <Location />
        </el-icon>
        <span>分支机构管理</span>
      </template>
      <el-menu-item-group>
        <template #title>
          <span>评级管理</span>
        </template>
        <el-menu-item index="/branch-rating-setting">
          分类评级设置
        </el-menu-item>
        <el-menu-item index="1-2">
          评级历史查询
        </el-menu-item>
      </el-menu-item-group>
      <el-menu-item-group title="机构管理">
        <el-menu-item index="1-3">
          机构信息维护
        </el-menu-item>
      </el-menu-item-group>
    </el-sub-menu>
    <el-menu-item index="/nav/2">
      <el-icon>
        <IconMenu />
      </el-icon>
      <template #title>
        数据统计
      </template>
    </el-menu-item>
    <el-menu-item index="3">
      <el-icon>
        <Document />
      </el-icon>
      <template #title>
        报表管理
      </template>
    </el-menu-item>
    <el-menu-item index="/nav/4">
      <el-icon>
        <Setting />
      </el-icon>
      <template #title>
        系统设置
      </template>
    </el-menu-item>
  </el-menu>
</template>
