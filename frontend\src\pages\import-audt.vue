<template>
  <div class="import-data">
    <h2>录入审核列表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-width="140px" label-position="left">
        <el-row :gutter="20"> 
          <el-col :span="8">
            <el-form-item label="数据开始日期">
              <el-date-picker
                v-model="queryForm.startDate"
                type="month"
                placeholder="选择日期"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="数据结束日期">
              <el-date-picker
                v-model="queryForm.endDate"
                type="month"
                placeholder="选择日期"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
          <el-form-item label="审核状态">
            <el-select v-model="queryForm.audtStatus" placeholder="请选择审核状态" clearable style="width: 100%">
              <el-option label="全部" value="" />
              <el-option label="待审核" value="0" />
              <el-option label="审核通过" value="1" />
              <el-option label="审核不通过" value="2" />
            </el-select>
          </el-form-item>
      </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button class="query-btn" @click="handleQuery">查询</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" border style="width: 100%" v-loading="loading">
        <el-table-column prop="import_num" label="数据录入编号" width="150" />
        <el-table-column prop="data_date" label="数据日期" width="120" />   
        <el-table-column prop="report_table" label="报表编号" width="150" />
        <el-table-column prop="report_name" label="报表名称" min-width="180" />
        <el-table-column prop="data_count" label="数据数量" width="100" />
        <el-table-column prop="audt_status" label="审核状态" width="120">
          <template #default="{ row }">
            <el-tag :type="statusTagType(row.audt_status).type">
              {{ statusTagType(row.audt_status).label}}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="import_time" label="录入时间" width="160" />
        <el-table-column prop="audt_time" label="审核时间" width="160" />
        <el-table-column prop="import_person" label="导入人" width="150" />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <el-button class="detail-btn" size="small" @click="handleDetail(scope.row)">查看详情</el-button>
            
            <!-- 只有在状态为 "0"（待审核）时才显示审核按钮 -->
            <el-button
              v-if="scope.row.audt_status === '0'"
              class="pass-btn"
              size="small"
              @click="handleAudit(scope.row, 'pass')"
            >
              审核通过
            </el-button>
            <el-button
              v-if="scope.row.audt_status === '0'"
              class="reject-btn"
              size="small"
              @click="handleAudit(scope.row, 'reject')"
            >
              审核不通过
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="详情信息"
      width="30%"
      @close="closeDetailDialog"
    >
      <el-descriptions class="detail-descriptions" :column="1" border>
        <el-descriptions-item label="导入编号">{{ currentRow.import_num }}</el-descriptions-item>
        <el-descriptions-item label="数据日期">{{ currentRow.data_date }}</el-descriptions-item>
        <el-descriptions-item label="报表表名">{{ currentRow.report_table }}</el-descriptions-item>
        <el-descriptions-item label="报表名称">{{ currentRow.report_name }}</el-descriptions-item>
        <el-descriptions-item label="数据条数">{{ currentRow.data_count }}</el-descriptions-item>
        <el-descriptions-item label="审核状态">
          <el-tag :type="statusTagType(currentRow.audt_status).type">
            {{ statusTagType(currentRow.audt_status).label }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="导入时间">{{ currentRow.import_time }}</el-descriptions-item>
        <el-descriptions-item label="审核时间">{{ currentRow.audt_time || '-' }}</el-descriptions-item>
        <el-descriptions-item label="导入人">{{ currentRow.import_person }}</el-descriptions-item>
      </el-descriptions>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDetailDialog">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog
      v-model="auditDialogVisible"
      :title="dialogTitle"
      width="30%"
      @close="closeAuditDialog"
    >
      <p>确定要将导入编号为 "{{ currentRow.import_num }}" 的记录设置为 "{{ auditActionText }}" 吗？</p>

      <!-- 审核不通过时显示原因输入框 -->
      <el-form label-position="top" v-if="action === 'reject'">
        <el-form-item label="请输入不通过原因">
          <el-input v-model="rejectReason" type="textarea" :rows="4" placeholder="请输入审核不通过的原因" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeAuditDialog">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import http from '~/http/http.js';

// 查询表单
const queryForm = reactive({
  startDate: '',
  endDate: '',
  audtStatus: ''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 当前行数据
const currentRow = ref({})

// 对话框
const detailDialogVisible = ref(false)
const auditDialogVisible = ref(false)
const action = ref('')  // 'detail', 'pass', 'reject'
const rejectReason = ref('')


// 初始化数据
onMounted(() => {
  handleQuery()
})

// 查询
const handleQuery = () => {
  loading.value = true

  // 构造 PostgREST 兼容的查询参数
 const filters = {}
if (queryForm.startDate) filters.data_date = `gte.${queryForm.startDate}`  // 大于等于开始日期
if (queryForm.endDate) filters.data_date = `lte.${queryForm.endDate}`      // 小于等于结束日期
if (queryForm.audtStatus !== '') filters.audt_status = `eq.${queryForm.audtStatus}`  // 精确匹配审核状态

  // 计算 offset 和 limit 实现 PostgREST 分页
  const offset = (currentPage.value - 1) * pageSize.value
  const limit = pageSize.value

  const config = {
    params: {
      ...filters,
      order: 'import_time.desc',  // 按导入时间倒序排列
      // // PostgREST 分页范围
      // rangeUnit: 'items'
      // range: `${offset}-${offset + limit - 1}`
    },
    headers: {
      // 只需设置 Accept 即可，不使用 total_count 字段
      'Accept': 'application/json',
      'Range': `${offset}-${offset + limit - 1}`, // 添加 Range 请求头
      'Prefer': 'count=exact',
      'Accept-Profile': 'mkt_mang'
    }
  }

  http.get('/import_audt_list', {}, config)
    .then(response => {
      tableData.value = response.data || []

      // 从响应头中获取总条数
      const contentRange = response.headers['Content-range']
      totalCount.value = contentRange ? parseInt(contentRange.split('/')[1], 10) : tableData.value.length
    })
    .catch(error => {
      console.error('API请求失败:', error)
      ElMessage.error('获取数据失败')
    })
    .finally(() => {
      loading.value = false
    })
}

// 重置查询
const resetQuery = () => {
  queryForm.startDate = ''
  queryForm.endDate = ''
  queryForm.audtStatus = ''
  handleQuery()
}

// 处理详情点击
const handleDetail = (row) => {
  currentRow.value = { ...row }
  detailDialogVisible.value = true
}

// 处理审核点击
const handleAudit = (row, auditType) => {
  currentRow.value = { ...row }
  action.value = auditType
  auditDialogVisible.value = true
}

// 关闭详情弹窗
const closeDetailDialog = () => {
  detailDialogVisible.value = false
  currentRow.value = {}
}

// 关闭审核弹窗
const closeAuditDialog = () => {
  auditDialogVisible.value = false
  action.value = ''
  currentRow.value = {}
  rejectReason.value = ''
}

// 提交审核
const handleConfirm = async () => {
  try {
    let audtOper = '0';
    if (action.value === 'pass') {
      audtOper = '';
    } else if (action.value === 'reject') {
      audtOper = rejectReason.value;

      if (!rejectReason.value.trim()) {
        ElMessage.warning('请填写审核不通过的原因')
        return;
      }
    }

    const payload = {
      p_audt_table:  currentRow.value.report_table, // 表名
      p_batch_num: currentRow.value.import_num, // 导入编号作为批次号
      p_audt_oper: 'zhouyc1', // 审核人
      p_audt_no_pass_resn: audtOper, // 不通过原因
      p_task_desc: '' // 空字符串即可
    };

    const response = await http.post('/rpc/audt_data', payload, {
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_mang'
      }
    });
    auditDialogVisible.value = false;

    // 后端返回结构为 { o_status: number, o_msg: string }
    const { o_status, o_msg } = response.data || {};

    if (o_status === 0) {
      ElMessage.success(`${auditActionText.value}成功`);
      rejectReason.value = '';
      handleQuery(); // 刷新列表
    } else {
      ElMessage.error(o_msg || `${auditActionText.value}失败`);
    }

  } catch (error) {
    ElMessage.error(`${auditActionText.value}失败`);
  }
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

// 根据审核状态返回标签类型
const statusTagType = (status) => {
  switch (status) {
    case '1':
      return { label: '审核通过', type: 'success' };
    case '2':
      return { label: '审核不通过', type: 'danger' };
    case '0':
      return { label: '待审核', type: 'warning' };
    default:
      return { label: '未知', type: '' };
  }
}

const dialogTitle = computed(() => {
  if (action.value === 'detail') {
    return '详情信息';
  } else {
    return `确认${auditActionText.value}？`;
  }
});

const auditActionText = computed(() => {
  if (action.value === 'pass') {
    return '审核通过';
  } else if (action.value === 'reject') {
    return '审核不通过';
  } else {
    return '';
  }
});
</script>

<style scoped>
.import-data {
  padding: 20px;
  width: 100%;
  min-height: 100vh;
  background-color: var(--ep-bg-color);
  box-sizing: border-box;
  text-align: left;
}

.query-card {
  margin-bottom: 20px;
}

.query-form {
  padding: 10px 0;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  gap: 10px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 自定义按钮颜色 */
.query-btn {
  background-color: #16D585 !important;
  border-color: #16D585 !important;
  color: white !important;
  padding: 8px 16px;
  min-width: auto;
}

.query-btn:hover {
  background-color: #14c578 !important;
  border-color: #14c578 !important;
}

.reset-btn {
  background-color: #8FF6C8 !important;
  border-color: #8FF6C8 !important;
  color: #333 !important;
  padding: 8px 16px;
  min-width: auto;
}

.reset-btn:hover {
  background-color: #7ef5c0 !important;
  border-color: #7ef5c0 !important;
}

.detail-btn {
  background-color: #5B7BFB !important;
  border-color: #5B7BFB !important;
  color: white !important;
}

.detail-btn:hover {
  background-color: #4a6bfa !important;
  border-color: #4a6bfa !important;
}

.pass-btn {
  background-color: #16D585 !important;
  border-color: #16D585 !important;
  color: white !important;
}

.pass-btn:hover {
  background-color: #14c578 !important;
  border-color: #14c578 !important;
}

.reject-btn {
  background-color: #F05025 !important;
  border-color: #F05025 !important;
  color: white !important;
}

.reject-btn:hover {
  background-color: #ef3f1a !important;
  border-color: #ef3f1a !important;
}
</style>
