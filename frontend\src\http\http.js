import axios from 'axios'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

class RequestHttp {
  constructor(config) {
    this.instance = axios.create({
      baseURL: import.meta.env.VITE_API_URL,
      timeout: 5000,
    })

    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器：统一处理 datetime 字段格式
    this.instance.interceptors.response.use(
      (response) => {
        // 如果是 blob 类型，直接返回
        if (response.config.responseType === 'blob') {
          return response
        }

        // ✅ 统一格式化 datetime 字段
        const formatDateTime = (data) => {
          if (Array.isArray(data)) {
            return data.map(formatDateTime)
          } else if (data && typeof data === 'object') {
            return Object.keys(data).reduce((acc, key) => {
              const value = data[key]

              // 匹配常见日期时间字段名
              if (
                key.toLowerCase().includes('time') ||
                key.toLowerCase().includes('date')
              ) {
                acc[key] = value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : ''
              } else if (Array.isArray(value) || (typeof value === 'object' && value !== null)) {
                acc[key] = formatDateTime(value)
              } else {
                acc[key] = value
              }

              return acc
            }, {})
          }
          return data
        }

        // 对整个响应数据进行格式化
        const formattedData = formatDateTime(response.data)

        // 返回新的 response 对象，保留 headers 等信息
        return {
          ...response,
          data: formattedData
        }
      },
      async (error) => {
        const { response } = error
        if (error.message.indexOf('timeout') !== -1) {
          ElMessage.error('请求超时！请您稍后重试')
        }
        if (error.message.indexOf('Network Error') !== -1) {
          ElMessage.error('网络错误！请您稍后重试')
        }
        return Promise.reject(error)
      }
    )
  }

  /**
   * @description 常用请求方法封装
   */
  get(url, params = {}, config) {
    return this.instance.get(url, { params, ...config }).then(response => response)
  }

  post(url, params, config) {
    return this.instance.post(url, params, config).then(response => response)
  }

  put(url, params, config) {
    return this.instance.put(url, params, config).then(response => response)
  }

  delete(url, data, config) {
    return this.instance.delete(url, { data, ...config }).then(response => response)
  }

  download(url, params = {}, config) {
    return this.instance.post(url, params, { ...config, responseType: 'blob' }).then(response => response)
  }

  template(url, params = {}, config) {
    return this.instance.get(url, { params, ...config, responseType: 'blob' }).then(response => response)
  }

  upload(url, params, config) {
    return this.instance.post(url, params, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }).then(response => response)
  }
}

export default new RequestHttp()