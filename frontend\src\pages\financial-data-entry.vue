<template>
  <div class="financial-data-entry">
    <h2>初始财务报表数据录入</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-width="100px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="开始日期">
              <el-date-picker
                v-model="queryForm.startDate"
                type="month"
                placeholder="选择开始月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="结束日期">
              <el-date-picker
                v-model="queryForm.endDate"
                type="month"
                placeholder="选择结束月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button class="template-btn" @click="handleDownloadTemplate">下载模板</el-button>
              <el-button class="import-btn" @click="handleImportData">数据导入</el-button>
              <el-button class="query-btn" @click="handleQuery">查询</el-button>
              <el-button class="export-btn" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" border style="width: 100%" v-loading="loading" size="small">
        <el-table-column prop="dataDate" label="数据日期" width="100" fixed="left" />
        <el-table-column prop="reportType" label="报表" width="100" fixed="left" />
        <el-table-column prop="indicator" label="指标" width="150" fixed="left" />
        <el-table-column prop="branchTotal" label="分支机构合计" width="120" />
        <el-table-column prop="hongshan" label="洪山营业部" width="120" />
        <el-table-column prop="jianghan" label="江汉营业部" width="120" />
        <el-table-column prop="huangshi" label="黄石营业部" width="120" />
        <el-table-column prop="qiaokou" label="硚口营业部" width="120" />
        <el-table-column prop="shanghai" label="上海分公司" width="120" />
        <el-table-column prop="shanghaiMarket4" label="上海分公司市场四部" width="160" />
        <el-table-column prop="beijingJianguomen" label="北京建国门营业部" width="150" />
        <el-table-column prop="guangzhou" label="广州分公司" width="120" />
        <el-table-column prop="chengdu" label="成都营业部" width="120" />
        <el-table-column prop="shanghaiBeijingxi" label="上海北京西路" width="130" />
        <el-table-column prop="wuchang" label="武昌营业部" width="120" />
        <el-table-column prop="changsha" label="长沙营业部" width="120" />
        <el-table-column prop="fuzhou" label="福州营业部" width="120" />
        <el-table-column prop="shenzhen" label="深圳分公司" width="120" />
        <el-table-column prop="beijingHaidian" label="北京海淀区营业部" width="150" />
        <el-table-column prop="taiyuan" label="太原营业部" width="120" />
        <el-table-column prop="hangzhou" label="杭州营业部" width="120" />
        <el-table-column prop="shanghaiZhangyang" label="上海张杨路营业部" width="150" />
        <el-table-column prop="zhongyuan" label="中原分公司" width="120" />
        <el-table-column prop="wuhan" label="武汉分公司" width="120" />
        <el-table-column prop="dalian" label="大连营业部" width="120" />
        <el-table-column prop="yanchang" label="延长营业部" width="120" />
        <el-table-column prop="creator" label="创建人" width="100" />
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column prop="auditor" label="审核人" width="100" />
        <el-table-column prop="auditTime" label="审核时间" width="160" />
        <el-table-column prop="auditResult" label="审核结果" width="100" />
        <el-table-column prop="auditReason" label="审核不通过原因" width="150" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 数据导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="数据导入"
      width="500px"
      @close="handleImportDialogClose"
    >
      <div class="import-content">
        <el-form label-width="100px">
          <el-form-item label="数据日期" required>
            <el-date-picker
              v-model="importForm.dataDate"
              type="month"
              placeholder="选择数据月份"
              format="YYYY-MM"
              value-format="YYYY-MM"
              style="width: 100%"
            />
          </el-form-item>
        </el-form>

        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :auto-upload="false"
          :limit="1"
          accept=".xlsx,.xls"
          :on-change="handleFileChange"
          :file-list="fileList"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 xlsx/xls 文件，且不超过 10MB
            </div>
          </template>
        </el-upload>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button class="import-confirm-btn" @click="handleImportConfirm" :loading="importLoading">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'

// 查询表单
const queryForm = reactive({
  startDate: '',
  endDate: ''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 导入对话框
const importDialogVisible = ref(false)
const uploadRef = ref()
const fileList = ref([])
const importLoading = ref(false)

// 导入表单
const importForm = reactive({
  dataDate: ''
})

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacde: params.get('oacde') || 'current_user',
    roleid: params.get('roleid') || '001'
  }
}

const urlParams = getUrlParams()

// 生成模拟数据
const generateMockData = () => {
  const data = []
  const reportTypes = ['利润表', '资产负债表', '现金流量表']
  const indicators = [
    '一、收入',
    '1、经纪业务收入',
    '其中：（1）IB业务',
    '（2）代理买卖证券业务',
    '2、投资银行业务收入',
    '3、资产管理业务收入',
    '4、投资收益',
    '二、支出',
    '1、营业费用',
    '2、管理费用',
    '3、财务费用',
    '三、利润总额',
    '四、净利润',
    '五、资产总计',
    '1、流动资产',
    '2、非流动资产',
    '六、负债总计',
    '1、流动负债',
    '2、非流动负债',
    '七、所有者权益'
  ]
  const creators = ['xurr1', 'zhouyc1', 'test1', 'admin', 'user1']
  const auditors = ['test1', 'test2', 'xurr1', 'admin', 'auditor1']
  const auditResults = ['通过', '不通过', '待审核']

  for (let i = 0; i < 20; i++) {
    const month = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')
    const year = 2024 + Math.floor(i / 12)
    const baseValue = 100 + i
    const auditResult = auditResults[i % 3]

    data.push({
      id: i + 1,
      dataDate: `${year}-${month}`,
      reportType: reportTypes[i % reportTypes.length],
      indicator: indicators[i % indicators.length],
      branchTotal: baseValue,
      hongshan: baseValue + 1,
      jianghan: baseValue + 2,
      huangshi: baseValue + 3,
      qiaokou: baseValue + 4,
      shanghai: baseValue + 5,
      shanghaiMarket4: baseValue + 6,
      beijingJianguomen: baseValue + 7,
      guangzhou: baseValue + 8,
      chengdu: baseValue + 9,
      shanghaiBeijingxi: baseValue + 10,
      wuchang: baseValue + 11,
      changsha: baseValue + 12,
      fuzhou: baseValue + 13,
      shenzhen: baseValue + 14,
      beijingHaidian: baseValue + 15,
      taiyuan: baseValue + 16,
      hangzhou: baseValue + 17,
      shanghaiZhangyang: baseValue + 18,
      zhongyuan: baseValue + 19,
      wuhan: baseValue + 20,
      dalian: baseValue + 21,
      yanchang: baseValue + 22,
      creator: creators[i % creators.length],
      createTime: `2024-${month}-01 10:21:${String(22 + i).padStart(2, '0')}`,
      auditor: auditors[i % auditors.length],
      auditTime: auditResult === '待审核' ? '' : `2024-${month}-02 10:21:${String(22 + i).padStart(2, '0')}`,
      auditResult: auditResult,
      auditReason: auditResult === '不通过' ? '数据异常，请核实' : ''
    })
  }

  return data
}

// 初始化数据
onMounted(() => {
  handleQuery()
})

// 查询
const handleQuery = () => {
  loading.value = true

  // 模拟API调用
  setTimeout(() => {
    let allData = generateMockData()

    // 根据日期范围过滤数据
    if (queryForm.startDate || queryForm.endDate) {
      allData = allData.filter(item => {
        const itemDate = item.dataDate
        let matchStart = true
        let matchEnd = true

        if (queryForm.startDate) {
          matchStart = itemDate >= queryForm.startDate
        }
        if (queryForm.endDate) {
          matchEnd = itemDate <= queryForm.endDate
        }

        return matchStart && matchEnd
      })
    }

    // 设置总数
    totalCount.value = allData.length

    // 计算分页数据
    const startIndex = (currentPage.value - 1) * pageSize.value
    const endIndex = startIndex + pageSize.value
    tableData.value = allData.slice(startIndex, endIndex)

    loading.value = false
  }, 500)
}

// 下载模板
const handleDownloadTemplate = () => {
  // 创建模板数据
  const templateHeaders = [
    '报表', '指标', '分支机构合计', '洪山营业部', '江汉营业部', '黄石营业部', '硚口营业部',
    '上海分公司', '上海分公司市场四部', '北京建国门营业部', '广州分公司', '成都营业部',
    '上海北京西路', '武昌营业部', '长沙营业部', '福州营业部', '深圳分公司', '北京海淀区营业部',
    '太原营业部', '杭州营业部', '上海张杨路营业部', '中原分公司', '武汉分公司', '大连营业部', '延长营业部'
  ]

  // 创建示例数据
  const templateData = [
    templateHeaders,
    ['利润表', '一、收入', 1000, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121],
    ['利润表', '1、经纪业务收入', 800, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101]
  ]

  // 创建工作簿
  const wb = XLSX.utils.book_new()
  const ws = XLSX.utils.aoa_to_sheet(templateData)

  // 设置列宽
  const colWidths = templateHeaders.map(() => ({ wch: 15 }))
  ws['!cols'] = colWidths

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, '财务报表数据模板')

  // 下载文件
  XLSX.writeFile(wb, '财务报表数据导入模板.xlsx')

  ElMessage.success('模板下载成功')
}

// 数据导入
const handleImportData = () => {
  importDialogVisible.value = true
  fileList.value = []
  importForm.dataDate = ''
}

// 文件选择变化
const handleFileChange = (file) => {
  fileList.value = [file]
}

// 导入确认
const handleImportConfirm = async () => {
  // 验证数据日期
  if (!importForm.dataDate) {
    ElMessage.warning('请选择数据日期')
    return
  }

  // 验证文件
  if (fileList.value.length === 0) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  importLoading.value = true

  try {
    // 读取Excel文件
    const file = fileList.value[0].raw
    const arrayBuffer = await file.arrayBuffer()
    const workbook = XLSX.read(arrayBuffer, { type: 'array' })

    // 获取第一个工作表
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]

    // 转换为JSON数据
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

    if (jsonData.length < 2) {
      ElMessage.error('Excel文件数据格式不正确')
      return
    }

    // 获取表头和数据
    const headers = jsonData[0]
    const dataRows = jsonData.slice(1)

    // 验证表头格式
    const expectedHeaders = [
      '报表', '指标', '分支机构合计', '洪山营业部', '江汉营业部', '黄石营业部', '硚口营业部',
      '上海分公司', '上海分公司市场四部', '北京建国门营业部', '广州分公司', '成都营业部',
      '上海北京西路', '武昌营业部', '长沙营业部', '福州营业部', '深圳分公司', '北京海淀区营业部',
      '太原营业部', '杭州营业部', '上海张杨路营业部', '中原分公司', '武汉分公司', '大连营业部', '延长营业部'
    ]

    // 检查表头是否匹配
    const headerMatches = expectedHeaders.every((header, index) => headers[index] === header)
    if (!headerMatches) {
      ElMessage.error('Excel文件表头格式不正确，请使用标准模板')
      return
    }

    // 生成批次号（时间戳）
    const batchNum = Date.now().toString()
    const currentTime = new Date().toISOString()
    const creator = urlParams.oacde // 从URL参数获取创建人

    // 转换数据格式
    const importData = dataRows.map(row => ({
      batch_num: batchNum,
      data_date: importForm.dataDate,
      rptl: row[0] || '',
      index: row[1] || '',
      brh_sum: parseFloat(row[2]) || 0,
      brh_hs: parseFloat(row[3]) || 0,
      brh_jh: parseFloat(row[4]) || 0,
      brh_hshi: parseFloat(row[5]) || 0,
      brh_qk: parseFloat(row[6]) || 0,
      brof_sh: parseFloat(row[7]) || 0,
      brof_sh_mkt4: parseFloat(row[8]) || 0,
      brh_bj: parseFloat(row[9]) || 0,
      brof_gz: parseFloat(row[10]) || 0,
      brh_cd: parseFloat(row[11]) || 0,
      brh_shbjxl: parseFloat(row[12]) || 0,
      brh_wc: parseFloat(row[13]) || 0,
      brh_cs: parseFloat(row[14]) || 0,
      brh_fz: parseFloat(row[15]) || 0,
      brof_sz: parseFloat(row[16]) || 0,
      brh_bjhdq: parseFloat(row[17]) || 0,
      brh_ty: parseFloat(row[18]) || 0,
      brh_hz: parseFloat(row[19]) || 0,
      brh_shzyl: parseFloat(row[20]) || 0,
      brof_zy: parseFloat(row[21]) || 0,
      brof_wh: parseFloat(row[22]) || 0,
      brh_dl: parseFloat(row[23]) || 0,
      brh_yc: parseFloat(row[24]) || 0,
      creator: creator,
      crt_time: currentTime
    }))

    // 调用API导入数据
    const response = await fetch('http://10.242.194.62:7300/temp_init_fin_rptl', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept-Profile': 'mkt_base'
      },
      body: JSON.stringify(importData)
    })

    if (response.ok) {
      ElMessage.success(`数据导入成功，共导入 ${importData.length} 条记录`)
      importDialogVisible.value = false
      handleQuery() // 重新查询数据
    } else {
      const errorText = await response.text()
      ElMessage.error(`数据导入失败: ${errorText}`)
    }

  } catch (error) {
    console.error('导入数据时发生错误:', error)
    ElMessage.error('数据导入失败，请检查文件格式')
  } finally {
    importLoading.value = false
  }
}

// 导入对话框关闭
const handleImportDialogClose = () => {
  fileList.value = []
  importForm.dataDate = ''
  importLoading.value = false
}

// 导出
const handleExport = () => {
  ElMessage.success('数据导出成功')
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}
</script>

<style scoped>
.financial-data-entry {
  padding: 20px;
  width: 100%;
  min-height: 100vh;
  background-color: var(--ep-bg-color);
  box-sizing: border-box;
  text-align: left;
}

.query-card {
  margin-bottom: 20px;
}

.query-form {
  padding: 10px 0;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  gap: 10px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.import-content {
  padding: 20px 0;
}

.import-content .el-form {
  margin-bottom: 20px;
}

/* 自定义按钮颜色 */
.template-btn {
  background-color: #8B5CF6 !important;
  border-color: #8B5CF6 !important;
  color: white !important;
  padding: 8px 16px;
  min-width: auto;
}

.template-btn:hover {
  background-color: #7C3AED !important;
  border-color: #7C3AED !important;
}

.import-btn {
  background-color: #F59E0B !important;
  border-color: #F59E0B !important;
  color: white !important;
  padding: 8px 16px;
  min-width: auto;
}

.import-btn:hover {
  background-color: #D97706 !important;
  border-color: #D97706 !important;
}

.query-btn {
  background-color: #16D585 !important;
  border-color: #16D585 !important;
  color: white !important;
  padding: 8px 16px;
  min-width: auto;
}

.query-btn:hover {
  background-color: #14c578 !important;
  border-color: #14c578 !important;
}

.export-btn {
  background-color: #F4CD33 !important;
  border-color: #F4CD33 !important;
  color: #333 !important;
  padding: 8px 16px;
  min-width: auto;
}

.export-btn:hover {
  background-color: #f3c620 !important;
  border-color: #f3c620 !important;
}

.import-confirm-btn {
  background-color: #5B7BFB !important;
  border-color: #5B7BFB !important;
  color: white !important;
}

.import-confirm-btn:hover {
  background-color: #4a6bfa !important;
  border-color: #4a6bfa !important;
}

/* 表格样式优化 */
.el-table {
  font-size: 12px;
}

.el-table th {
  background-color: #f5f7fa;
  font-weight: bold;
}

.el-table td {
  padding: 8px 0;
}

/* 上传组件样式 */
.upload-demo {
  width: 100%;
}

.el-upload-dragger {
  width: 100%;
  height: 180px;
}
</style>
