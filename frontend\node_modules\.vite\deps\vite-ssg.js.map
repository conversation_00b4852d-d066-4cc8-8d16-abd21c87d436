{"version": 3, "sources": ["../../@unhead/shared/dist/index.mjs", "../../@unhead/dom/dist/index.mjs", "../../hookable/dist/index.mjs", "../../unhead/dist/index.mjs", "../../@unhead/vue/dist/shared/vue.ziyDaVMR.mjs", "../../@unhead/vue/dist/index.mjs", "../../@vue/devtools-api/lib/esm/env.js", "../../@vue/devtools-api/lib/esm/const.js", "../../@vue/devtools-api/lib/esm/time.js", "../../@vue/devtools-api/lib/esm/proxy.js", "../../@vue/devtools-api/lib/esm/index.js", "../../vue-router/dist/vue-router.mjs", "../../vite-ssg/dist/shared/vite-ssg.ETIvV-80.mjs", "../../vite-ssg/dist/shared/vite-ssg.C6pK7rvr.mjs", "../../vite-ssg/dist/index.mjs"], "sourcesContent": ["import { unpackToString, unpackToArray, packArray } from 'packrup';\n\nfunction asArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\n\nconst SelfClosingTags = /* @__PURE__ */ new Set([\"meta\", \"link\", \"base\"]);\nconst TagsWithInnerContent = /* @__PURE__ */ new Set([\"title\", \"titleTemplate\", \"script\", \"style\", \"noscript\"]);\nconst HasElementTags = /* @__PURE__ */ new Set([\n  \"base\",\n  \"meta\",\n  \"link\",\n  \"style\",\n  \"script\",\n  \"noscript\"\n]);\nconst ValidHeadTags = /* @__PURE__ */ new Set([\n  \"title\",\n  \"titleTemplate\",\n  \"templateParams\",\n  \"base\",\n  \"htmlAttrs\",\n  \"bodyAttrs\",\n  \"meta\",\n  \"link\",\n  \"style\",\n  \"script\",\n  \"noscript\"\n]);\nconst UniqueTags = /* @__PURE__ */ new Set([\"base\", \"title\", \"titleTemplate\", \"bodyAttrs\", \"htmlAttrs\", \"templateParams\"]);\nconst TagConfigKeys = /* @__PURE__ */ new Set([\"tagPosition\", \"tagPriority\", \"tagDuplicateStrategy\", \"children\", \"innerHTML\", \"textContent\", \"processTemplateParams\"]);\nconst IsBrowser = typeof window !== \"undefined\";\nconst composableNames = [\n  \"getActiveHead\",\n  \"useHead\",\n  \"useSeoMeta\",\n  \"useHeadSafe\",\n  \"useServerHead\",\n  \"useServerSeoMeta\",\n  \"useServerHeadSafe\"\n];\n\nfunction defineHeadPlugin(plugin) {\n  return plugin;\n}\n\nfunction hashCode(s) {\n  let h = 9;\n  for (let i = 0; i < s.length; )\n    h = Math.imul(h ^ s.charCodeAt(i++), 9 ** 9);\n  return ((h ^ h >>> 9) + 65536).toString(16).substring(1, 8).toLowerCase();\n}\nfunction hashTag(tag) {\n  if (tag._h) {\n    return tag._h;\n  }\n  if (tag._d) {\n    return hashCode(tag._d);\n  }\n  let content = `${tag.tag}:${tag.textContent || tag.innerHTML || \"\"}:`;\n  for (const key in tag.props) {\n    content += `${key}:${String(tag.props[key])},`;\n  }\n  return hashCode(content);\n}\n\nconst p = (p2) => ({ keyValue: p2, metaKey: \"property\" });\nconst k = (p2) => ({ keyValue: p2 });\nconst MetaPackingSchema = {\n  appleItunesApp: {\n    unpack: {\n      entrySeparator: \", \",\n      resolve({ key, value }) {\n        return `${fixKeyCase(key)}=${value}`;\n      }\n    }\n  },\n  articleExpirationTime: p(\"article:expiration_time\"),\n  articleModifiedTime: p(\"article:modified_time\"),\n  articlePublishedTime: p(\"article:published_time\"),\n  bookReleaseDate: p(\"book:release_date\"),\n  charset: {\n    metaKey: \"charset\"\n  },\n  contentSecurityPolicy: {\n    unpack: {\n      entrySeparator: \"; \",\n      resolve({ key, value }) {\n        return `${fixKeyCase(key)} ${value}`;\n      }\n    },\n    metaKey: \"http-equiv\"\n  },\n  contentType: {\n    metaKey: \"http-equiv\"\n  },\n  defaultStyle: {\n    metaKey: \"http-equiv\"\n  },\n  fbAppId: p(\"fb:app_id\"),\n  msapplicationConfig: k(\"msapplication-Config\"),\n  msapplicationTileColor: k(\"msapplication-TileColor\"),\n  msapplicationTileImage: k(\"msapplication-TileImage\"),\n  ogAudioSecureUrl: p(\"og:audio:secure_url\"),\n  ogAudioUrl: p(\"og:audio\"),\n  ogImageSecureUrl: p(\"og:image:secure_url\"),\n  ogImageUrl: p(\"og:image\"),\n  ogSiteName: p(\"og:site_name\"),\n  ogVideoSecureUrl: p(\"og:video:secure_url\"),\n  ogVideoUrl: p(\"og:video\"),\n  profileFirstName: p(\"profile:first_name\"),\n  profileLastName: p(\"profile:last_name\"),\n  profileUsername: p(\"profile:username\"),\n  refresh: {\n    metaKey: \"http-equiv\",\n    unpack: {\n      entrySeparator: \";\",\n      resolve({ key, value }) {\n        if (key === \"seconds\")\n          return `${value}`;\n      }\n    }\n  },\n  robots: {\n    unpack: {\n      entrySeparator: \", \",\n      resolve({ key, value }) {\n        if (typeof value === \"boolean\")\n          return `${fixKeyCase(key)}`;\n        else\n          return `${fixKeyCase(key)}:${value}`;\n      }\n    }\n  },\n  xUaCompatible: {\n    metaKey: \"http-equiv\"\n  }\n};\nconst openGraphNamespaces = /* @__PURE__ */ new Set([\n  \"og\",\n  \"book\",\n  \"article\",\n  \"profile\"\n]);\nfunction resolveMetaKeyType(key) {\n  const fKey = fixKeyCase(key);\n  const prefixIndex = fKey.indexOf(\":\");\n  if (openGraphNamespaces.has(fKey.substring(0, prefixIndex)))\n    return \"property\";\n  return MetaPackingSchema[key]?.metaKey || \"name\";\n}\nfunction resolveMetaKeyValue(key) {\n  return MetaPackingSchema[key]?.keyValue || fixKeyCase(key);\n}\nfunction fixKeyCase(key) {\n  const updated = key.replace(/([A-Z])/g, \"-$1\").toLowerCase();\n  const prefixIndex = updated.indexOf(\"-\");\n  const fKey = updated.substring(0, prefixIndex);\n  if (fKey === \"twitter\" || openGraphNamespaces.has(fKey))\n    return key.replace(/([A-Z])/g, \":$1\").toLowerCase();\n  return updated;\n}\nfunction changeKeyCasingDeep(input) {\n  if (Array.isArray(input)) {\n    return input.map((entry) => changeKeyCasingDeep(entry));\n  }\n  if (typeof input !== \"object\" || Array.isArray(input))\n    return input;\n  const output = {};\n  for (const key in input) {\n    if (!Object.prototype.hasOwnProperty.call(input, key)) {\n      continue;\n    }\n    output[fixKeyCase(key)] = changeKeyCasingDeep(input[key]);\n  }\n  return output;\n}\nfunction resolvePackedMetaObjectValue(value, key) {\n  const definition = MetaPackingSchema[key];\n  if (key === \"refresh\")\n    return `${value.seconds};url=${value.url}`;\n  return unpackToString(\n    changeKeyCasingDeep(value),\n    {\n      keyValueSeparator: \"=\",\n      entrySeparator: \", \",\n      resolve({ value: value2, key: key2 }) {\n        if (value2 === null)\n          return \"\";\n        if (typeof value2 === \"boolean\")\n          return `${key2}`;\n      },\n      ...definition?.unpack\n    }\n  );\n}\nconst ObjectArrayEntries = /* @__PURE__ */ new Set([\"og:image\", \"og:video\", \"og:audio\", \"twitter:image\"]);\nfunction sanitize(input) {\n  const out = {};\n  for (const k2 in input) {\n    if (!Object.prototype.hasOwnProperty.call(input, k2)) {\n      continue;\n    }\n    const v = input[k2];\n    if (String(v) !== \"false\" && k2)\n      out[k2] = v;\n  }\n  return out;\n}\nfunction handleObjectEntry(key, v) {\n  const value = sanitize(v);\n  const fKey = fixKeyCase(key);\n  const attr = resolveMetaKeyType(fKey);\n  if (ObjectArrayEntries.has(fKey)) {\n    const input = {};\n    for (const k2 in value) {\n      if (!Object.prototype.hasOwnProperty.call(value, k2)) {\n        continue;\n      }\n      input[`${key}${k2 === \"url\" ? \"\" : `${k2[0].toUpperCase()}${k2.slice(1)}`}`] = value[k2];\n    }\n    return unpackMeta(input).sort((a, b) => (a[attr]?.length || 0) - (b[attr]?.length || 0));\n  }\n  return [{ [attr]: fKey, ...value }];\n}\nfunction unpackMeta(input) {\n  const extras = [];\n  const primitives = {};\n  for (const key in input) {\n    if (!Object.prototype.hasOwnProperty.call(input, key)) {\n      continue;\n    }\n    const value = input[key];\n    if (!Array.isArray(value)) {\n      if (typeof value === \"object\" && value) {\n        if (ObjectArrayEntries.has(fixKeyCase(key))) {\n          extras.push(...handleObjectEntry(key, value));\n          continue;\n        }\n        primitives[key] = sanitize(value);\n      } else {\n        primitives[key] = value;\n      }\n      continue;\n    }\n    for (const v of value) {\n      extras.push(...typeof v === \"string\" ? unpackMeta({ [key]: v }) : handleObjectEntry(key, v));\n    }\n  }\n  const meta = unpackToArray(primitives, {\n    key({ key }) {\n      return resolveMetaKeyType(key);\n    },\n    value({ key }) {\n      return key === \"charset\" ? \"charset\" : \"content\";\n    },\n    resolveKeyData({ key }) {\n      return resolveMetaKeyValue(key);\n    },\n    resolveValueData({ value, key }) {\n      if (value === null)\n        return \"_null\";\n      if (typeof value === \"object\")\n        return resolvePackedMetaObjectValue(value, key);\n      return typeof value === \"number\" ? value.toString() : value;\n    }\n  });\n  return [...extras, ...meta].map((m) => {\n    if (m.content === \"_null\")\n      m.content = null;\n    return m;\n  });\n}\nfunction packMeta(inputs) {\n  const mappedPackingSchema = Object.entries(MetaPackingSchema).map(([key, value]) => [key, value.keyValue]);\n  return packArray(inputs, {\n    key: [\"name\", \"property\", \"httpEquiv\", \"http-equiv\", \"charset\"],\n    value: [\"content\", \"charset\"],\n    resolveKey(k2) {\n      let key = mappedPackingSchema.filter((sk) => sk[1] === k2)?.[0]?.[0] || k2;\n      const replacer = (_, letter) => letter?.toUpperCase();\n      key = key.replace(/:([a-z])/g, replacer).replace(/-([a-z])/g, replacer);\n      return key;\n    }\n  });\n}\n\nfunction thenable(val, thenFn) {\n  if (val instanceof Promise) {\n    return val.then(thenFn);\n  }\n  return thenFn(val);\n}\n\nfunction normaliseTag(tagName, input, e, normalizedProps) {\n  const props = normalizedProps || normaliseProps(\n    // explicitly check for an object\n    // @ts-expect-error untyped\n    typeof input === \"object\" && typeof input !== \"function\" && !(input instanceof Promise) ? { ...input } : { [tagName === \"script\" || tagName === \"noscript\" || tagName === \"style\" ? \"innerHTML\" : \"textContent\"]: input },\n    tagName === \"templateParams\" || tagName === \"titleTemplate\"\n  );\n  if (props instanceof Promise) {\n    return props.then((val) => normaliseTag(tagName, input, e, val));\n  }\n  const tag = {\n    tag: tagName,\n    props\n  };\n  for (const k of TagConfigKeys) {\n    const val = tag.props[k] !== void 0 ? tag.props[k] : e[k];\n    if (val !== void 0) {\n      if (!(k === \"innerHTML\" || k === \"textContent\" || k === \"children\") || TagsWithInnerContent.has(tag.tag)) {\n        tag[k === \"children\" ? \"innerHTML\" : k] = val;\n      }\n      delete tag.props[k];\n    }\n  }\n  if (tag.props.body) {\n    tag.tagPosition = \"bodyClose\";\n    delete tag.props.body;\n  }\n  if (tag.tag === \"script\") {\n    if (typeof tag.innerHTML === \"object\") {\n      tag.innerHTML = JSON.stringify(tag.innerHTML);\n      tag.props.type = tag.props.type || \"application/json\";\n    }\n  }\n  return Array.isArray(tag.props.content) ? tag.props.content.map((v) => ({ ...tag, props: { ...tag.props, content: v } })) : tag;\n}\nfunction normaliseStyleClassProps(key, v) {\n  const sep = key === \"class\" ? \" \" : \";\";\n  if (v && typeof v === \"object\" && !Array.isArray(v)) {\n    v = Object.entries(v).filter(([, v2]) => v2).map(([k, v2]) => key === \"style\" ? `${k}:${v2}` : k);\n  }\n  return String(Array.isArray(v) ? v.join(sep) : v)?.split(sep).filter((c) => Boolean(c.trim())).join(sep);\n}\nfunction nestedNormaliseProps(props, virtual, keys, startIndex) {\n  for (let i = startIndex; i < keys.length; i += 1) {\n    const k = keys[i];\n    if (k === \"class\" || k === \"style\") {\n      props[k] = normaliseStyleClassProps(k, props[k]);\n      continue;\n    }\n    if (props[k] instanceof Promise) {\n      return props[k].then((val) => {\n        props[k] = val;\n        return nestedNormaliseProps(props, virtual, keys, i);\n      });\n    }\n    if (!virtual && !TagConfigKeys.has(k)) {\n      const v = String(props[k]);\n      const isDataKey = k.startsWith(\"data-\");\n      if (v === \"true\" || v === \"\") {\n        props[k] = isDataKey ? \"true\" : true;\n      } else if (!props[k]) {\n        if (isDataKey && v === \"false\")\n          props[k] = \"false\";\n        else\n          delete props[k];\n      }\n    }\n  }\n}\nfunction normaliseProps(props, virtual = false) {\n  const resolvedProps = nestedNormaliseProps(props, virtual, Object.keys(props), 0);\n  if (resolvedProps instanceof Promise) {\n    return resolvedProps.then(() => props);\n  }\n  return props;\n}\nconst TagEntityBits = 10;\nfunction nestedNormaliseEntryTags(headTags, tagPromises, startIndex) {\n  for (let i = startIndex; i < tagPromises.length; i += 1) {\n    const tags = tagPromises[i];\n    if (tags instanceof Promise) {\n      return tags.then((val) => {\n        tagPromises[i] = val;\n        return nestedNormaliseEntryTags(headTags, tagPromises, i);\n      });\n    }\n    if (Array.isArray(tags)) {\n      headTags.push(...tags);\n    } else {\n      headTags.push(tags);\n    }\n  }\n}\nfunction normaliseEntryTags(e) {\n  const tagPromises = [];\n  const input = e.resolvedInput;\n  for (const k in input) {\n    if (!Object.prototype.hasOwnProperty.call(input, k)) {\n      continue;\n    }\n    const v = input[k];\n    if (v === void 0 || !ValidHeadTags.has(k)) {\n      continue;\n    }\n    if (Array.isArray(v)) {\n      for (const props of v) {\n        tagPromises.push(normaliseTag(k, props, e));\n      }\n      continue;\n    }\n    tagPromises.push(normaliseTag(k, v, e));\n  }\n  if (tagPromises.length === 0) {\n    return [];\n  }\n  const headTags = [];\n  return thenable(nestedNormaliseEntryTags(headTags, tagPromises, 0), () => headTags.map((t, i) => {\n    t._e = e._i;\n    e.mode && (t._m = e.mode);\n    t._p = (e._i << TagEntityBits) + i;\n    return t;\n  }));\n}\n\nconst WhitelistAttributes = {\n  htmlAttrs: [\"id\", \"class\", \"lang\", \"dir\"],\n  bodyAttrs: [\"id\", \"class\"],\n  meta: [\"id\", \"name\", \"property\", \"charset\", \"content\"],\n  noscript: [\"id\", \"textContent\"],\n  script: [\"id\", \"type\", \"textContent\"],\n  link: [\"id\", \"color\", \"crossorigin\", \"fetchpriority\", \"href\", \"hreflang\", \"imagesrcset\", \"imagesizes\", \"integrity\", \"media\", \"referrerpolicy\", \"rel\", \"sizes\", \"type\"]\n};\nfunction acceptDataAttrs(value) {\n  const filtered = {};\n  Object.keys(value || {}).filter((a) => a.startsWith(\"data-\")).forEach((a) => {\n    filtered[a] = value[a];\n  });\n  return filtered;\n}\nfunction whitelistSafeInput(input) {\n  const filtered = {};\n  Object.keys(input).forEach((key) => {\n    const tagValue = input[key];\n    if (!tagValue)\n      return;\n    switch (key) {\n      // always safe\n      case \"title\":\n      case \"titleTemplate\":\n      case \"templateParams\":\n        filtered[key] = tagValue;\n        break;\n      case \"htmlAttrs\":\n      case \"bodyAttrs\":\n        filtered[key] = acceptDataAttrs(tagValue);\n        WhitelistAttributes[key].forEach((a) => {\n          if (tagValue[a])\n            filtered[key][a] = tagValue[a];\n        });\n        break;\n      // meta is safe, except for http-equiv\n      case \"meta\":\n        if (Array.isArray(tagValue)) {\n          filtered[key] = tagValue.map((meta) => {\n            const safeMeta = acceptDataAttrs(meta);\n            WhitelistAttributes.meta.forEach((key2) => {\n              if (meta[key2])\n                safeMeta[key2] = meta[key2];\n            });\n            return safeMeta;\n          }).filter((meta) => Object.keys(meta).length > 0);\n        }\n        break;\n      // link tags we don't allow stylesheets, scripts, preloading, prerendering, prefetching, etc\n      case \"link\":\n        if (Array.isArray(tagValue)) {\n          filtered[key] = tagValue.map((meta) => {\n            const link = acceptDataAttrs(meta);\n            WhitelistAttributes.link.forEach((key2) => {\n              const val = meta[key2];\n              if (key2 === \"rel\" && (val === \"stylesheet\" || val === \"canonical\" || val === \"modulepreload\" || val === \"prerender\" || val === \"preload\" || val === \"prefetch\"))\n                return;\n              if (key2 === \"href\") {\n                if (val.includes(\"javascript:\") || val.includes(\"data:\"))\n                  return;\n                link[key2] = val;\n              } else if (val) {\n                link[key2] = val;\n              }\n            });\n            return link;\n          }).filter((link) => Object.keys(link).length > 1 && !!link.rel);\n        }\n        break;\n      case \"noscript\":\n        if (Array.isArray(tagValue)) {\n          filtered[key] = tagValue.map((meta) => {\n            const noscript = acceptDataAttrs(meta);\n            WhitelistAttributes.noscript.forEach((key2) => {\n              if (meta[key2])\n                noscript[key2] = meta[key2];\n            });\n            return noscript;\n          }).filter((meta) => Object.keys(meta).length > 0);\n        }\n        break;\n      // we only allow JSON in scripts\n      case \"script\":\n        if (Array.isArray(tagValue)) {\n          filtered[key] = tagValue.map((script) => {\n            const safeScript = acceptDataAttrs(script);\n            WhitelistAttributes.script.forEach((s) => {\n              if (script[s]) {\n                if (s === \"textContent\") {\n                  try {\n                    const jsonVal = typeof script[s] === \"string\" ? JSON.parse(script[s]) : script[s];\n                    safeScript[s] = JSON.stringify(jsonVal, null, 0);\n                  } catch (e) {\n                  }\n                } else {\n                  safeScript[s] = script[s];\n                }\n              }\n            });\n            return safeScript;\n          }).filter((meta) => Object.keys(meta).length > 0);\n        }\n        break;\n    }\n  });\n  return filtered;\n}\n\nconst NetworkEvents = /* @__PURE__ */ new Set([\"onload\", \"onerror\", \"onabort\", \"onprogress\", \"onloadstart\"]);\nconst ScriptNetworkEvents = /* @__PURE__ */ new Set([\"onload\", \"onerror\"]);\n\nconst TAG_WEIGHTS = {\n  // tags\n  base: -10,\n  title: 10\n};\nconst TAG_ALIASES = {\n  // relative scores to their default values\n  critical: -80,\n  high: -10,\n  low: 20\n};\nfunction tagWeight(tag) {\n  const priority = tag.tagPriority;\n  if (typeof priority === \"number\")\n    return priority;\n  let weight = 100;\n  if (tag.tag === \"meta\") {\n    if (tag.props[\"http-equiv\"] === \"content-security-policy\")\n      weight = -30;\n    else if (tag.props.charset)\n      weight = -20;\n    else if (tag.props.name === \"viewport\")\n      weight = -15;\n  } else if (tag.tag === \"link\" && tag.props.rel === \"preconnect\") {\n    weight = 20;\n  } else if (tag.tag in TAG_WEIGHTS) {\n    weight = TAG_WEIGHTS[tag.tag];\n  }\n  if (priority && priority in TAG_ALIASES) {\n    return weight + TAG_ALIASES[priority];\n  }\n  return weight;\n}\nconst SortModifiers = [{ prefix: \"before:\", offset: -1 }, { prefix: \"after:\", offset: 1 }];\n\nconst allowedMetaProperties = [\"name\", \"property\", \"http-equiv\"];\nfunction tagDedupeKey(tag) {\n  const { props, tag: tagName } = tag;\n  if (UniqueTags.has(tagName))\n    return tagName;\n  if (tagName === \"link\" && props.rel === \"canonical\")\n    return \"canonical\";\n  if (props.charset)\n    return \"charset\";\n  if (props.id) {\n    return `${tagName}:id:${props.id}`;\n  }\n  for (const n of allowedMetaProperties) {\n    if (props[n] !== void 0) {\n      return `${tagName}:${n}:${props[n]}`;\n    }\n  }\n  return false;\n}\n\nconst sepSub = \"%separator\";\nfunction sub(p, token, isJson = false) {\n  let val;\n  if (token === \"s\" || token === \"pageTitle\") {\n    val = p.pageTitle;\n  } else if (token.includes(\".\")) {\n    const dotIndex = token.indexOf(\".\");\n    val = p[token.substring(0, dotIndex)]?.[token.substring(dotIndex + 1)];\n  } else {\n    val = p[token];\n  }\n  if (val !== void 0) {\n    return isJson ? (val || \"\").replace(/\"/g, '\\\\\"') : val || \"\";\n  }\n  return void 0;\n}\nconst sepSubRe = new RegExp(`${sepSub}(?:\\\\s*${sepSub})*`, \"g\");\nfunction processTemplateParams(s, p, sep, isJson = false) {\n  if (typeof s !== \"string\" || !s.includes(\"%\"))\n    return s;\n  let decoded = s;\n  try {\n    decoded = decodeURI(s);\n  } catch {\n  }\n  const tokens = decoded.match(/%\\w+(?:\\.\\w+)?/g);\n  if (!tokens) {\n    return s;\n  }\n  const hasSepSub = s.includes(sepSub);\n  s = s.replace(/%\\w+(?:\\.\\w+)?/g, (token) => {\n    if (token === sepSub || !tokens.includes(token)) {\n      return token;\n    }\n    const re = sub(p, token.slice(1), isJson);\n    return re !== void 0 ? re : token;\n  }).trim();\n  if (hasSepSub) {\n    if (s.endsWith(sepSub))\n      s = s.slice(0, -sepSub.length);\n    if (s.startsWith(sepSub))\n      s = s.slice(sepSub.length);\n    s = s.replace(sepSubRe, sep).trim();\n  }\n  return s;\n}\n\nfunction resolveTitleTemplate(template, title) {\n  if (template == null)\n    return title || null;\n  if (typeof template === \"function\")\n    return template(title);\n  return template;\n}\n\nexport { HasElementTags, IsBrowser, NetworkEvents, ScriptNetworkEvents, SelfClosingTags, SortModifiers, TAG_ALIASES, TAG_WEIGHTS, TagConfigKeys, TagEntityBits, TagsWithInnerContent, UniqueTags, ValidHeadTags, asArray, composableNames, defineHeadPlugin, hashCode, hashTag, normaliseEntryTags, normaliseProps, normaliseStyleClassProps, normaliseTag, packMeta, processTemplateParams, resolveMetaKeyType, resolveMetaKeyValue, resolvePackedMetaObjectValue, resolveTitleTemplate, tagDedupeKey, tagWeight, thenable, unpackMeta, whitelistSafeInput };\n", "import { HasElementTags, hashTag, normaliseProps, tagDedupeKey, defineHeadPlugin } from '@unhead/shared';\n\nasync function renderDOMHead(head, options = {}) {\n  const dom = options.document || head.resolvedOptions.document;\n  if (!dom || !head.dirty)\n    return;\n  const beforeRenderCtx = { shouldRender: true, tags: [] };\n  await head.hooks.callHook(\"dom:beforeRender\", beforeRenderCtx);\n  if (!beforeRenderCtx.shouldRender)\n    return;\n  if (head._domUpdatePromise) {\n    return head._domUpdatePromise;\n  }\n  head._domUpdatePromise = new Promise(async (resolve) => {\n    const tags = (await head.resolveTags()).map((tag) => ({\n      tag,\n      id: HasElementTags.has(tag.tag) ? hashTag(tag) : tag.tag,\n      shouldRender: true\n    }));\n    let state = head._dom;\n    if (!state) {\n      state = {\n        elMap: { htmlAttrs: dom.documentElement, bodyAttrs: dom.body }\n      };\n      const takenDedupeKeys = /* @__PURE__ */ new Set();\n      for (const key of [\"body\", \"head\"]) {\n        const children = dom[key]?.children;\n        for (const c of children) {\n          const tag = c.tagName.toLowerCase();\n          if (!HasElementTags.has(tag)) {\n            continue;\n          }\n          const t = {\n            tag,\n            props: await normaliseProps(\n              c.getAttributeNames().reduce((props, name) => ({ ...props, [name]: c.getAttribute(name) }), {})\n            ),\n            innerHTML: c.innerHTML\n          };\n          const dedupeKey = tagDedupeKey(t);\n          let d = dedupeKey;\n          let i = 1;\n          while (d && takenDedupeKeys.has(d))\n            d = `${dedupeKey}:${i++}`;\n          if (d) {\n            t._d = d;\n            takenDedupeKeys.add(d);\n          }\n          state.elMap[c.getAttribute(\"data-hid\") || hashTag(t)] = c;\n        }\n      }\n    }\n    state.pendingSideEffects = { ...state.sideEffects };\n    state.sideEffects = {};\n    function track(id, scope, fn) {\n      const k = `${id}:${scope}`;\n      state.sideEffects[k] = fn;\n      delete state.pendingSideEffects[k];\n    }\n    function trackCtx({ id, $el, tag }) {\n      const isAttrTag = tag.tag.endsWith(\"Attrs\");\n      state.elMap[id] = $el;\n      if (!isAttrTag) {\n        if (tag.textContent && tag.textContent !== $el.textContent) {\n          $el.textContent = tag.textContent;\n        }\n        if (tag.innerHTML && tag.innerHTML !== $el.innerHTML) {\n          $el.innerHTML = tag.innerHTML;\n        }\n        track(id, \"el\", () => {\n          state.elMap[id]?.remove();\n          delete state.elMap[id];\n        });\n      }\n      if (tag._eventHandlers) {\n        for (const k in tag._eventHandlers) {\n          if (!Object.prototype.hasOwnProperty.call(tag._eventHandlers, k)) {\n            continue;\n          }\n          if ($el.getAttribute(`data-${k}`) !== \"\") {\n            (tag.tag === \"bodyAttrs\" ? dom.defaultView : $el).addEventListener(\n              // onload -> load\n              k.substring(2),\n              tag._eventHandlers[k].bind($el)\n            );\n            $el.setAttribute(`data-${k}`, \"\");\n          }\n        }\n      }\n      for (const k in tag.props) {\n        if (!Object.prototype.hasOwnProperty.call(tag.props, k)) {\n          continue;\n        }\n        const value = tag.props[k];\n        const ck = `attr:${k}`;\n        if (k === \"class\") {\n          if (!value) {\n            continue;\n          }\n          for (const c of value.split(\" \")) {\n            isAttrTag && track(id, `${ck}:${c}`, () => $el.classList.remove(c));\n            !$el.classList.contains(c) && $el.classList.add(c);\n          }\n        } else if (k === \"style\") {\n          if (!value) {\n            continue;\n          }\n          for (const c of value.split(\";\")) {\n            const propIndex = c.indexOf(\":\");\n            const k2 = c.substring(0, propIndex).trim();\n            const v = c.substring(propIndex + 1).trim();\n            track(id, `${ck}:${k2}`, () => {\n              $el.style.removeProperty(k2);\n            });\n            $el.style.setProperty(k2, v);\n          }\n        } else {\n          $el.getAttribute(k) !== value && $el.setAttribute(k, value === true ? \"\" : String(value));\n          isAttrTag && track(id, ck, () => $el.removeAttribute(k));\n        }\n      }\n    }\n    const pending = [];\n    const frag = {\n      bodyClose: void 0,\n      bodyOpen: void 0,\n      head: void 0\n    };\n    for (const ctx of tags) {\n      const { tag, shouldRender, id } = ctx;\n      if (!shouldRender)\n        continue;\n      if (tag.tag === \"title\") {\n        dom.title = tag.textContent;\n        continue;\n      }\n      ctx.$el = ctx.$el || state.elMap[id];\n      if (ctx.$el) {\n        trackCtx(ctx);\n      } else if (HasElementTags.has(tag.tag)) {\n        pending.push(ctx);\n      }\n    }\n    for (const ctx of pending) {\n      const pos = ctx.tag.tagPosition || \"head\";\n      ctx.$el = dom.createElement(ctx.tag.tag);\n      trackCtx(ctx);\n      frag[pos] = frag[pos] || dom.createDocumentFragment();\n      frag[pos].appendChild(ctx.$el);\n    }\n    for (const ctx of tags)\n      await head.hooks.callHook(\"dom:renderTag\", ctx, dom, track);\n    frag.head && dom.head.appendChild(frag.head);\n    frag.bodyOpen && dom.body.insertBefore(frag.bodyOpen, dom.body.firstChild);\n    frag.bodyClose && dom.body.appendChild(frag.bodyClose);\n    for (const k in state.pendingSideEffects) {\n      state.pendingSideEffects[k]();\n    }\n    head._dom = state;\n    await head.hooks.callHook(\"dom:rendered\", { renders: tags });\n    resolve();\n  }).finally(() => {\n    head._domUpdatePromise = void 0;\n    head.dirty = false;\n  });\n  return head._domUpdatePromise;\n}\n\nfunction debouncedRenderDOMHead(head, options = {}) {\n  const fn = options.delayFn || ((fn2) => setTimeout(fn2, 10));\n  return head._domDebouncedUpdatePromise = head._domDebouncedUpdatePromise || new Promise((resolve) => fn(() => {\n    return renderDOMHead(head, options).then(() => {\n      delete head._domDebouncedUpdatePromise;\n      resolve();\n    });\n  }));\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction DomPlugin(options) {\n  return defineHeadPlugin((head) => {\n    const initialPayload = head.resolvedOptions.document?.head.querySelector('script[id=\"unhead:payload\"]')?.innerHTML || false;\n    if (initialPayload) {\n      head.push(JSON.parse(initialPayload));\n    }\n    return {\n      mode: \"client\",\n      hooks: {\n        \"entries:updated\": (head2) => {\n          debouncedRenderDOMHead(head2, options);\n        }\n      }\n    };\n  });\n}\n\nexport { DomPlugin, debouncedRenderDOMHead, renderDOMHead };\n", "function flatHooks(configHooks, hooks = {}, parentName) {\n  for (const key in configHooks) {\n    const subHook = configHooks[key];\n    const name = parentName ? `${parentName}:${key}` : key;\n    if (typeof subHook === \"object\" && subHook !== null) {\n      flatHooks(subHook, hooks, name);\n    } else if (typeof subHook === \"function\") {\n      hooks[name] = subHook;\n    }\n  }\n  return hooks;\n}\nfunction mergeHooks(...hooks) {\n  const finalHooks = {};\n  for (const hook of hooks) {\n    const flatenHook = flatHooks(hook);\n    for (const key in flatenHook) {\n      if (finalHooks[key]) {\n        finalHooks[key].push(flatenHook[key]);\n      } else {\n        finalHooks[key] = [flatenHook[key]];\n      }\n    }\n  }\n  for (const key in finalHooks) {\n    if (finalHooks[key].length > 1) {\n      const array = finalHooks[key];\n      finalHooks[key] = (...arguments_) => serial(array, (function_) => function_(...arguments_));\n    } else {\n      finalHooks[key] = finalHooks[key][0];\n    }\n  }\n  return finalHooks;\n}\nfunction serial(tasks, function_) {\n  return tasks.reduce(\n    (promise, task) => promise.then(() => function_(task)),\n    Promise.resolve()\n  );\n}\nconst defaultTask = { run: (function_) => function_() };\nconst _createTask = () => defaultTask;\nconst createTask = typeof console.createTask !== \"undefined\" ? console.createTask : _createTask;\nfunction serialTaskCaller(hooks, args) {\n  const name = args.shift();\n  const task = createTask(name);\n  return hooks.reduce(\n    (promise, hookFunction) => promise.then(() => task.run(() => hookFunction(...args))),\n    Promise.resolve()\n  );\n}\nfunction parallelTaskCaller(hooks, args) {\n  const name = args.shift();\n  const task = createTask(name);\n  return Promise.all(hooks.map((hook) => task.run(() => hook(...args))));\n}\nfunction serialCaller(hooks, arguments_) {\n  return hooks.reduce(\n    (promise, hookFunction) => promise.then(() => hookFunction(...arguments_ || [])),\n    Promise.resolve()\n  );\n}\nfunction parallelCaller(hooks, args) {\n  return Promise.all(hooks.map((hook) => hook(...args || [])));\n}\nfunction callEachWith(callbacks, arg0) {\n  for (const callback of [...callbacks]) {\n    callback(arg0);\n  }\n}\n\nclass Hookable {\n  constructor() {\n    this._hooks = {};\n    this._before = void 0;\n    this._after = void 0;\n    this._deprecatedMessages = void 0;\n    this._deprecatedHooks = {};\n    this.hook = this.hook.bind(this);\n    this.callHook = this.callHook.bind(this);\n    this.callHookWith = this.callHookWith.bind(this);\n  }\n  hook(name, function_, options = {}) {\n    if (!name || typeof function_ !== \"function\") {\n      return () => {\n      };\n    }\n    const originalName = name;\n    let dep;\n    while (this._deprecatedHooks[name]) {\n      dep = this._deprecatedHooks[name];\n      name = dep.to;\n    }\n    if (dep && !options.allowDeprecated) {\n      let message = dep.message;\n      if (!message) {\n        message = `${originalName} hook has been deprecated` + (dep.to ? `, please use ${dep.to}` : \"\");\n      }\n      if (!this._deprecatedMessages) {\n        this._deprecatedMessages = /* @__PURE__ */ new Set();\n      }\n      if (!this._deprecatedMessages.has(message)) {\n        console.warn(message);\n        this._deprecatedMessages.add(message);\n      }\n    }\n    if (!function_.name) {\n      try {\n        Object.defineProperty(function_, \"name\", {\n          get: () => \"_\" + name.replace(/\\W+/g, \"_\") + \"_hook_cb\",\n          configurable: true\n        });\n      } catch {\n      }\n    }\n    this._hooks[name] = this._hooks[name] || [];\n    this._hooks[name].push(function_);\n    return () => {\n      if (function_) {\n        this.removeHook(name, function_);\n        function_ = void 0;\n      }\n    };\n  }\n  hookOnce(name, function_) {\n    let _unreg;\n    let _function = (...arguments_) => {\n      if (typeof _unreg === \"function\") {\n        _unreg();\n      }\n      _unreg = void 0;\n      _function = void 0;\n      return function_(...arguments_);\n    };\n    _unreg = this.hook(name, _function);\n    return _unreg;\n  }\n  removeHook(name, function_) {\n    if (this._hooks[name]) {\n      const index = this._hooks[name].indexOf(function_);\n      if (index !== -1) {\n        this._hooks[name].splice(index, 1);\n      }\n      if (this._hooks[name].length === 0) {\n        delete this._hooks[name];\n      }\n    }\n  }\n  deprecateHook(name, deprecated) {\n    this._deprecatedHooks[name] = typeof deprecated === \"string\" ? { to: deprecated } : deprecated;\n    const _hooks = this._hooks[name] || [];\n    delete this._hooks[name];\n    for (const hook of _hooks) {\n      this.hook(name, hook);\n    }\n  }\n  deprecateHooks(deprecatedHooks) {\n    Object.assign(this._deprecatedHooks, deprecatedHooks);\n    for (const name in deprecatedHooks) {\n      this.deprecateHook(name, deprecatedHooks[name]);\n    }\n  }\n  addHooks(configHooks) {\n    const hooks = flatHooks(configHooks);\n    const removeFns = Object.keys(hooks).map(\n      (key) => this.hook(key, hooks[key])\n    );\n    return () => {\n      for (const unreg of removeFns.splice(0, removeFns.length)) {\n        unreg();\n      }\n    };\n  }\n  removeHooks(configHooks) {\n    const hooks = flatHooks(configHooks);\n    for (const key in hooks) {\n      this.removeHook(key, hooks[key]);\n    }\n  }\n  removeAllHooks() {\n    for (const key in this._hooks) {\n      delete this._hooks[key];\n    }\n  }\n  callHook(name, ...arguments_) {\n    arguments_.unshift(name);\n    return this.callHookWith(serialTaskCaller, name, ...arguments_);\n  }\n  callHookParallel(name, ...arguments_) {\n    arguments_.unshift(name);\n    return this.callHookWith(parallelTaskCaller, name, ...arguments_);\n  }\n  callHookWith(caller, name, ...arguments_) {\n    const event = this._before || this._after ? { name, args: arguments_, context: {} } : void 0;\n    if (this._before) {\n      callEachWith(this._before, event);\n    }\n    const result = caller(\n      name in this._hooks ? [...this._hooks[name]] : [],\n      arguments_\n    );\n    if (result instanceof Promise) {\n      return result.finally(() => {\n        if (this._after && event) {\n          callEachWith(this._after, event);\n        }\n      });\n    }\n    if (this._after && event) {\n      callEachWith(this._after, event);\n    }\n    return result;\n  }\n  beforeEach(function_) {\n    this._before = this._before || [];\n    this._before.push(function_);\n    return () => {\n      if (this._before !== void 0) {\n        const index = this._before.indexOf(function_);\n        if (index !== -1) {\n          this._before.splice(index, 1);\n        }\n      }\n    };\n  }\n  afterEach(function_) {\n    this._after = this._after || [];\n    this._after.push(function_);\n    return () => {\n      if (this._after !== void 0) {\n        const index = this._after.indexOf(function_);\n        if (index !== -1) {\n          this._after.splice(index, 1);\n        }\n      }\n    };\n  }\n}\nfunction createHooks() {\n  return new Hookable();\n}\n\nconst isBrowser = typeof window !== \"undefined\";\nfunction createDebugger(hooks, _options = {}) {\n  const options = {\n    inspect: isBrowser,\n    group: isBrowser,\n    filter: () => true,\n    ..._options\n  };\n  const _filter = options.filter;\n  const filter = typeof _filter === \"string\" ? (name) => name.startsWith(_filter) : _filter;\n  const _tag = options.tag ? `[${options.tag}] ` : \"\";\n  const logPrefix = (event) => _tag + event.name + \"\".padEnd(event._id, \"\\0\");\n  const _idCtr = {};\n  const unsubscribeBefore = hooks.beforeEach((event) => {\n    if (filter !== void 0 && !filter(event.name)) {\n      return;\n    }\n    _idCtr[event.name] = _idCtr[event.name] || 0;\n    event._id = _idCtr[event.name]++;\n    console.time(logPrefix(event));\n  });\n  const unsubscribeAfter = hooks.afterEach((event) => {\n    if (filter !== void 0 && !filter(event.name)) {\n      return;\n    }\n    if (options.group) {\n      console.groupCollapsed(event.name);\n    }\n    if (options.inspect) {\n      console.timeLog(logPrefix(event), event.args);\n    } else {\n      console.timeEnd(logPrefix(event));\n    }\n    if (options.group) {\n      console.groupEnd();\n    }\n    _idCtr[event.name]--;\n  });\n  return {\n    /** Stop debugging and remove listeners */\n    close: () => {\n      unsubscribeBefore();\n      unsubscribeAfter();\n    }\n  };\n}\n\nexport { Hookable, createDebugger, createHooks, flatHooks, mergeHooks, parallelCaller, serial, serialCaller };\n", "import { DomPlugin } from '@unhead/dom';\nimport { defineHeadPlugin, tagDedupeKey, hashTag, tagWeight, HasElementTags, NetworkEvents, hashCode, SortModifiers, processTemplateParams, resolveTitleTemplate, IsBrowser, normaliseEntryTags, composableNames, whitelistSafeInput, ScriptNetworkEvents, unpackMeta } from '@unhead/shared';\nexport { composableNames } from '@unhead/shared';\nimport { createHooks } from 'hookable';\n\nconst UsesMergeStrategy = /* @__PURE__ */ new Set([\"templateParams\", \"htmlAttrs\", \"bodyAttrs\"]);\nconst DedupePlugin = defineHeadPlugin({\n  hooks: {\n    \"tag:normalise\": ({ tag }) => {\n      if (tag.props.hid) {\n        tag.key = tag.props.hid;\n        delete tag.props.hid;\n      }\n      if (tag.props.vmid) {\n        tag.key = tag.props.vmid;\n        delete tag.props.vmid;\n      }\n      if (tag.props.key) {\n        tag.key = tag.props.key;\n        delete tag.props.key;\n      }\n      const generatedKey = tagDedupeKey(tag);\n      if (generatedKey && !generatedKey.startsWith(\"meta:og:\") && !generatedKey.startsWith(\"meta:twitter:\")) {\n        delete tag.key;\n      }\n      const dedupe = generatedKey || (tag.key ? `${tag.tag}:${tag.key}` : false);\n      if (dedupe)\n        tag._d = dedupe;\n    },\n    \"tags:resolve\": (ctx) => {\n      const deduping = /* @__PURE__ */ Object.create(null);\n      for (const tag of ctx.tags) {\n        const dedupeKey = (tag.key ? `${tag.tag}:${tag.key}` : tag._d) || hashTag(tag);\n        const dupedTag = deduping[dedupeKey];\n        if (dupedTag) {\n          let strategy = tag?.tagDuplicateStrategy;\n          if (!strategy && UsesMergeStrategy.has(tag.tag))\n            strategy = \"merge\";\n          if (strategy === \"merge\") {\n            const oldProps = dupedTag.props;\n            if (oldProps.style && tag.props.style) {\n              if (oldProps.style[oldProps.style.length - 1] !== \";\") {\n                oldProps.style += \";\";\n              }\n              tag.props.style = `${oldProps.style} ${tag.props.style}`;\n            }\n            if (oldProps.class && tag.props.class) {\n              tag.props.class = `${oldProps.class} ${tag.props.class}`;\n            } else if (oldProps.class) {\n              tag.props.class = oldProps.class;\n            }\n            deduping[dedupeKey].props = {\n              ...oldProps,\n              ...tag.props\n            };\n            continue;\n          } else if (tag._e === dupedTag._e) {\n            dupedTag._duped = dupedTag._duped || [];\n            tag._d = `${dupedTag._d}:${dupedTag._duped.length + 1}`;\n            dupedTag._duped.push(tag);\n            continue;\n          } else if (tagWeight(tag) > tagWeight(dupedTag)) {\n            continue;\n          }\n        }\n        const hasProps = tag.innerHTML || tag.textContent || Object.keys(tag.props).length !== 0;\n        if (!hasProps && HasElementTags.has(tag.tag)) {\n          delete deduping[dedupeKey];\n          continue;\n        }\n        deduping[dedupeKey] = tag;\n      }\n      const newTags = [];\n      for (const key in deduping) {\n        const tag = deduping[key];\n        const dupes = tag._duped;\n        newTags.push(tag);\n        if (dupes) {\n          delete tag._duped;\n          newTags.push(...dupes);\n        }\n      }\n      ctx.tags = newTags;\n      ctx.tags = ctx.tags.filter((t) => !(t.tag === \"meta\" && (t.props.name || t.props.property) && !t.props.content));\n    }\n  }\n});\n\nconst ValidEventTags = /* @__PURE__ */ new Set([\"script\", \"link\", \"bodyAttrs\"]);\nconst EventHandlersPlugin = defineHeadPlugin((head) => ({\n  hooks: {\n    \"tags:resolve\": (ctx) => {\n      for (const tag of ctx.tags) {\n        if (!ValidEventTags.has(tag.tag)) {\n          continue;\n        }\n        const props = tag.props;\n        for (const key in props) {\n          if (key[0] !== \"o\" || key[1] !== \"n\") {\n            continue;\n          }\n          if (!Object.prototype.hasOwnProperty.call(props, key)) {\n            continue;\n          }\n          const value = props[key];\n          if (typeof value !== \"function\") {\n            continue;\n          }\n          if (head.ssr && NetworkEvents.has(key)) {\n            props[key] = `this.dataset.${key}fired = true`;\n          } else {\n            delete props[key];\n          }\n          tag._eventHandlers = tag._eventHandlers || {};\n          tag._eventHandlers[key] = value;\n        }\n        if (head.ssr && tag._eventHandlers && (tag.props.src || tag.props.href)) {\n          tag.key = tag.key || hashCode(tag.props.src || tag.props.href);\n        }\n      }\n    },\n    \"dom:renderTag\": ({ $el, tag }) => {\n      const dataset = $el?.dataset;\n      if (!dataset) {\n        return;\n      }\n      for (const k in dataset) {\n        if (!k.endsWith(\"fired\")) {\n          continue;\n        }\n        const ek = k.slice(0, -5);\n        if (!NetworkEvents.has(ek)) {\n          continue;\n        }\n        tag._eventHandlers?.[ek]?.call($el, new Event(ek.substring(2)));\n      }\n    }\n  }\n}));\n\nconst DupeableTags = /* @__PURE__ */ new Set([\"link\", \"style\", \"script\", \"noscript\"]);\nconst HashKeyedPlugin = defineHeadPlugin({\n  hooks: {\n    \"tag:normalise\": ({ tag }) => {\n      if (tag.key && DupeableTags.has(tag.tag)) {\n        tag.props[\"data-hid\"] = tag._h = hashCode(tag.key);\n      }\n    }\n  }\n});\n\nconst PayloadPlugin = defineHeadPlugin({\n  mode: \"server\",\n  hooks: {\n    \"tags:beforeResolve\": (ctx) => {\n      const payload = {};\n      let hasPayload = false;\n      for (const tag of ctx.tags) {\n        if (tag._m !== \"server\" || tag.tag !== \"titleTemplate\" && tag.tag !== \"templateParams\" && tag.tag !== \"title\") {\n          continue;\n        }\n        payload[tag.tag] = tag.tag === \"title\" || tag.tag === \"titleTemplate\" ? tag.textContent : tag.props;\n        hasPayload = true;\n      }\n      if (hasPayload) {\n        ctx.tags.push({\n          tag: \"script\",\n          innerHTML: JSON.stringify(payload),\n          props: { id: \"unhead:payload\", type: \"application/json\" }\n        });\n      }\n    }\n  }\n});\n\nconst SortPlugin = defineHeadPlugin({\n  hooks: {\n    \"tags:resolve\": (ctx) => {\n      for (const tag of ctx.tags) {\n        if (typeof tag.tagPriority !== \"string\") {\n          continue;\n        }\n        for (const { prefix, offset } of SortModifiers) {\n          if (!tag.tagPriority.startsWith(prefix)) {\n            continue;\n          }\n          const key = tag.tagPriority.substring(prefix.length);\n          const position = ctx.tags.find((tag2) => tag2._d === key)?._p;\n          if (position !== void 0) {\n            tag._p = position + offset;\n            break;\n          }\n        }\n      }\n      ctx.tags.sort((a, b) => {\n        const aWeight = tagWeight(a);\n        const bWeight = tagWeight(b);\n        if (aWeight < bWeight) {\n          return -1;\n        } else if (aWeight > bWeight) {\n          return 1;\n        }\n        return a._p - b._p;\n      });\n    }\n  }\n});\n\nconst SupportedAttrs = {\n  meta: \"content\",\n  link: \"href\",\n  htmlAttrs: \"lang\"\n};\nconst contentAttrs = [\"innerHTML\", \"textContent\"];\nconst TemplateParamsPlugin = defineHeadPlugin((head) => ({\n  hooks: {\n    \"tags:resolve\": (ctx) => {\n      const { tags } = ctx;\n      let templateParams;\n      for (let i = 0; i < tags.length; i += 1) {\n        const tag = tags[i];\n        if (tag.tag !== \"templateParams\") {\n          continue;\n        }\n        templateParams = ctx.tags.splice(i, 1)[0].props;\n        i -= 1;\n      }\n      const params = templateParams || {};\n      const sep = params.separator || \"|\";\n      delete params.separator;\n      params.pageTitle = processTemplateParams(\n        // find templateParams\n        params.pageTitle || tags.find((tag) => tag.tag === \"title\")?.textContent || \"\",\n        params,\n        sep\n      );\n      for (const tag of tags) {\n        if (tag.processTemplateParams === false) {\n          continue;\n        }\n        const v = SupportedAttrs[tag.tag];\n        if (v && typeof tag.props[v] === \"string\") {\n          tag.props[v] = processTemplateParams(tag.props[v], params, sep);\n        } else if (tag.processTemplateParams || tag.tag === \"titleTemplate\" || tag.tag === \"title\") {\n          for (const p of contentAttrs) {\n            if (typeof tag[p] === \"string\")\n              tag[p] = processTemplateParams(tag[p], params, sep, tag.tag === \"script\" && tag.props.type.endsWith(\"json\"));\n          }\n        }\n      }\n      head._templateParams = params;\n      head._separator = sep;\n    },\n    \"tags:afterResolve\": ({ tags }) => {\n      let title;\n      for (let i = 0; i < tags.length; i += 1) {\n        const tag = tags[i];\n        if (tag.tag === \"title\" && tag.processTemplateParams !== false) {\n          title = tag;\n        }\n      }\n      if (title?.textContent) {\n        title.textContent = processTemplateParams(title.textContent, head._templateParams, head._separator);\n      }\n    }\n  }\n}));\n\nconst TitleTemplatePlugin = defineHeadPlugin({\n  hooks: {\n    \"tags:resolve\": (ctx) => {\n      const { tags } = ctx;\n      let titleTag;\n      let titleTemplateTag;\n      for (let i = 0; i < tags.length; i += 1) {\n        const tag = tags[i];\n        if (tag.tag === \"title\") {\n          titleTag = tag;\n        } else if (tag.tag === \"titleTemplate\") {\n          titleTemplateTag = tag;\n        }\n      }\n      if (titleTemplateTag && titleTag) {\n        const newTitle = resolveTitleTemplate(\n          titleTemplateTag.textContent,\n          titleTag.textContent\n        );\n        if (newTitle !== null) {\n          titleTag.textContent = newTitle || titleTag.textContent;\n        } else {\n          ctx.tags.splice(ctx.tags.indexOf(titleTag), 1);\n        }\n      } else if (titleTemplateTag) {\n        const newTitle = resolveTitleTemplate(\n          titleTemplateTag.textContent\n        );\n        if (newTitle !== null) {\n          titleTemplateTag.textContent = newTitle;\n          titleTemplateTag.tag = \"title\";\n          titleTemplateTag = void 0;\n        }\n      }\n      if (titleTemplateTag) {\n        ctx.tags.splice(ctx.tags.indexOf(titleTemplateTag), 1);\n      }\n    }\n  }\n});\n\nconst XSSPlugin = defineHeadPlugin({\n  hooks: {\n    \"tags:afterResolve\": (ctx) => {\n      for (const tag of ctx.tags) {\n        if (typeof tag.innerHTML === \"string\") {\n          if (tag.innerHTML && (tag.props.type === \"application/ld+json\" || tag.props.type === \"application/json\")) {\n            tag.innerHTML = tag.innerHTML.replace(/</g, \"\\\\u003C\");\n          } else {\n            tag.innerHTML = tag.innerHTML.replace(new RegExp(`</${tag.tag}`, \"g\"), `<\\\\/${tag.tag}`);\n          }\n        }\n      }\n    }\n  }\n});\n\nlet activeHead;\n// @__NO_SIDE_EFFECTS__\nfunction createHead(options = {}) {\n  const head = createHeadCore(options);\n  head.use(DomPlugin());\n  return activeHead = head;\n}\n// @__NO_SIDE_EFFECTS__\nfunction createServerHead(options = {}) {\n  return activeHead = createHeadCore(options);\n}\nfunction filterMode(mode, ssr) {\n  return !mode || mode === \"server\" && ssr || mode === \"client\" && !ssr;\n}\nfunction createHeadCore(options = {}) {\n  const hooks = createHooks();\n  hooks.addHooks(options.hooks || {});\n  options.document = options.document || (IsBrowser ? document : void 0);\n  const ssr = !options.document;\n  const updated = () => {\n    head.dirty = true;\n    hooks.callHook(\"entries:updated\", head);\n  };\n  let entryCount = 0;\n  let entries = [];\n  const plugins = [];\n  const head = {\n    plugins,\n    dirty: false,\n    resolvedOptions: options,\n    hooks,\n    headEntries() {\n      return entries;\n    },\n    use(p) {\n      const plugin = typeof p === \"function\" ? p(head) : p;\n      if (!plugin.key || !plugins.some((p2) => p2.key === plugin.key)) {\n        plugins.push(plugin);\n        filterMode(plugin.mode, ssr) && hooks.addHooks(plugin.hooks || {});\n      }\n    },\n    push(input, entryOptions) {\n      delete entryOptions?.head;\n      const entry = {\n        _i: entryCount++,\n        input,\n        ...entryOptions\n      };\n      if (filterMode(entry.mode, ssr)) {\n        entries.push(entry);\n        updated();\n      }\n      return {\n        dispose() {\n          entries = entries.filter((e) => e._i !== entry._i);\n          updated();\n        },\n        // a patch is the same as creating a new entry, just a nice DX\n        patch(input2) {\n          for (const e of entries) {\n            if (e._i === entry._i) {\n              e.input = entry.input = input2;\n            }\n          }\n          updated();\n        }\n      };\n    },\n    async resolveTags() {\n      const resolveCtx = { tags: [], entries: [...entries] };\n      await hooks.callHook(\"entries:resolve\", resolveCtx);\n      for (const entry of resolveCtx.entries) {\n        const resolved = entry.resolvedInput || entry.input;\n        entry.resolvedInput = await (entry.transform ? entry.transform(resolved) : resolved);\n        if (entry.resolvedInput) {\n          for (const tag of await normaliseEntryTags(entry)) {\n            const tagCtx = { tag, entry, resolvedOptions: head.resolvedOptions };\n            await hooks.callHook(\"tag:normalise\", tagCtx);\n            resolveCtx.tags.push(tagCtx.tag);\n          }\n        }\n      }\n      await hooks.callHook(\"tags:beforeResolve\", resolveCtx);\n      await hooks.callHook(\"tags:resolve\", resolveCtx);\n      await hooks.callHook(\"tags:afterResolve\", resolveCtx);\n      return resolveCtx.tags;\n    },\n    ssr\n  };\n  [\n    DedupePlugin,\n    PayloadPlugin,\n    EventHandlersPlugin,\n    HashKeyedPlugin,\n    SortPlugin,\n    TemplateParamsPlugin,\n    TitleTemplatePlugin,\n    XSSPlugin,\n    ...options?.plugins || []\n  ].forEach((p) => head.use(p));\n  head.hooks.callHook(\"init\", head);\n  return head;\n}\n\nconst unheadComposablesImports = [\n  {\n    from: \"unhead\",\n    imports: composableNames\n  }\n];\n\nfunction getActiveHead() {\n  return activeHead;\n}\n\nfunction useHead(input, options = {}) {\n  const head = options.head || getActiveHead();\n  return head?.push(input, options);\n}\n\nfunction useHeadSafe(input, options) {\n  return useHead(input, {\n    ...options,\n    transform: whitelistSafeInput\n  });\n}\n\nconst ScriptProxyTarget = Symbol(\"ScriptProxyTarget\");\nfunction scriptProxy() {\n}\nscriptProxy[ScriptProxyTarget] = true;\nfunction resolveScriptKey(input) {\n  return input.key || hashCode(input.src || (typeof input.innerHTML === \"string\" ? input.innerHTML : \"\"));\n}\nfunction useScript(_input, _options) {\n  const input = typeof _input === \"string\" ? { src: _input } : _input;\n  const options = _options || {};\n  const head = options.head || getActiveHead();\n  if (!head)\n    throw new Error(\"Missing Unhead context.\");\n  const id = resolveScriptKey(input);\n  const prevScript = head._scripts?.[id];\n  if (prevScript) {\n    prevScript.setupTriggerHandler(options.trigger);\n    return prevScript;\n  }\n  options.beforeInit?.();\n  const syncStatus = (s) => {\n    script.status = s;\n    head.hooks.callHook(`script:updated`, hookCtx);\n  };\n  ScriptNetworkEvents.forEach((fn) => {\n    const _fn = typeof input[fn] === \"function\" ? input[fn].bind(options.eventContext) : null;\n    input[fn] = (e) => {\n      syncStatus(fn === \"onload\" ? \"loaded\" : fn === \"onerror\" ? \"error\" : \"loading\");\n      _fn?.(e);\n    };\n  });\n  const _cbs = { loaded: [], error: [] };\n  const _registerCb = (key, cb) => {\n    if (_cbs[key]) {\n      const i = _cbs[key].push(cb);\n      return () => _cbs[key]?.splice(i - 1, 1);\n    }\n    cb(script.instance);\n    return () => {\n    };\n  };\n  const loadPromise = new Promise((resolve) => {\n    if (head.ssr)\n      return;\n    const emit = (api) => requestAnimationFrame(() => resolve(api));\n    const _ = head.hooks.hook(\"script:updated\", ({ script: script2 }) => {\n      const status = script2.status;\n      if (script2.id === id && (status === \"loaded\" || status === \"error\")) {\n        if (status === \"loaded\") {\n          if (typeof options.use === \"function\") {\n            const api = options.use();\n            if (api) {\n              emit(api);\n            }\n          } else {\n            emit({});\n          }\n        } else if (status === \"error\") {\n          resolve(false);\n        }\n        _();\n      }\n    });\n  });\n  const script = Object.assign(loadPromise, {\n    instance: !head.ssr && options?.use?.() || null,\n    proxy: null,\n    id,\n    status: \"awaitingLoad\",\n    remove() {\n      script._triggerAbortController?.abort();\n      script._triggerPromises = [];\n      if (script.entry) {\n        script.entry.dispose();\n        script.entry = void 0;\n        syncStatus(\"removed\");\n        delete head._scripts?.[id];\n        return true;\n      }\n      return false;\n    },\n    load(cb) {\n      script._triggerAbortController?.abort();\n      script._triggerPromises = [];\n      if (!script.entry) {\n        syncStatus(\"loading\");\n        const defaults = {\n          defer: true,\n          fetchpriority: \"low\"\n        };\n        if (input.src && (input.src.startsWith(\"http\") || input.src.startsWith(\"//\"))) {\n          defaults.crossorigin = \"anonymous\";\n          defaults.referrerpolicy = \"no-referrer\";\n        }\n        script.entry = head.push({\n          script: [{ ...defaults, ...input, key: `script.${id}` }]\n        }, options);\n      }\n      if (cb)\n        _registerCb(\"loaded\", cb);\n      return loadPromise;\n    },\n    onLoaded(cb) {\n      return _registerCb(\"loaded\", cb);\n    },\n    onError(cb) {\n      return _registerCb(\"error\", cb);\n    },\n    setupTriggerHandler(trigger) {\n      if (script.status !== \"awaitingLoad\") {\n        return;\n      }\n      if ((typeof trigger === \"undefined\" || trigger === \"client\") && !head.ssr || trigger === \"server\") {\n        script.load();\n      } else if (trigger instanceof Promise) {\n        if (head.ssr) {\n          return;\n        }\n        if (!script._triggerAbortController) {\n          script._triggerAbortController = new AbortController();\n          script._triggerAbortPromise = new Promise((resolve) => {\n            script._triggerAbortController.signal.addEventListener(\"abort\", () => {\n              script._triggerAbortController = null;\n              resolve();\n            });\n          });\n        }\n        script._triggerPromises = script._triggerPromises || [];\n        const idx = script._triggerPromises.push(Promise.race([\n          trigger.then((v) => typeof v === \"undefined\" || v ? script.load : void 0),\n          script._triggerAbortPromise\n        ]).catch(() => {\n        }).then((res2) => {\n          res2?.();\n        }).finally(() => {\n          script._triggerPromises?.splice(idx, 1);\n        }));\n      } else if (typeof trigger === \"function\") {\n        trigger(script.load);\n      }\n    },\n    _cbs\n  });\n  loadPromise.then((api) => {\n    if (api !== false) {\n      script.instance = api;\n      _cbs.loaded?.forEach((cb) => cb(api));\n      _cbs.loaded = null;\n    } else {\n      _cbs.error?.forEach((cb) => cb());\n      _cbs.error = null;\n    }\n  });\n  const hookCtx = { script };\n  script.setupTriggerHandler(options.trigger);\n  script.$script = script;\n  const proxyChain = (instance, accessor, accessors) => {\n    return new Proxy((!accessor ? instance : instance?.[accessor]) || scriptProxy, {\n      get(_, k, r) {\n        head.hooks.callHook(\"script:instance-fn\", { script, fn: k, exists: k in _ });\n        if (!accessor) {\n          const stub = options.stub?.({ script, fn: k });\n          if (stub)\n            return stub;\n        }\n        if (_ && k in _ && typeof _[k] !== \"undefined\") {\n          return Reflect.get(_, k, r);\n        }\n        if (k === Symbol.iterator) {\n          return [][Symbol.iterator];\n        }\n        return proxyChain(accessor ? instance?.[accessor] : instance, k, accessors || [k]);\n      },\n      async apply(_, _this, args) {\n        if (head.ssr && _[ScriptProxyTarget])\n          return;\n        let instance2;\n        const access = (fn2) => {\n          instance2 = fn2 || instance2;\n          for (let i = 0; i < (accessors || []).length; i++) {\n            const k = (accessors || [])[i];\n            fn2 = fn2?.[k];\n          }\n          return fn2;\n        };\n        let fn = access(script.instance);\n        if (!fn) {\n          fn = await new Promise((resolve) => {\n            script.onLoaded((api) => {\n              resolve(access(api));\n            });\n          });\n        }\n        return typeof fn === \"function\" ? Reflect.apply(fn, instance2, args) : fn;\n      }\n    });\n  };\n  script.proxy = proxyChain(script.instance);\n  const res = new Proxy(script, {\n    get(_, k) {\n      const target = k in script || String(k)[0] === \"_\" ? script : script.proxy;\n      if (k === \"then\" || k === \"catch\") {\n        return script[k].bind(script);\n      }\n      return Reflect.get(target, k, target);\n    }\n  });\n  head._scripts = Object.assign(head._scripts || {}, { [id]: res });\n  return res;\n}\n\nfunction useSeoMeta(input, options) {\n  const { title, titleTemplate, ...meta } = input;\n  return useHead({\n    title,\n    titleTemplate,\n    // we need to input the meta so the reactivity will be resolved\n    // @ts-expect-error runtime type\n    _flatMeta: meta\n  }, {\n    ...options,\n    transform(t) {\n      const meta2 = unpackMeta({ ...t._flatMeta });\n      delete t._flatMeta;\n      return {\n        // @ts-expect-error runtime type\n        ...t,\n        meta: meta2\n      };\n    }\n  });\n}\n\nfunction useServerHead(input, options = {}) {\n  return useHead(input, { ...options, mode: \"server\" });\n}\n\nfunction useServerHeadSafe(input, options = {}) {\n  return useHeadSafe(input, { ...options, mode: \"server\" });\n}\n\nfunction useServerSeoMeta(input, options) {\n  return useSeoMeta(input, {\n    ...options,\n    mode: \"server\"\n  });\n}\n\nconst importRe = /@import/;\n// @__NO_SIDE_EFFECTS__\nfunction CapoPlugin(options) {\n  return defineHeadPlugin({\n    hooks: {\n      \"tags:beforeResolve\": ({ tags }) => {\n        for (const tag of tags) {\n          if (tag.tagPosition && tag.tagPosition !== \"head\")\n            continue;\n          tag.tagPriority = tag.tagPriority || tagWeight(tag);\n          if (tag.tagPriority !== 100)\n            continue;\n          const isTruthy = (val) => val === \"\" || val === true;\n          const isScript = tag.tag === \"script\";\n          const isLink = tag.tag === \"link\";\n          if (isScript && isTruthy(tag.props.async)) {\n            tag.tagPriority = 30;\n          } else if (tag.tag === \"style\" && tag.innerHTML && importRe.test(tag.innerHTML)) {\n            tag.tagPriority = 40;\n          } else if (isScript && tag.props.src && !isTruthy(tag.props.defer) && !isTruthy(tag.props.async) && tag.props.type !== \"module\" && !tag.props.type?.endsWith(\"json\")) {\n            tag.tagPriority = 50;\n          } else if (isLink && tag.props.rel === \"stylesheet\" || tag.tag === \"style\") {\n            tag.tagPriority = 60;\n          } else if (isLink && (tag.props.rel === \"preload\" || tag.props.rel === \"modulepreload\")) {\n            tag.tagPriority = 70;\n          } else if (isScript && isTruthy(tag.props.defer) && tag.props.src && !isTruthy(tag.props.async)) {\n            tag.tagPriority = 80;\n          } else if (isLink && (tag.props.rel === \"prefetch\" || tag.props.rel === \"dns-prefetch\" || tag.props.rel === \"prerender\")) {\n            tag.tagPriority = 90;\n          }\n        }\n        options?.track && tags.push({\n          tag: \"htmlAttrs\",\n          props: {\n            \"data-capo\": \"\"\n          }\n        });\n      }\n    }\n  });\n}\n\n// @__NO_SIDE_EFFECTS__\nfunction HashHydrationPlugin() {\n  return defineHeadPlugin({});\n}\n\nexport { CapoPlugin, HashHydrationPlugin, createHead, createHeadCore, createServerHead, getActiveHead, resolveScriptKey, unheadComposablesImports, useHead, useHeadSafe, useScript, useSeoMeta, useServerHead, useServerHeadSafe, useServerSeoMeta };\n", "import { createServerHead as createServerHead$1, createHead as createHead$1, getActiveHead } from 'unhead';\nimport { version, unref, nextTick, inject } from 'vue';\nimport { defineHeadPlugin } from '@unhead/shared';\n\nconst Vue3 = version[0] === \"3\";\n\nfunction resolveUnref(r) {\n  return typeof r === \"function\" ? r() : unref(r);\n}\nfunction resolveUnrefHeadInput(ref) {\n  if (ref instanceof Promise || ref instanceof Date || ref instanceof RegExp)\n    return ref;\n  const root = resolveUnref(ref);\n  if (!ref || !root)\n    return root;\n  if (Array.isArray(root))\n    return root.map((r) => resolveUnrefHeadInput(r));\n  if (typeof root === \"object\") {\n    const resolved = {};\n    for (const k in root) {\n      if (!Object.prototype.hasOwnProperty.call(root, k)) {\n        continue;\n      }\n      if (k === \"titleTemplate\" || k[0] === \"o\" && k[1] === \"n\") {\n        resolved[k] = unref(root[k]);\n        continue;\n      }\n      resolved[k] = resolveUnrefHeadInput(root[k]);\n    }\n    return resolved;\n  }\n  return root;\n}\n\nconst VueReactivityPlugin = defineHeadPlugin({\n  hooks: {\n    \"entries:resolve\": (ctx) => {\n      for (const entry of ctx.entries)\n        entry.resolvedInput = resolveUnrefHeadInput(entry.input);\n    }\n  }\n});\n\nconst headSymbol = \"usehead\";\nfunction vueInstall(head) {\n  const plugin = {\n    install(app) {\n      if (Vue3) {\n        app.config.globalProperties.$unhead = head;\n        app.config.globalProperties.$head = head;\n        app.provide(headSymbol, head);\n      }\n    }\n  };\n  return plugin.install;\n}\nfunction createServerHead(options = {}) {\n  const head = createServerHead$1(options);\n  head.use(VueReactivityPlugin);\n  head.install = vueInstall(head);\n  return head;\n}\nfunction createHead(options = {}) {\n  options.domDelayFn = options.domDelayFn || ((fn) => nextTick(() => setTimeout(() => fn(), 0)));\n  const head = createHead$1(options);\n  head.use(VueReactivityPlugin);\n  head.install = vueInstall(head);\n  return head;\n}\n\nconst _global = typeof globalThis !== \"undefined\" ? globalThis : typeof window !== \"undefined\" ? window : typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : {};\nconst globalKey = \"__unhead_injection_handler__\";\nfunction setHeadInjectionHandler(handler) {\n  _global[globalKey] = handler;\n}\nfunction injectHead() {\n  if (globalKey in _global) {\n    return _global[globalKey]();\n  }\n  const head = inject(headSymbol);\n  if (!head && process.env.NODE_ENV !== \"production\")\n    console.warn(\"Unhead is missing Vue context, falling back to shared context. This may have unexpected results.\");\n  return head || getActiveHead();\n}\n\nexport { Vue3 as V, createServerHead as a, createHead as c, headSymbol as h, injectHead as i, resolveUnrefHeadInput as r, setHeadInjectionHandler as s };\n", "import { useScript as useScript$1 } from 'unhead';\nexport { CapoPlugin, HashHydrationPlugin, createHeadCore } from 'unhead';\nimport { i as injectHead, h as headSymbol, V as Vue3 } from './shared/vue.ziyDaVMR.mjs';\nexport { c as createHead, a as createServerHead, r as resolveUnrefHeadInput, s as setHeadInjectionHandler } from './shared/vue.ziyDaVMR.mjs';\nimport { composableNames, whitelistSafeInput, unpackMeta } from '@unhead/shared';\nimport { u as useHead } from './shared/vue.-sixQ7xP.mjs';\nimport { getCurrentInstance, onMounted, isRef, watch, onScopeDispose, ref } from 'vue';\n\nconst coreComposableNames = [\n  \"injectHead\"\n];\nconst unheadVueComposablesImports = {\n  \"@unhead/vue\": [...coreComposableNames, ...composableNames]\n};\n\nfunction useHeadSafe(input, options = {}) {\n  return useHead(input, { ...options, transform: whitelistSafeInput });\n}\n\nfunction registerVueScopeHandlers(script, scope) {\n  if (!scope) {\n    return;\n  }\n  const _registerCb = (key, cb) => {\n    if (!script._cbs[key]) {\n      cb(script.instance);\n      return () => {\n      };\n    }\n    let i = script._cbs[key].push(cb);\n    const destroy = () => {\n      if (i) {\n        script._cbs[key]?.splice(i - 1, 1);\n        i = null;\n      }\n    };\n    onScopeDispose(destroy);\n    return destroy;\n  };\n  script.onLoaded = (cb) => _registerCb(\"loaded\", cb);\n  script.onError = (cb) => _registerCb(\"error\", cb);\n  onScopeDispose(() => {\n    script._triggerAbortController?.abort();\n  });\n}\nfunction useScript(_input, _options) {\n  const input = typeof _input === \"string\" ? { src: _input } : _input;\n  const options = _options || {};\n  const head = options?.head || injectHead();\n  options.head = head;\n  const scope = getCurrentInstance();\n  options.eventContext = scope;\n  if (scope && typeof options.trigger === \"undefined\") {\n    options.trigger = onMounted;\n  } else if (isRef(options.trigger)) {\n    const refTrigger = options.trigger;\n    let off;\n    options.trigger = new Promise((resolve) => {\n      off = watch(refTrigger, (val) => {\n        if (val) {\n          resolve(true);\n        }\n      }, {\n        immediate: true\n      });\n      onScopeDispose(() => resolve(false), true);\n    }).then((val) => {\n      off?.();\n      return val;\n    });\n  }\n  head._scriptStatusWatcher = head._scriptStatusWatcher || head.hooks.hook(\"script:updated\", ({ script: s }) => {\n    s._statusRef.value = s.status;\n  });\n  const script = useScript$1(input, options);\n  script._statusRef = script._statusRef || ref(script.status);\n  registerVueScopeHandlers(script, scope);\n  return new Proxy(script, {\n    get(_, key, a) {\n      return Reflect.get(_, key === \"status\" ? \"_statusRef\" : key, a);\n    }\n  });\n}\n\nfunction useSeoMeta(input, options) {\n  const { title, titleTemplate, ...meta } = input;\n  return useHead({\n    title,\n    titleTemplate,\n    // @ts-expect-error runtime type\n    _flatMeta: meta\n  }, {\n    ...options,\n    transform(t) {\n      const meta2 = unpackMeta({ ...t._flatMeta });\n      delete t._flatMeta;\n      return {\n        // @ts-expect-error runtime type\n        ...t,\n        meta: meta2\n      };\n    }\n  });\n}\n\nfunction useServerHead(input, options = {}) {\n  const head = options.head || injectHead();\n  delete options.head;\n  if (head)\n    return head.push(input, { ...options, mode: \"server\" });\n}\n\nfunction useServerHeadSafe(input, options = {}) {\n  return useHeadSafe(input, { ...options, mode: \"server\" });\n}\n\nfunction useServerSeoMeta(input, options) {\n  return useSeoMeta(input, { ...options, mode: \"server\" });\n}\n\nconst Vue2ProvideUnheadPlugin = (_Vue, head) => {\n  _Vue.mixin({\n    beforeCreate() {\n      const options = this.$options;\n      const origProvide = options.provide;\n      options.provide = function() {\n        let origProvideResult;\n        if (typeof origProvide === \"function\")\n          origProvideResult = origProvide.call(this);\n        else\n          origProvideResult = origProvide || {};\n        return {\n          ...origProvideResult,\n          [headSymbol]: head\n        };\n      };\n    }\n  });\n};\n\nconst VueHeadMixin = {\n  created() {\n    let source = false;\n    if (Vue3) {\n      const instance = getCurrentInstance();\n      if (!instance)\n        return;\n      const options = instance.type;\n      if (!options || !(\"head\" in options))\n        return;\n      source = typeof options.head === \"function\" ? () => options.head.call(instance.proxy) : options.head;\n    } else {\n      const head = this.$options.head;\n      if (head) {\n        source = typeof head === \"function\" ? () => head.call(this) : head;\n      }\n    }\n    source && useHead(source);\n  }\n};\n\nexport { Vue2ProvideUnheadPlugin, VueHeadMixin, injectHead, unheadVueComposablesImports, useHead, useHeadSafe, useScript, useSeoMeta, useServerHead, useServerHeadSafe, useServerSeoMeta };\n", "export function getDevtoolsGlobalHook() {\n    return getTarget().__VUE_DEVTOOLS_GLOBAL_HOOK__;\n}\nexport function getTarget() {\n    // @ts-expect-error navigator and windows are not available in all environments\n    return (typeof navigator !== 'undefined' && typeof window !== 'undefined')\n        ? window\n        : typeof globalThis !== 'undefined'\n            ? globalThis\n            : {};\n}\nexport const isProxyAvailable = typeof Proxy === 'function';\n", "export const HOOK_SETUP = 'devtools-plugin:setup';\nexport const HOOK_PLUGIN_SETTINGS_SET = 'plugin:settings:set';\n", "let supported;\nlet perf;\nexport function isPerformanceSupported() {\n    var _a;\n    if (supported !== undefined) {\n        return supported;\n    }\n    if (typeof window !== 'undefined' && window.performance) {\n        supported = true;\n        perf = window.performance;\n    }\n    else if (typeof globalThis !== 'undefined' && ((_a = globalThis.perf_hooks) === null || _a === void 0 ? void 0 : _a.performance)) {\n        supported = true;\n        perf = globalThis.perf_hooks.performance;\n    }\n    else {\n        supported = false;\n    }\n    return supported;\n}\nexport function now() {\n    return isPerformanceSupported() ? perf.now() : Date.now();\n}\n", "import { HOOK_PLUGIN_SETTINGS_SET } from './const.js';\nimport { now } from './time.js';\nexport class ApiProxy {\n    constructor(plugin, hook) {\n        this.target = null;\n        this.targetQueue = [];\n        this.onQueue = [];\n        this.plugin = plugin;\n        this.hook = hook;\n        const defaultSettings = {};\n        if (plugin.settings) {\n            for (const id in plugin.settings) {\n                const item = plugin.settings[id];\n                defaultSettings[id] = item.defaultValue;\n            }\n        }\n        const localSettingsSaveId = `__vue-devtools-plugin-settings__${plugin.id}`;\n        let currentSettings = Object.assign({}, defaultSettings);\n        try {\n            const raw = localStorage.getItem(localSettingsSaveId);\n            const data = JSON.parse(raw);\n            Object.assign(currentSettings, data);\n        }\n        catch (e) {\n            // noop\n        }\n        this.fallbacks = {\n            getSettings() {\n                return currentSettings;\n            },\n            setSettings(value) {\n                try {\n                    localStorage.setItem(localSettingsSaveId, JSON.stringify(value));\n                }\n                catch (e) {\n                    // noop\n                }\n                currentSettings = value;\n            },\n            now() {\n                return now();\n            },\n        };\n        if (hook) {\n            hook.on(HOOK_PLUGIN_SETTINGS_SET, (pluginId, value) => {\n                if (pluginId === this.plugin.id) {\n                    this.fallbacks.setSettings(value);\n                }\n            });\n        }\n        this.proxiedOn = new Proxy({}, {\n            get: (_target, prop) => {\n                if (this.target) {\n                    return this.target.on[prop];\n                }\n                else {\n                    return (...args) => {\n                        this.onQueue.push({\n                            method: prop,\n                            args,\n                        });\n                    };\n                }\n            },\n        });\n        this.proxiedTarget = new Proxy({}, {\n            get: (_target, prop) => {\n                if (this.target) {\n                    return this.target[prop];\n                }\n                else if (prop === 'on') {\n                    return this.proxiedOn;\n                }\n                else if (Object.keys(this.fallbacks).includes(prop)) {\n                    return (...args) => {\n                        this.targetQueue.push({\n                            method: prop,\n                            args,\n                            resolve: () => { },\n                        });\n                        return this.fallbacks[prop](...args);\n                    };\n                }\n                else {\n                    return (...args) => {\n                        return new Promise((resolve) => {\n                            this.targetQueue.push({\n                                method: prop,\n                                args,\n                                resolve,\n                            });\n                        });\n                    };\n                }\n            },\n        });\n    }\n    async setRealTarget(target) {\n        this.target = target;\n        for (const item of this.onQueue) {\n            this.target.on[item.method](...item.args);\n        }\n        for (const item of this.targetQueue) {\n            item.resolve(await this.target[item.method](...item.args));\n        }\n    }\n}\n", "import { getDevtoolsGlobalHook, getTarget, isProxyAvailable } from './env.js';\nimport { HOOK_SETUP } from './const.js';\nimport { ApiProxy } from './proxy.js';\nexport * from './api/index.js';\nexport * from './plugin.js';\nexport * from './time.js';\nexport function setupDevtoolsPlugin(pluginDescriptor, setupFn) {\n    const descriptor = pluginDescriptor;\n    const target = getTarget();\n    const hook = getDevtoolsGlobalHook();\n    const enableProxy = isProxyAvailable && descriptor.enableEarlyProxy;\n    if (hook && (target.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__ || !enableProxy)) {\n        hook.emit(HOOK_SETUP, pluginDescriptor, setupFn);\n    }\n    else {\n        const proxy = enableProxy ? new ApiProxy(descriptor, hook) : null;\n        const list = target.__VUE_DEVTOOLS_PLUGINS__ = target.__VUE_DEVTOOLS_PLUGINS__ || [];\n        list.push({\n            pluginDescriptor: descriptor,\n            setupFn,\n            proxy,\n        });\n        if (proxy) {\n            setupFn(proxy.proxiedTarget);\n        }\n    }\n}\n", "/*!\n  * vue-router v4.5.1\n  * (c) 2025 <PERSON>\n  * @license MIT\n  */\nimport { getCurrentInstance, inject, onUnmounted, onDeactivated, onActivated, computed, unref, watchEffect, defineComponent, reactive, h, provide, ref, watch, shallowRef, shallowReactive, nextTick } from 'vue';\nimport { setupDevtoolsPlugin } from '@vue/devtools-api';\n\nconst isBrowser = typeof document !== 'undefined';\n\n/**\n * Allows differentiating lazy components from functional components and vue-class-component\n * @internal\n *\n * @param component\n */\nfunction isRouteComponent(component) {\n    return (typeof component === 'object' ||\n        'displayName' in component ||\n        'props' in component ||\n        '__vccOpts' in component);\n}\nfunction isESModule(obj) {\n    return (obj.__esModule ||\n        obj[Symbol.toStringTag] === 'Module' ||\n        // support CF with dynamic imports that do not\n        // add the Module string tag\n        (obj.default && isRouteComponent(obj.default)));\n}\nconst assign = Object.assign;\nfunction applyToParams(fn, params) {\n    const newParams = {};\n    for (const key in params) {\n        const value = params[key];\n        newParams[key] = isArray(value)\n            ? value.map(fn)\n            : fn(value);\n    }\n    return newParams;\n}\nconst noop = () => { };\n/**\n * Typesafe alternative to Array.isArray\n * https://github.com/microsoft/TypeScript/pull/48228\n */\nconst isArray = Array.isArray;\n\nfunction warn(msg) {\n    // avoid using ...args as it breaks in older Edge builds\n    const args = Array.from(arguments).slice(1);\n    console.warn.apply(console, ['[Vue Router warn]: ' + msg].concat(args));\n}\n\n/**\n * Encoding Rules (␣ = Space)\n * - Path: ␣ \" < > # ? { }\n * - Query: ␣ \" < > # & =\n * - Hash: ␣ \" < > `\n *\n * On top of that, the RFC3986 (https://tools.ietf.org/html/rfc3986#section-2.2)\n * defines some extra characters to be encoded. Most browsers do not encode them\n * in encodeURI https://github.com/whatwg/url/issues/369, so it may be safer to\n * also encode `!'()*`. Leaving un-encoded only ASCII alphanumeric(`a-zA-Z0-9`)\n * plus `-._~`. This extra safety should be applied to query by patching the\n * string returned by encodeURIComponent encodeURI also encodes `[\\]^`. `\\`\n * should be encoded to avoid ambiguity. Browsers (IE, FF, C) transform a `\\`\n * into a `/` if directly typed in. The _backtick_ (`````) should also be\n * encoded everywhere because some browsers like FF encode it when directly\n * written while others don't. Safari and IE don't encode ``\"<>{}``` in hash.\n */\n// const EXTRA_RESERVED_RE = /[!'()*]/g\n// const encodeReservedReplacer = (c: string) => '%' + c.charCodeAt(0).toString(16)\nconst HASH_RE = /#/g; // %23\nconst AMPERSAND_RE = /&/g; // %26\nconst SLASH_RE = /\\//g; // %2F\nconst EQUAL_RE = /=/g; // %3D\nconst IM_RE = /\\?/g; // %3F\nconst PLUS_RE = /\\+/g; // %2B\n/**\n * NOTE: It's not clear to me if we should encode the + symbol in queries, it\n * seems to be less flexible than not doing so and I can't find out the legacy\n * systems requiring this for regular requests like text/html. In the standard,\n * the encoding of the plus character is only mentioned for\n * application/x-www-form-urlencoded\n * (https://url.spec.whatwg.org/#urlencoded-parsing) and most browsers seems lo\n * leave the plus character as is in queries. To be more flexible, we allow the\n * plus character on the query, but it can also be manually encoded by the user.\n *\n * Resources:\n * - https://url.spec.whatwg.org/#urlencoded-parsing\n * - https://stackoverflow.com/questions/1634271/url-encoding-the-space-character-or-20\n */\nconst ENC_BRACKET_OPEN_RE = /%5B/g; // [\nconst ENC_BRACKET_CLOSE_RE = /%5D/g; // ]\nconst ENC_CARET_RE = /%5E/g; // ^\nconst ENC_BACKTICK_RE = /%60/g; // `\nconst ENC_CURLY_OPEN_RE = /%7B/g; // {\nconst ENC_PIPE_RE = /%7C/g; // |\nconst ENC_CURLY_CLOSE_RE = /%7D/g; // }\nconst ENC_SPACE_RE = /%20/g; // }\n/**\n * Encode characters that need to be encoded on the path, search and hash\n * sections of the URL.\n *\n * @internal\n * @param text - string to encode\n * @returns encoded string\n */\nfunction commonEncode(text) {\n    return encodeURI('' + text)\n        .replace(ENC_PIPE_RE, '|')\n        .replace(ENC_BRACKET_OPEN_RE, '[')\n        .replace(ENC_BRACKET_CLOSE_RE, ']');\n}\n/**\n * Encode characters that need to be encoded on the hash section of the URL.\n *\n * @param text - string to encode\n * @returns encoded string\n */\nfunction encodeHash(text) {\n    return commonEncode(text)\n        .replace(ENC_CURLY_OPEN_RE, '{')\n        .replace(ENC_CURLY_CLOSE_RE, '}')\n        .replace(ENC_CARET_RE, '^');\n}\n/**\n * Encode characters that need to be encoded query values on the query\n * section of the URL.\n *\n * @param text - string to encode\n * @returns encoded string\n */\nfunction encodeQueryValue(text) {\n    return (commonEncode(text)\n        // Encode the space as +, encode the + to differentiate it from the space\n        .replace(PLUS_RE, '%2B')\n        .replace(ENC_SPACE_RE, '+')\n        .replace(HASH_RE, '%23')\n        .replace(AMPERSAND_RE, '%26')\n        .replace(ENC_BACKTICK_RE, '`')\n        .replace(ENC_CURLY_OPEN_RE, '{')\n        .replace(ENC_CURLY_CLOSE_RE, '}')\n        .replace(ENC_CARET_RE, '^'));\n}\n/**\n * Like `encodeQueryValue` but also encodes the `=` character.\n *\n * @param text - string to encode\n */\nfunction encodeQueryKey(text) {\n    return encodeQueryValue(text).replace(EQUAL_RE, '%3D');\n}\n/**\n * Encode characters that need to be encoded on the path section of the URL.\n *\n * @param text - string to encode\n * @returns encoded string\n */\nfunction encodePath(text) {\n    return commonEncode(text).replace(HASH_RE, '%23').replace(IM_RE, '%3F');\n}\n/**\n * Encode characters that need to be encoded on the path section of the URL as a\n * param. This function encodes everything {@link encodePath} does plus the\n * slash (`/`) character. If `text` is `null` or `undefined`, returns an empty\n * string instead.\n *\n * @param text - string to encode\n * @returns encoded string\n */\nfunction encodeParam(text) {\n    return text == null ? '' : encodePath(text).replace(SLASH_RE, '%2F');\n}\n/**\n * Decode text using `decodeURIComponent`. Returns the original text if it\n * fails.\n *\n * @param text - string to decode\n * @returns decoded string\n */\nfunction decode(text) {\n    try {\n        return decodeURIComponent('' + text);\n    }\n    catch (err) {\n        (process.env.NODE_ENV !== 'production') && warn(`Error decoding \"${text}\". Using original value`);\n    }\n    return '' + text;\n}\n\nconst TRAILING_SLASH_RE = /\\/$/;\nconst removeTrailingSlash = (path) => path.replace(TRAILING_SLASH_RE, '');\n/**\n * Transforms a URI into a normalized history location\n *\n * @param parseQuery\n * @param location - URI to normalize\n * @param currentLocation - current absolute location. Allows resolving relative\n * paths. Must start with `/`. Defaults to `/`\n * @returns a normalized history location\n */\nfunction parseURL(parseQuery, location, currentLocation = '/') {\n    let path, query = {}, searchString = '', hash = '';\n    // Could use URL and URLSearchParams but IE 11 doesn't support it\n    // TODO: move to new URL()\n    const hashPos = location.indexOf('#');\n    let searchPos = location.indexOf('?');\n    // the hash appears before the search, so it's not part of the search string\n    if (hashPos < searchPos && hashPos >= 0) {\n        searchPos = -1;\n    }\n    if (searchPos > -1) {\n        path = location.slice(0, searchPos);\n        searchString = location.slice(searchPos + 1, hashPos > -1 ? hashPos : location.length);\n        query = parseQuery(searchString);\n    }\n    if (hashPos > -1) {\n        path = path || location.slice(0, hashPos);\n        // keep the # character\n        hash = location.slice(hashPos, location.length);\n    }\n    // no search and no query\n    path = resolveRelativePath(path != null ? path : location, currentLocation);\n    // empty path means a relative query or hash `?foo=f`, `#thing`\n    return {\n        fullPath: path + (searchString && '?') + searchString + hash,\n        path,\n        query,\n        hash: decode(hash),\n    };\n}\n/**\n * Stringifies a URL object\n *\n * @param stringifyQuery\n * @param location\n */\nfunction stringifyURL(stringifyQuery, location) {\n    const query = location.query ? stringifyQuery(location.query) : '';\n    return location.path + (query && '?') + query + (location.hash || '');\n}\n/**\n * Strips off the base from the beginning of a location.pathname in a non-case-sensitive way.\n *\n * @param pathname - location.pathname\n * @param base - base to strip off\n */\nfunction stripBase(pathname, base) {\n    // no base or base is not found at the beginning\n    if (!base || !pathname.toLowerCase().startsWith(base.toLowerCase()))\n        return pathname;\n    return pathname.slice(base.length) || '/';\n}\n/**\n * Checks if two RouteLocation are equal. This means that both locations are\n * pointing towards the same {@link RouteRecord} and that all `params`, `query`\n * parameters and `hash` are the same\n *\n * @param stringifyQuery - A function that takes a query object of type LocationQueryRaw and returns a string representation of it.\n * @param a - first {@link RouteLocation}\n * @param b - second {@link RouteLocation}\n */\nfunction isSameRouteLocation(stringifyQuery, a, b) {\n    const aLastIndex = a.matched.length - 1;\n    const bLastIndex = b.matched.length - 1;\n    return (aLastIndex > -1 &&\n        aLastIndex === bLastIndex &&\n        isSameRouteRecord(a.matched[aLastIndex], b.matched[bLastIndex]) &&\n        isSameRouteLocationParams(a.params, b.params) &&\n        stringifyQuery(a.query) === stringifyQuery(b.query) &&\n        a.hash === b.hash);\n}\n/**\n * Check if two `RouteRecords` are equal. Takes into account aliases: they are\n * considered equal to the `RouteRecord` they are aliasing.\n *\n * @param a - first {@link RouteRecord}\n * @param b - second {@link RouteRecord}\n */\nfunction isSameRouteRecord(a, b) {\n    // since the original record has an undefined value for aliasOf\n    // but all aliases point to the original record, this will always compare\n    // the original record\n    return (a.aliasOf || a) === (b.aliasOf || b);\n}\nfunction isSameRouteLocationParams(a, b) {\n    if (Object.keys(a).length !== Object.keys(b).length)\n        return false;\n    for (const key in a) {\n        if (!isSameRouteLocationParamsValue(a[key], b[key]))\n            return false;\n    }\n    return true;\n}\nfunction isSameRouteLocationParamsValue(a, b) {\n    return isArray(a)\n        ? isEquivalentArray(a, b)\n        : isArray(b)\n            ? isEquivalentArray(b, a)\n            : a === b;\n}\n/**\n * Check if two arrays are the same or if an array with one single entry is the\n * same as another primitive value. Used to check query and parameters\n *\n * @param a - array of values\n * @param b - array of values or a single value\n */\nfunction isEquivalentArray(a, b) {\n    return isArray(b)\n        ? a.length === b.length && a.every((value, i) => value === b[i])\n        : a.length === 1 && a[0] === b;\n}\n/**\n * Resolves a relative path that starts with `.`.\n *\n * @param to - path location we are resolving\n * @param from - currentLocation.path, should start with `/`\n */\nfunction resolveRelativePath(to, from) {\n    if (to.startsWith('/'))\n        return to;\n    if ((process.env.NODE_ENV !== 'production') && !from.startsWith('/')) {\n        warn(`Cannot resolve a relative location without an absolute path. Trying to resolve \"${to}\" from \"${from}\". It should look like \"/${from}\".`);\n        return to;\n    }\n    if (!to)\n        return from;\n    const fromSegments = from.split('/');\n    const toSegments = to.split('/');\n    const lastToSegment = toSegments[toSegments.length - 1];\n    // make . and ./ the same (../ === .., ../../ === ../..)\n    // this is the same behavior as new URL()\n    if (lastToSegment === '..' || lastToSegment === '.') {\n        toSegments.push('');\n    }\n    let position = fromSegments.length - 1;\n    let toPosition;\n    let segment;\n    for (toPosition = 0; toPosition < toSegments.length; toPosition++) {\n        segment = toSegments[toPosition];\n        // we stay on the same position\n        if (segment === '.')\n            continue;\n        // go up in the from array\n        if (segment === '..') {\n            // we can't go below zero, but we still need to increment toPosition\n            if (position > 1)\n                position--;\n            // continue\n        }\n        // we reached a non-relative path, we stop here\n        else\n            break;\n    }\n    return (fromSegments.slice(0, position).join('/') +\n        '/' +\n        toSegments.slice(toPosition).join('/'));\n}\n/**\n * Initial route location where the router is. Can be used in navigation guards\n * to differentiate the initial navigation.\n *\n * @example\n * ```js\n * import { START_LOCATION } from 'vue-router'\n *\n * router.beforeEach((to, from) => {\n *   if (from === START_LOCATION) {\n *     // initial navigation\n *   }\n * })\n * ```\n */\nconst START_LOCATION_NORMALIZED = {\n    path: '/',\n    // TODO: could we use a symbol in the future?\n    name: undefined,\n    params: {},\n    query: {},\n    hash: '',\n    fullPath: '/',\n    matched: [],\n    meta: {},\n    redirectedFrom: undefined,\n};\n\nvar NavigationType;\n(function (NavigationType) {\n    NavigationType[\"pop\"] = \"pop\";\n    NavigationType[\"push\"] = \"push\";\n})(NavigationType || (NavigationType = {}));\nvar NavigationDirection;\n(function (NavigationDirection) {\n    NavigationDirection[\"back\"] = \"back\";\n    NavigationDirection[\"forward\"] = \"forward\";\n    NavigationDirection[\"unknown\"] = \"\";\n})(NavigationDirection || (NavigationDirection = {}));\n/**\n * Starting location for Histories\n */\nconst START = '';\n// Generic utils\n/**\n * Normalizes a base by removing any trailing slash and reading the base tag if\n * present.\n *\n * @param base - base to normalize\n */\nfunction normalizeBase(base) {\n    if (!base) {\n        if (isBrowser) {\n            // respect <base> tag\n            const baseEl = document.querySelector('base');\n            base = (baseEl && baseEl.getAttribute('href')) || '/';\n            // strip full URL origin\n            base = base.replace(/^\\w+:\\/\\/[^\\/]+/, '');\n        }\n        else {\n            base = '/';\n        }\n    }\n    // ensure leading slash when it was removed by the regex above avoid leading\n    // slash with hash because the file could be read from the disk like file://\n    // and the leading slash would cause problems\n    if (base[0] !== '/' && base[0] !== '#')\n        base = '/' + base;\n    // remove the trailing slash so all other method can just do `base + fullPath`\n    // to build an href\n    return removeTrailingSlash(base);\n}\n// remove any character before the hash\nconst BEFORE_HASH_RE = /^[^#]+#/;\nfunction createHref(base, location) {\n    return base.replace(BEFORE_HASH_RE, '#') + location;\n}\n\nfunction getElementPosition(el, offset) {\n    const docRect = document.documentElement.getBoundingClientRect();\n    const elRect = el.getBoundingClientRect();\n    return {\n        behavior: offset.behavior,\n        left: elRect.left - docRect.left - (offset.left || 0),\n        top: elRect.top - docRect.top - (offset.top || 0),\n    };\n}\nconst computeScrollPosition = () => ({\n    left: window.scrollX,\n    top: window.scrollY,\n});\nfunction scrollToPosition(position) {\n    let scrollToOptions;\n    if ('el' in position) {\n        const positionEl = position.el;\n        const isIdSelector = typeof positionEl === 'string' && positionEl.startsWith('#');\n        /**\n         * `id`s can accept pretty much any characters, including CSS combinators\n         * like `>` or `~`. It's still possible to retrieve elements using\n         * `document.getElementById('~')` but it needs to be escaped when using\n         * `document.querySelector('#\\\\~')` for it to be valid. The only\n         * requirements for `id`s are them to be unique on the page and to not be\n         * empty (`id=\"\"`). Because of that, when passing an id selector, it should\n         * be properly escaped for it to work with `querySelector`. We could check\n         * for the id selector to be simple (no CSS combinators `+ >~`) but that\n         * would make things inconsistent since they are valid characters for an\n         * `id` but would need to be escaped when using `querySelector`, breaking\n         * their usage and ending up in no selector returned. Selectors need to be\n         * escaped:\n         *\n         * - `#1-thing` becomes `#\\31 -thing`\n         * - `#with~symbols` becomes `#with\\\\~symbols`\n         *\n         * - More information about  the topic can be found at\n         *   https://mathiasbynens.be/notes/html5-id-class.\n         * - Practical example: https://mathiasbynens.be/demo/html5-id\n         */\n        if ((process.env.NODE_ENV !== 'production') && typeof position.el === 'string') {\n            if (!isIdSelector || !document.getElementById(position.el.slice(1))) {\n                try {\n                    const foundEl = document.querySelector(position.el);\n                    if (isIdSelector && foundEl) {\n                        warn(`The selector \"${position.el}\" should be passed as \"el: document.querySelector('${position.el}')\" because it starts with \"#\".`);\n                        // return to avoid other warnings\n                        return;\n                    }\n                }\n                catch (err) {\n                    warn(`The selector \"${position.el}\" is invalid. If you are using an id selector, make sure to escape it. You can find more information about escaping characters in selectors at https://mathiasbynens.be/notes/css-escapes or use CSS.escape (https://developer.mozilla.org/en-US/docs/Web/API/CSS/escape).`);\n                    // return to avoid other warnings\n                    return;\n                }\n            }\n        }\n        const el = typeof positionEl === 'string'\n            ? isIdSelector\n                ? document.getElementById(positionEl.slice(1))\n                : document.querySelector(positionEl)\n            : positionEl;\n        if (!el) {\n            (process.env.NODE_ENV !== 'production') &&\n                warn(`Couldn't find element using selector \"${position.el}\" returned by scrollBehavior.`);\n            return;\n        }\n        scrollToOptions = getElementPosition(el, position);\n    }\n    else {\n        scrollToOptions = position;\n    }\n    if ('scrollBehavior' in document.documentElement.style)\n        window.scrollTo(scrollToOptions);\n    else {\n        window.scrollTo(scrollToOptions.left != null ? scrollToOptions.left : window.scrollX, scrollToOptions.top != null ? scrollToOptions.top : window.scrollY);\n    }\n}\nfunction getScrollKey(path, delta) {\n    const position = history.state ? history.state.position - delta : -1;\n    return position + path;\n}\nconst scrollPositions = new Map();\nfunction saveScrollPosition(key, scrollPosition) {\n    scrollPositions.set(key, scrollPosition);\n}\nfunction getSavedScrollPosition(key) {\n    const scroll = scrollPositions.get(key);\n    // consume it so it's not used again\n    scrollPositions.delete(key);\n    return scroll;\n}\n// TODO: RFC about how to save scroll position\n/**\n * ScrollBehavior instance used by the router to compute and restore the scroll\n * position when navigating.\n */\n// export interface ScrollHandler<ScrollPositionEntry extends HistoryStateValue, ScrollPosition extends ScrollPositionEntry> {\n//   // returns a scroll position that can be saved in history\n//   compute(): ScrollPositionEntry\n//   // can take an extended ScrollPositionEntry\n//   scroll(position: ScrollPosition): void\n// }\n// export const scrollHandler: ScrollHandler<ScrollPosition> = {\n//   compute: computeScroll,\n//   scroll: scrollToPosition,\n// }\n\nlet createBaseLocation = () => location.protocol + '//' + location.host;\n/**\n * Creates a normalized history location from a window.location object\n * @param base - The base path\n * @param location - The window.location object\n */\nfunction createCurrentLocation(base, location) {\n    const { pathname, search, hash } = location;\n    // allows hash bases like #, /#, #/, #!, #!/, /#!/, or even /folder#end\n    const hashPos = base.indexOf('#');\n    if (hashPos > -1) {\n        let slicePos = hash.includes(base.slice(hashPos))\n            ? base.slice(hashPos).length\n            : 1;\n        let pathFromHash = hash.slice(slicePos);\n        // prepend the starting slash to hash so the url starts with /#\n        if (pathFromHash[0] !== '/')\n            pathFromHash = '/' + pathFromHash;\n        return stripBase(pathFromHash, '');\n    }\n    const path = stripBase(pathname, base);\n    return path + search + hash;\n}\nfunction useHistoryListeners(base, historyState, currentLocation, replace) {\n    let listeners = [];\n    let teardowns = [];\n    // TODO: should it be a stack? a Dict. Check if the popstate listener\n    // can trigger twice\n    let pauseState = null;\n    const popStateHandler = ({ state, }) => {\n        const to = createCurrentLocation(base, location);\n        const from = currentLocation.value;\n        const fromState = historyState.value;\n        let delta = 0;\n        if (state) {\n            currentLocation.value = to;\n            historyState.value = state;\n            // ignore the popstate and reset the pauseState\n            if (pauseState && pauseState === from) {\n                pauseState = null;\n                return;\n            }\n            delta = fromState ? state.position - fromState.position : 0;\n        }\n        else {\n            replace(to);\n        }\n        // Here we could also revert the navigation by calling history.go(-delta)\n        // this listener will have to be adapted to not trigger again and to wait for the url\n        // to be updated before triggering the listeners. Some kind of validation function would also\n        // need to be passed to the listeners so the navigation can be accepted\n        // call all listeners\n        listeners.forEach(listener => {\n            listener(currentLocation.value, from, {\n                delta,\n                type: NavigationType.pop,\n                direction: delta\n                    ? delta > 0\n                        ? NavigationDirection.forward\n                        : NavigationDirection.back\n                    : NavigationDirection.unknown,\n            });\n        });\n    };\n    function pauseListeners() {\n        pauseState = currentLocation.value;\n    }\n    function listen(callback) {\n        // set up the listener and prepare teardown callbacks\n        listeners.push(callback);\n        const teardown = () => {\n            const index = listeners.indexOf(callback);\n            if (index > -1)\n                listeners.splice(index, 1);\n        };\n        teardowns.push(teardown);\n        return teardown;\n    }\n    function beforeUnloadListener() {\n        const { history } = window;\n        if (!history.state)\n            return;\n        history.replaceState(assign({}, history.state, { scroll: computeScrollPosition() }), '');\n    }\n    function destroy() {\n        for (const teardown of teardowns)\n            teardown();\n        teardowns = [];\n        window.removeEventListener('popstate', popStateHandler);\n        window.removeEventListener('beforeunload', beforeUnloadListener);\n    }\n    // set up the listeners and prepare teardown callbacks\n    window.addEventListener('popstate', popStateHandler);\n    // TODO: could we use 'pagehide' or 'visibilitychange' instead?\n    // https://developer.chrome.com/blog/page-lifecycle-api/\n    window.addEventListener('beforeunload', beforeUnloadListener, {\n        passive: true,\n    });\n    return {\n        pauseListeners,\n        listen,\n        destroy,\n    };\n}\n/**\n * Creates a state object\n */\nfunction buildState(back, current, forward, replaced = false, computeScroll = false) {\n    return {\n        back,\n        current,\n        forward,\n        replaced,\n        position: window.history.length,\n        scroll: computeScroll ? computeScrollPosition() : null,\n    };\n}\nfunction useHistoryStateNavigation(base) {\n    const { history, location } = window;\n    // private variables\n    const currentLocation = {\n        value: createCurrentLocation(base, location),\n    };\n    const historyState = { value: history.state };\n    // build current history entry as this is a fresh navigation\n    if (!historyState.value) {\n        changeLocation(currentLocation.value, {\n            back: null,\n            current: currentLocation.value,\n            forward: null,\n            // the length is off by one, we need to decrease it\n            position: history.length - 1,\n            replaced: true,\n            // don't add a scroll as the user may have an anchor, and we want\n            // scrollBehavior to be triggered without a saved position\n            scroll: null,\n        }, true);\n    }\n    function changeLocation(to, state, replace) {\n        /**\n         * if a base tag is provided, and we are on a normal domain, we have to\n         * respect the provided `base` attribute because pushState() will use it and\n         * potentially erase anything before the `#` like at\n         * https://github.com/vuejs/router/issues/685 where a base of\n         * `/folder/#` but a base of `/` would erase the `/folder/` section. If\n         * there is no host, the `<base>` tag makes no sense and if there isn't a\n         * base tag we can just use everything after the `#`.\n         */\n        const hashIndex = base.indexOf('#');\n        const url = hashIndex > -1\n            ? (location.host && document.querySelector('base')\n                ? base\n                : base.slice(hashIndex)) + to\n            : createBaseLocation() + base + to;\n        try {\n            // BROWSER QUIRK\n            // NOTE: Safari throws a SecurityError when calling this function 100 times in 30 seconds\n            history[replace ? 'replaceState' : 'pushState'](state, '', url);\n            historyState.value = state;\n        }\n        catch (err) {\n            if ((process.env.NODE_ENV !== 'production')) {\n                warn('Error with push/replace State', err);\n            }\n            else {\n                console.error(err);\n            }\n            // Force the navigation, this also resets the call count\n            location[replace ? 'replace' : 'assign'](url);\n        }\n    }\n    function replace(to, data) {\n        const state = assign({}, history.state, buildState(historyState.value.back, \n        // keep back and forward entries but override current position\n        to, historyState.value.forward, true), data, { position: historyState.value.position });\n        changeLocation(to, state, true);\n        currentLocation.value = to;\n    }\n    function push(to, data) {\n        // Add to current entry the information of where we are going\n        // as well as saving the current position\n        const currentState = assign({}, \n        // use current history state to gracefully handle a wrong call to\n        // history.replaceState\n        // https://github.com/vuejs/router/issues/366\n        historyState.value, history.state, {\n            forward: to,\n            scroll: computeScrollPosition(),\n        });\n        if ((process.env.NODE_ENV !== 'production') && !history.state) {\n            warn(`history.state seems to have been manually replaced without preserving the necessary values. Make sure to preserve existing history state if you are manually calling history.replaceState:\\n\\n` +\n                `history.replaceState(history.state, '', url)\\n\\n` +\n                `You can find more information at https://router.vuejs.org/guide/migration/#Usage-of-history-state`);\n        }\n        changeLocation(currentState.current, currentState, true);\n        const state = assign({}, buildState(currentLocation.value, to, null), { position: currentState.position + 1 }, data);\n        changeLocation(to, state, false);\n        currentLocation.value = to;\n    }\n    return {\n        location: currentLocation,\n        state: historyState,\n        push,\n        replace,\n    };\n}\n/**\n * Creates an HTML5 history. Most common history for single page applications.\n *\n * @param base -\n */\nfunction createWebHistory(base) {\n    base = normalizeBase(base);\n    const historyNavigation = useHistoryStateNavigation(base);\n    const historyListeners = useHistoryListeners(base, historyNavigation.state, historyNavigation.location, historyNavigation.replace);\n    function go(delta, triggerListeners = true) {\n        if (!triggerListeners)\n            historyListeners.pauseListeners();\n        history.go(delta);\n    }\n    const routerHistory = assign({\n        // it's overridden right after\n        location: '',\n        base,\n        go,\n        createHref: createHref.bind(null, base),\n    }, historyNavigation, historyListeners);\n    Object.defineProperty(routerHistory, 'location', {\n        enumerable: true,\n        get: () => historyNavigation.location.value,\n    });\n    Object.defineProperty(routerHistory, 'state', {\n        enumerable: true,\n        get: () => historyNavigation.state.value,\n    });\n    return routerHistory;\n}\n\n/**\n * Creates an in-memory based history. The main purpose of this history is to handle SSR. It starts in a special location that is nowhere.\n * It's up to the user to replace that location with the starter location by either calling `router.push` or `router.replace`.\n *\n * @param base - Base applied to all urls, defaults to '/'\n * @returns a history object that can be passed to the router constructor\n */\nfunction createMemoryHistory(base = '') {\n    let listeners = [];\n    let queue = [[START, {}]];\n    let position = 0;\n    base = normalizeBase(base);\n    function setLocation(location, state = {}) {\n        position++;\n        if (position !== queue.length) {\n            // we are in the middle, we remove everything from here in the queue\n            queue.splice(position);\n        }\n        queue.push([location, state]);\n    }\n    function triggerListeners(to, from, { direction, delta }) {\n        const info = {\n            direction,\n            delta,\n            type: NavigationType.pop,\n        };\n        for (const callback of listeners) {\n            callback(to, from, info);\n        }\n    }\n    const routerHistory = {\n        // rewritten by Object.defineProperty\n        location: START,\n        // rewritten by Object.defineProperty\n        state: {},\n        base,\n        createHref: createHref.bind(null, base),\n        replace(to, state) {\n            // remove current entry and decrement position\n            queue.splice(position--, 1);\n            setLocation(to, state);\n        },\n        push(to, state) {\n            setLocation(to, state);\n        },\n        listen(callback) {\n            listeners.push(callback);\n            return () => {\n                const index = listeners.indexOf(callback);\n                if (index > -1)\n                    listeners.splice(index, 1);\n            };\n        },\n        destroy() {\n            listeners = [];\n            queue = [[START, {}]];\n            position = 0;\n        },\n        go(delta, shouldTrigger = true) {\n            const from = this.location;\n            const direction = \n            // we are considering delta === 0 going forward, but in abstract mode\n            // using 0 for the delta doesn't make sense like it does in html5 where\n            // it reloads the page\n            delta < 0 ? NavigationDirection.back : NavigationDirection.forward;\n            position = Math.max(0, Math.min(position + delta, queue.length - 1));\n            if (shouldTrigger) {\n                triggerListeners(this.location, from, {\n                    direction,\n                    delta,\n                });\n            }\n        },\n    };\n    Object.defineProperty(routerHistory, 'location', {\n        enumerable: true,\n        get: () => queue[position][0],\n    });\n    Object.defineProperty(routerHistory, 'state', {\n        enumerable: true,\n        get: () => queue[position][1],\n    });\n    return routerHistory;\n}\n\n/**\n * Creates a hash history. Useful for web applications with no host (e.g. `file://`) or when configuring a server to\n * handle any URL is not possible.\n *\n * @param base - optional base to provide. Defaults to `location.pathname + location.search` If there is a `<base>` tag\n * in the `head`, its value will be ignored in favor of this parameter **but note it affects all the history.pushState()\n * calls**, meaning that if you use a `<base>` tag, it's `href` value **has to match this parameter** (ignoring anything\n * after the `#`).\n *\n * @example\n * ```js\n * // at https://example.com/folder\n * createWebHashHistory() // gives a url of `https://example.com/folder#`\n * createWebHashHistory('/folder/') // gives a url of `https://example.com/folder/#`\n * // if the `#` is provided in the base, it won't be added by `createWebHashHistory`\n * createWebHashHistory('/folder/#/app/') // gives a url of `https://example.com/folder/#/app/`\n * // you should avoid doing this because it changes the original url and breaks copying urls\n * createWebHashHistory('/other-folder/') // gives a url of `https://example.com/other-folder/#`\n *\n * // at file:///usr/etc/folder/index.html\n * // for locations with no `host`, the base is ignored\n * createWebHashHistory('/iAmIgnored') // gives a url of `file:///usr/etc/folder/index.html#`\n * ```\n */\nfunction createWebHashHistory(base) {\n    // Make sure this implementation is fine in terms of encoding, specially for IE11\n    // for `file://`, directly use the pathname and ignore the base\n    // location.pathname contains an initial `/` even at the root: `https://example.com`\n    base = location.host ? base || location.pathname + location.search : '';\n    // allow the user to provide a `#` in the middle: `/base/#/app`\n    if (!base.includes('#'))\n        base += '#';\n    if ((process.env.NODE_ENV !== 'production') && !base.endsWith('#/') && !base.endsWith('#')) {\n        warn(`A hash base must end with a \"#\":\\n\"${base}\" should be \"${base.replace(/#.*$/, '#')}\".`);\n    }\n    return createWebHistory(base);\n}\n\nfunction isRouteLocation(route) {\n    return typeof route === 'string' || (route && typeof route === 'object');\n}\nfunction isRouteName(name) {\n    return typeof name === 'string' || typeof name === 'symbol';\n}\n\nconst NavigationFailureSymbol = Symbol((process.env.NODE_ENV !== 'production') ? 'navigation failure' : '');\n/**\n * Enumeration with all possible types for navigation failures. Can be passed to\n * {@link isNavigationFailure} to check for specific failures.\n */\nvar NavigationFailureType;\n(function (NavigationFailureType) {\n    /**\n     * An aborted navigation is a navigation that failed because a navigation\n     * guard returned `false` or called `next(false)`\n     */\n    NavigationFailureType[NavigationFailureType[\"aborted\"] = 4] = \"aborted\";\n    /**\n     * A cancelled navigation is a navigation that failed because a more recent\n     * navigation finished started (not necessarily finished).\n     */\n    NavigationFailureType[NavigationFailureType[\"cancelled\"] = 8] = \"cancelled\";\n    /**\n     * A duplicated navigation is a navigation that failed because it was\n     * initiated while already being at the exact same location.\n     */\n    NavigationFailureType[NavigationFailureType[\"duplicated\"] = 16] = \"duplicated\";\n})(NavigationFailureType || (NavigationFailureType = {}));\n// DEV only debug messages\nconst ErrorTypeMessages = {\n    [1 /* ErrorTypes.MATCHER_NOT_FOUND */]({ location, currentLocation }) {\n        return `No match for\\n ${JSON.stringify(location)}${currentLocation\n            ? '\\nwhile being at\\n' + JSON.stringify(currentLocation)\n            : ''}`;\n    },\n    [2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */]({ from, to, }) {\n        return `Redirected from \"${from.fullPath}\" to \"${stringifyRoute(to)}\" via a navigation guard.`;\n    },\n    [4 /* ErrorTypes.NAVIGATION_ABORTED */]({ from, to }) {\n        return `Navigation aborted from \"${from.fullPath}\" to \"${to.fullPath}\" via a navigation guard.`;\n    },\n    [8 /* ErrorTypes.NAVIGATION_CANCELLED */]({ from, to }) {\n        return `Navigation cancelled from \"${from.fullPath}\" to \"${to.fullPath}\" with a new navigation.`;\n    },\n    [16 /* ErrorTypes.NAVIGATION_DUPLICATED */]({ from, to }) {\n        return `Avoided redundant navigation to current location: \"${from.fullPath}\".`;\n    },\n};\n/**\n * Creates a typed NavigationFailure object.\n * @internal\n * @param type - NavigationFailureType\n * @param params - { from, to }\n */\nfunction createRouterError(type, params) {\n    // keep full error messages in cjs versions\n    if ((process.env.NODE_ENV !== 'production') || !true) {\n        return assign(new Error(ErrorTypeMessages[type](params)), {\n            type,\n            [NavigationFailureSymbol]: true,\n        }, params);\n    }\n    else {\n        return assign(new Error(), {\n            type,\n            [NavigationFailureSymbol]: true,\n        }, params);\n    }\n}\nfunction isNavigationFailure(error, type) {\n    return (error instanceof Error &&\n        NavigationFailureSymbol in error &&\n        (type == null || !!(error.type & type)));\n}\nconst propertiesToLog = ['params', 'query', 'hash'];\nfunction stringifyRoute(to) {\n    if (typeof to === 'string')\n        return to;\n    if (to.path != null)\n        return to.path;\n    const location = {};\n    for (const key of propertiesToLog) {\n        if (key in to)\n            location[key] = to[key];\n    }\n    return JSON.stringify(location, null, 2);\n}\n\n// default pattern for a param: non-greedy everything but /\nconst BASE_PARAM_PATTERN = '[^/]+?';\nconst BASE_PATH_PARSER_OPTIONS = {\n    sensitive: false,\n    strict: false,\n    start: true,\n    end: true,\n};\n// Special Regex characters that must be escaped in static tokens\nconst REGEX_CHARS_RE = /[.+*?^${}()[\\]/\\\\]/g;\n/**\n * Creates a path parser from an array of Segments (a segment is an array of Tokens)\n *\n * @param segments - array of segments returned by tokenizePath\n * @param extraOptions - optional options for the regexp\n * @returns a PathParser\n */\nfunction tokensToParser(segments, extraOptions) {\n    const options = assign({}, BASE_PATH_PARSER_OPTIONS, extraOptions);\n    // the amount of scores is the same as the length of segments except for the root segment \"/\"\n    const score = [];\n    // the regexp as a string\n    let pattern = options.start ? '^' : '';\n    // extracted keys\n    const keys = [];\n    for (const segment of segments) {\n        // the root segment needs special treatment\n        const segmentScores = segment.length ? [] : [90 /* PathScore.Root */];\n        // allow trailing slash\n        if (options.strict && !segment.length)\n            pattern += '/';\n        for (let tokenIndex = 0; tokenIndex < segment.length; tokenIndex++) {\n            const token = segment[tokenIndex];\n            // resets the score if we are inside a sub-segment /:a-other-:b\n            let subSegmentScore = 40 /* PathScore.Segment */ +\n                (options.sensitive ? 0.25 /* PathScore.BonusCaseSensitive */ : 0);\n            if (token.type === 0 /* TokenType.Static */) {\n                // prepend the slash if we are starting a new segment\n                if (!tokenIndex)\n                    pattern += '/';\n                pattern += token.value.replace(REGEX_CHARS_RE, '\\\\$&');\n                subSegmentScore += 40 /* PathScore.Static */;\n            }\n            else if (token.type === 1 /* TokenType.Param */) {\n                const { value, repeatable, optional, regexp } = token;\n                keys.push({\n                    name: value,\n                    repeatable,\n                    optional,\n                });\n                const re = regexp ? regexp : BASE_PARAM_PATTERN;\n                // the user provided a custom regexp /:id(\\\\d+)\n                if (re !== BASE_PARAM_PATTERN) {\n                    subSegmentScore += 10 /* PathScore.BonusCustomRegExp */;\n                    // make sure the regexp is valid before using it\n                    try {\n                        new RegExp(`(${re})`);\n                    }\n                    catch (err) {\n                        throw new Error(`Invalid custom RegExp for param \"${value}\" (${re}): ` +\n                            err.message);\n                    }\n                }\n                // when we repeat we must take care of the repeating leading slash\n                let subPattern = repeatable ? `((?:${re})(?:/(?:${re}))*)` : `(${re})`;\n                // prepend the slash if we are starting a new segment\n                if (!tokenIndex)\n                    subPattern =\n                        // avoid an optional / if there are more segments e.g. /:p?-static\n                        // or /:p?-:p2\n                        optional && segment.length < 2\n                            ? `(?:/${subPattern})`\n                            : '/' + subPattern;\n                if (optional)\n                    subPattern += '?';\n                pattern += subPattern;\n                subSegmentScore += 20 /* PathScore.Dynamic */;\n                if (optional)\n                    subSegmentScore += -8 /* PathScore.BonusOptional */;\n                if (repeatable)\n                    subSegmentScore += -20 /* PathScore.BonusRepeatable */;\n                if (re === '.*')\n                    subSegmentScore += -50 /* PathScore.BonusWildcard */;\n            }\n            segmentScores.push(subSegmentScore);\n        }\n        // an empty array like /home/<USER>\n        // if (!segment.length) pattern += '/'\n        score.push(segmentScores);\n    }\n    // only apply the strict bonus to the last score\n    if (options.strict && options.end) {\n        const i = score.length - 1;\n        score[i][score[i].length - 1] += 0.7000000000000001 /* PathScore.BonusStrict */;\n    }\n    // TODO: dev only warn double trailing slash\n    if (!options.strict)\n        pattern += '/?';\n    if (options.end)\n        pattern += '$';\n    // allow paths like /dynamic to only match dynamic or dynamic/... but not dynamic_something_else\n    else if (options.strict && !pattern.endsWith('/'))\n        pattern += '(?:/|$)';\n    const re = new RegExp(pattern, options.sensitive ? '' : 'i');\n    function parse(path) {\n        const match = path.match(re);\n        const params = {};\n        if (!match)\n            return null;\n        for (let i = 1; i < match.length; i++) {\n            const value = match[i] || '';\n            const key = keys[i - 1];\n            params[key.name] = value && key.repeatable ? value.split('/') : value;\n        }\n        return params;\n    }\n    function stringify(params) {\n        let path = '';\n        // for optional parameters to allow to be empty\n        let avoidDuplicatedSlash = false;\n        for (const segment of segments) {\n            if (!avoidDuplicatedSlash || !path.endsWith('/'))\n                path += '/';\n            avoidDuplicatedSlash = false;\n            for (const token of segment) {\n                if (token.type === 0 /* TokenType.Static */) {\n                    path += token.value;\n                }\n                else if (token.type === 1 /* TokenType.Param */) {\n                    const { value, repeatable, optional } = token;\n                    const param = value in params ? params[value] : '';\n                    if (isArray(param) && !repeatable) {\n                        throw new Error(`Provided param \"${value}\" is an array but it is not repeatable (* or + modifiers)`);\n                    }\n                    const text = isArray(param)\n                        ? param.join('/')\n                        : param;\n                    if (!text) {\n                        if (optional) {\n                            // if we have more than one optional param like /:a?-static we don't need to care about the optional param\n                            if (segment.length < 2) {\n                                // remove the last slash as we could be at the end\n                                if (path.endsWith('/'))\n                                    path = path.slice(0, -1);\n                                // do not append a slash on the next iteration\n                                else\n                                    avoidDuplicatedSlash = true;\n                            }\n                        }\n                        else\n                            throw new Error(`Missing required param \"${value}\"`);\n                    }\n                    path += text;\n                }\n            }\n        }\n        // avoid empty path when we have multiple optional params\n        return path || '/';\n    }\n    return {\n        re,\n        score,\n        keys,\n        parse,\n        stringify,\n    };\n}\n/**\n * Compares an array of numbers as used in PathParser.score and returns a\n * number. This function can be used to `sort` an array\n *\n * @param a - first array of numbers\n * @param b - second array of numbers\n * @returns 0 if both are equal, < 0 if a should be sorted first, > 0 if b\n * should be sorted first\n */\nfunction compareScoreArray(a, b) {\n    let i = 0;\n    while (i < a.length && i < b.length) {\n        const diff = b[i] - a[i];\n        // only keep going if diff === 0\n        if (diff)\n            return diff;\n        i++;\n    }\n    // if the last subsegment was Static, the shorter segments should be sorted first\n    // otherwise sort the longest segment first\n    if (a.length < b.length) {\n        return a.length === 1 && a[0] === 40 /* PathScore.Static */ + 40 /* PathScore.Segment */\n            ? -1\n            : 1;\n    }\n    else if (a.length > b.length) {\n        return b.length === 1 && b[0] === 40 /* PathScore.Static */ + 40 /* PathScore.Segment */\n            ? 1\n            : -1;\n    }\n    return 0;\n}\n/**\n * Compare function that can be used with `sort` to sort an array of PathParser\n *\n * @param a - first PathParser\n * @param b - second PathParser\n * @returns 0 if both are equal, < 0 if a should be sorted first, > 0 if b\n */\nfunction comparePathParserScore(a, b) {\n    let i = 0;\n    const aScore = a.score;\n    const bScore = b.score;\n    while (i < aScore.length && i < bScore.length) {\n        const comp = compareScoreArray(aScore[i], bScore[i]);\n        // do not return if both are equal\n        if (comp)\n            return comp;\n        i++;\n    }\n    if (Math.abs(bScore.length - aScore.length) === 1) {\n        if (isLastScoreNegative(aScore))\n            return 1;\n        if (isLastScoreNegative(bScore))\n            return -1;\n    }\n    // if a and b share the same score entries but b has more, sort b first\n    return bScore.length - aScore.length;\n    // this is the ternary version\n    // return aScore.length < bScore.length\n    //   ? 1\n    //   : aScore.length > bScore.length\n    //   ? -1\n    //   : 0\n}\n/**\n * This allows detecting splats at the end of a path: /home/<USER>\n *\n * @param score - score to check\n * @returns true if the last entry is negative\n */\nfunction isLastScoreNegative(score) {\n    const last = score[score.length - 1];\n    return score.length > 0 && last[last.length - 1] < 0;\n}\n\nconst ROOT_TOKEN = {\n    type: 0 /* TokenType.Static */,\n    value: '',\n};\nconst VALID_PARAM_RE = /[a-zA-Z0-9_]/;\n// After some profiling, the cache seems to be unnecessary because tokenizePath\n// (the slowest part of adding a route) is very fast\n// const tokenCache = new Map<string, Token[][]>()\nfunction tokenizePath(path) {\n    if (!path)\n        return [[]];\n    if (path === '/')\n        return [[ROOT_TOKEN]];\n    if (!path.startsWith('/')) {\n        throw new Error((process.env.NODE_ENV !== 'production')\n            ? `Route paths should start with a \"/\": \"${path}\" should be \"/${path}\".`\n            : `Invalid path \"${path}\"`);\n    }\n    // if (tokenCache.has(path)) return tokenCache.get(path)!\n    function crash(message) {\n        throw new Error(`ERR (${state})/\"${buffer}\": ${message}`);\n    }\n    let state = 0 /* TokenizerState.Static */;\n    let previousState = state;\n    const tokens = [];\n    // the segment will always be valid because we get into the initial state\n    // with the leading /\n    let segment;\n    function finalizeSegment() {\n        if (segment)\n            tokens.push(segment);\n        segment = [];\n    }\n    // index on the path\n    let i = 0;\n    // char at index\n    let char;\n    // buffer of the value read\n    let buffer = '';\n    // custom regexp for a param\n    let customRe = '';\n    function consumeBuffer() {\n        if (!buffer)\n            return;\n        if (state === 0 /* TokenizerState.Static */) {\n            segment.push({\n                type: 0 /* TokenType.Static */,\n                value: buffer,\n            });\n        }\n        else if (state === 1 /* TokenizerState.Param */ ||\n            state === 2 /* TokenizerState.ParamRegExp */ ||\n            state === 3 /* TokenizerState.ParamRegExpEnd */) {\n            if (segment.length > 1 && (char === '*' || char === '+'))\n                crash(`A repeatable param (${buffer}) must be alone in its segment. eg: '/:ids+.`);\n            segment.push({\n                type: 1 /* TokenType.Param */,\n                value: buffer,\n                regexp: customRe,\n                repeatable: char === '*' || char === '+',\n                optional: char === '*' || char === '?',\n            });\n        }\n        else {\n            crash('Invalid state to consume buffer');\n        }\n        buffer = '';\n    }\n    function addCharToBuffer() {\n        buffer += char;\n    }\n    while (i < path.length) {\n        char = path[i++];\n        if (char === '\\\\' && state !== 2 /* TokenizerState.ParamRegExp */) {\n            previousState = state;\n            state = 4 /* TokenizerState.EscapeNext */;\n            continue;\n        }\n        switch (state) {\n            case 0 /* TokenizerState.Static */:\n                if (char === '/') {\n                    if (buffer) {\n                        consumeBuffer();\n                    }\n                    finalizeSegment();\n                }\n                else if (char === ':') {\n                    consumeBuffer();\n                    state = 1 /* TokenizerState.Param */;\n                }\n                else {\n                    addCharToBuffer();\n                }\n                break;\n            case 4 /* TokenizerState.EscapeNext */:\n                addCharToBuffer();\n                state = previousState;\n                break;\n            case 1 /* TokenizerState.Param */:\n                if (char === '(') {\n                    state = 2 /* TokenizerState.ParamRegExp */;\n                }\n                else if (VALID_PARAM_RE.test(char)) {\n                    addCharToBuffer();\n                }\n                else {\n                    consumeBuffer();\n                    state = 0 /* TokenizerState.Static */;\n                    // go back one character if we were not modifying\n                    if (char !== '*' && char !== '?' && char !== '+')\n                        i--;\n                }\n                break;\n            case 2 /* TokenizerState.ParamRegExp */:\n                // TODO: is it worth handling nested regexp? like :p(?:prefix_([^/]+)_suffix)\n                // it already works by escaping the closing )\n                // https://paths.esm.dev/?p=AAMeJbiAwQEcDKbAoAAkP60PG2R6QAvgNaA6AFACM2ABuQBB#\n                // is this really something people need since you can also write\n                // /prefix_:p()_suffix\n                if (char === ')') {\n                    // handle the escaped )\n                    if (customRe[customRe.length - 1] == '\\\\')\n                        customRe = customRe.slice(0, -1) + char;\n                    else\n                        state = 3 /* TokenizerState.ParamRegExpEnd */;\n                }\n                else {\n                    customRe += char;\n                }\n                break;\n            case 3 /* TokenizerState.ParamRegExpEnd */:\n                // same as finalizing a param\n                consumeBuffer();\n                state = 0 /* TokenizerState.Static */;\n                // go back one character if we were not modifying\n                if (char !== '*' && char !== '?' && char !== '+')\n                    i--;\n                customRe = '';\n                break;\n            default:\n                crash('Unknown state');\n                break;\n        }\n    }\n    if (state === 2 /* TokenizerState.ParamRegExp */)\n        crash(`Unfinished custom RegExp for param \"${buffer}\"`);\n    consumeBuffer();\n    finalizeSegment();\n    // tokenCache.set(path, tokens)\n    return tokens;\n}\n\nfunction createRouteRecordMatcher(record, parent, options) {\n    const parser = tokensToParser(tokenizePath(record.path), options);\n    // warn against params with the same name\n    if ((process.env.NODE_ENV !== 'production')) {\n        const existingKeys = new Set();\n        for (const key of parser.keys) {\n            if (existingKeys.has(key.name))\n                warn(`Found duplicated params with name \"${key.name}\" for path \"${record.path}\". Only the last one will be available on \"$route.params\".`);\n            existingKeys.add(key.name);\n        }\n    }\n    const matcher = assign(parser, {\n        record,\n        parent,\n        // these needs to be populated by the parent\n        children: [],\n        alias: [],\n    });\n    if (parent) {\n        // both are aliases or both are not aliases\n        // we don't want to mix them because the order is used when\n        // passing originalRecord in Matcher.addRoute\n        if (!matcher.record.aliasOf === !parent.record.aliasOf)\n            parent.children.push(matcher);\n    }\n    return matcher;\n}\n\n/**\n * Creates a Router Matcher.\n *\n * @internal\n * @param routes - array of initial routes\n * @param globalOptions - global route options\n */\nfunction createRouterMatcher(routes, globalOptions) {\n    // normalized ordered array of matchers\n    const matchers = [];\n    const matcherMap = new Map();\n    globalOptions = mergeOptions({ strict: false, end: true, sensitive: false }, globalOptions);\n    function getRecordMatcher(name) {\n        return matcherMap.get(name);\n    }\n    function addRoute(record, parent, originalRecord) {\n        // used later on to remove by name\n        const isRootAdd = !originalRecord;\n        const mainNormalizedRecord = normalizeRouteRecord(record);\n        if ((process.env.NODE_ENV !== 'production')) {\n            checkChildMissingNameWithEmptyPath(mainNormalizedRecord, parent);\n        }\n        // we might be the child of an alias\n        mainNormalizedRecord.aliasOf = originalRecord && originalRecord.record;\n        const options = mergeOptions(globalOptions, record);\n        // generate an array of records to correctly handle aliases\n        const normalizedRecords = [mainNormalizedRecord];\n        if ('alias' in record) {\n            const aliases = typeof record.alias === 'string' ? [record.alias] : record.alias;\n            for (const alias of aliases) {\n                normalizedRecords.push(\n                // we need to normalize again to ensure the `mods` property\n                // being non enumerable\n                normalizeRouteRecord(assign({}, mainNormalizedRecord, {\n                    // this allows us to hold a copy of the `components` option\n                    // so that async components cache is hold on the original record\n                    components: originalRecord\n                        ? originalRecord.record.components\n                        : mainNormalizedRecord.components,\n                    path: alias,\n                    // we might be the child of an alias\n                    aliasOf: originalRecord\n                        ? originalRecord.record\n                        : mainNormalizedRecord,\n                    // the aliases are always of the same kind as the original since they\n                    // are defined on the same record\n                })));\n            }\n        }\n        let matcher;\n        let originalMatcher;\n        for (const normalizedRecord of normalizedRecords) {\n            const { path } = normalizedRecord;\n            // Build up the path for nested routes if the child isn't an absolute\n            // route. Only add the / delimiter if the child path isn't empty and if the\n            // parent path doesn't have a trailing slash\n            if (parent && path[0] !== '/') {\n                const parentPath = parent.record.path;\n                const connectingSlash = parentPath[parentPath.length - 1] === '/' ? '' : '/';\n                normalizedRecord.path =\n                    parent.record.path + (path && connectingSlash + path);\n            }\n            if ((process.env.NODE_ENV !== 'production') && normalizedRecord.path === '*') {\n                throw new Error('Catch all routes (\"*\") must now be defined using a param with a custom regexp.\\n' +\n                    'See more at https://router.vuejs.org/guide/migration/#Removed-star-or-catch-all-routes.');\n            }\n            // create the object beforehand, so it can be passed to children\n            matcher = createRouteRecordMatcher(normalizedRecord, parent, options);\n            if ((process.env.NODE_ENV !== 'production') && parent && path[0] === '/')\n                checkMissingParamsInAbsolutePath(matcher, parent);\n            // if we are an alias we must tell the original record that we exist,\n            // so we can be removed\n            if (originalRecord) {\n                originalRecord.alias.push(matcher);\n                if ((process.env.NODE_ENV !== 'production')) {\n                    checkSameParams(originalRecord, matcher);\n                }\n            }\n            else {\n                // otherwise, the first record is the original and others are aliases\n                originalMatcher = originalMatcher || matcher;\n                if (originalMatcher !== matcher)\n                    originalMatcher.alias.push(matcher);\n                // remove the route if named and only for the top record (avoid in nested calls)\n                // this works because the original record is the first one\n                if (isRootAdd && record.name && !isAliasRecord(matcher)) {\n                    if ((process.env.NODE_ENV !== 'production')) {\n                        checkSameNameAsAncestor(record, parent);\n                    }\n                    removeRoute(record.name);\n                }\n            }\n            // Avoid adding a record that doesn't display anything. This allows passing through records without a component to\n            // not be reached and pass through the catch all route\n            if (isMatchable(matcher)) {\n                insertMatcher(matcher);\n            }\n            if (mainNormalizedRecord.children) {\n                const children = mainNormalizedRecord.children;\n                for (let i = 0; i < children.length; i++) {\n                    addRoute(children[i], matcher, originalRecord && originalRecord.children[i]);\n                }\n            }\n            // if there was no original record, then the first one was not an alias and all\n            // other aliases (if any) need to reference this record when adding children\n            originalRecord = originalRecord || matcher;\n            // TODO: add normalized records for more flexibility\n            // if (parent && isAliasRecord(originalRecord)) {\n            //   parent.children.push(originalRecord)\n            // }\n        }\n        return originalMatcher\n            ? () => {\n                // since other matchers are aliases, they should be removed by the original matcher\n                removeRoute(originalMatcher);\n            }\n            : noop;\n    }\n    function removeRoute(matcherRef) {\n        if (isRouteName(matcherRef)) {\n            const matcher = matcherMap.get(matcherRef);\n            if (matcher) {\n                matcherMap.delete(matcherRef);\n                matchers.splice(matchers.indexOf(matcher), 1);\n                matcher.children.forEach(removeRoute);\n                matcher.alias.forEach(removeRoute);\n            }\n        }\n        else {\n            const index = matchers.indexOf(matcherRef);\n            if (index > -1) {\n                matchers.splice(index, 1);\n                if (matcherRef.record.name)\n                    matcherMap.delete(matcherRef.record.name);\n                matcherRef.children.forEach(removeRoute);\n                matcherRef.alias.forEach(removeRoute);\n            }\n        }\n    }\n    function getRoutes() {\n        return matchers;\n    }\n    function insertMatcher(matcher) {\n        const index = findInsertionIndex(matcher, matchers);\n        matchers.splice(index, 0, matcher);\n        // only add the original record to the name map\n        if (matcher.record.name && !isAliasRecord(matcher))\n            matcherMap.set(matcher.record.name, matcher);\n    }\n    function resolve(location, currentLocation) {\n        let matcher;\n        let params = {};\n        let path;\n        let name;\n        if ('name' in location && location.name) {\n            matcher = matcherMap.get(location.name);\n            if (!matcher)\n                throw createRouterError(1 /* ErrorTypes.MATCHER_NOT_FOUND */, {\n                    location,\n                });\n            // warn if the user is passing invalid params so they can debug it better when they get removed\n            if ((process.env.NODE_ENV !== 'production')) {\n                const invalidParams = Object.keys(location.params || {}).filter(paramName => !matcher.keys.find(k => k.name === paramName));\n                if (invalidParams.length) {\n                    warn(`Discarded invalid param(s) \"${invalidParams.join('\", \"')}\" when navigating. See https://github.com/vuejs/router/blob/main/packages/router/CHANGELOG.md#414-2022-08-22 for more details.`);\n                }\n            }\n            name = matcher.record.name;\n            params = assign(\n            // paramsFromLocation is a new object\n            paramsFromLocation(currentLocation.params, \n            // only keep params that exist in the resolved location\n            // only keep optional params coming from a parent record\n            matcher.keys\n                .filter(k => !k.optional)\n                .concat(matcher.parent ? matcher.parent.keys.filter(k => k.optional) : [])\n                .map(k => k.name)), \n            // discard any existing params in the current location that do not exist here\n            // #1497 this ensures better active/exact matching\n            location.params &&\n                paramsFromLocation(location.params, matcher.keys.map(k => k.name)));\n            // throws if cannot be stringified\n            path = matcher.stringify(params);\n        }\n        else if (location.path != null) {\n            // no need to resolve the path with the matcher as it was provided\n            // this also allows the user to control the encoding\n            path = location.path;\n            if ((process.env.NODE_ENV !== 'production') && !path.startsWith('/')) {\n                warn(`The Matcher cannot resolve relative paths but received \"${path}\". Unless you directly called \\`matcher.resolve(\"${path}\")\\`, this is probably a bug in vue-router. Please open an issue at https://github.com/vuejs/router/issues/new/choose.`);\n            }\n            matcher = matchers.find(m => m.re.test(path));\n            // matcher should have a value after the loop\n            if (matcher) {\n                // we know the matcher works because we tested the regexp\n                params = matcher.parse(path);\n                name = matcher.record.name;\n            }\n            // location is a relative path\n        }\n        else {\n            // match by name or path of current route\n            matcher = currentLocation.name\n                ? matcherMap.get(currentLocation.name)\n                : matchers.find(m => m.re.test(currentLocation.path));\n            if (!matcher)\n                throw createRouterError(1 /* ErrorTypes.MATCHER_NOT_FOUND */, {\n                    location,\n                    currentLocation,\n                });\n            name = matcher.record.name;\n            // since we are navigating to the same location, we don't need to pick the\n            // params like when `name` is provided\n            params = assign({}, currentLocation.params, location.params);\n            path = matcher.stringify(params);\n        }\n        const matched = [];\n        let parentMatcher = matcher;\n        while (parentMatcher) {\n            // reversed order so parents are at the beginning\n            matched.unshift(parentMatcher.record);\n            parentMatcher = parentMatcher.parent;\n        }\n        return {\n            name,\n            path,\n            params,\n            matched,\n            meta: mergeMetaFields(matched),\n        };\n    }\n    // add initial routes\n    routes.forEach(route => addRoute(route));\n    function clearRoutes() {\n        matchers.length = 0;\n        matcherMap.clear();\n    }\n    return {\n        addRoute,\n        resolve,\n        removeRoute,\n        clearRoutes,\n        getRoutes,\n        getRecordMatcher,\n    };\n}\nfunction paramsFromLocation(params, keys) {\n    const newParams = {};\n    for (const key of keys) {\n        if (key in params)\n            newParams[key] = params[key];\n    }\n    return newParams;\n}\n/**\n * Normalizes a RouteRecordRaw. Creates a copy\n *\n * @param record\n * @returns the normalized version\n */\nfunction normalizeRouteRecord(record) {\n    const normalized = {\n        path: record.path,\n        redirect: record.redirect,\n        name: record.name,\n        meta: record.meta || {},\n        aliasOf: record.aliasOf,\n        beforeEnter: record.beforeEnter,\n        props: normalizeRecordProps(record),\n        children: record.children || [],\n        instances: {},\n        leaveGuards: new Set(),\n        updateGuards: new Set(),\n        enterCallbacks: {},\n        // must be declared afterwards\n        // mods: {},\n        components: 'components' in record\n            ? record.components || null\n            : record.component && { default: record.component },\n    };\n    // mods contain modules and shouldn't be copied,\n    // logged or anything. It's just used for internal\n    // advanced use cases like data loaders\n    Object.defineProperty(normalized, 'mods', {\n        value: {},\n    });\n    return normalized;\n}\n/**\n * Normalize the optional `props` in a record to always be an object similar to\n * components. Also accept a boolean for components.\n * @param record\n */\nfunction normalizeRecordProps(record) {\n    const propsObject = {};\n    // props does not exist on redirect records, but we can set false directly\n    const props = record.props || false;\n    if ('component' in record) {\n        propsObject.default = props;\n    }\n    else {\n        // NOTE: we could also allow a function to be applied to every component.\n        // Would need user feedback for use cases\n        for (const name in record.components)\n            propsObject[name] = typeof props === 'object' ? props[name] : props;\n    }\n    return propsObject;\n}\n/**\n * Checks if a record or any of its parent is an alias\n * @param record\n */\nfunction isAliasRecord(record) {\n    while (record) {\n        if (record.record.aliasOf)\n            return true;\n        record = record.parent;\n    }\n    return false;\n}\n/**\n * Merge meta fields of an array of records\n *\n * @param matched - array of matched records\n */\nfunction mergeMetaFields(matched) {\n    return matched.reduce((meta, record) => assign(meta, record.meta), {});\n}\nfunction mergeOptions(defaults, partialOptions) {\n    const options = {};\n    for (const key in defaults) {\n        options[key] = key in partialOptions ? partialOptions[key] : defaults[key];\n    }\n    return options;\n}\nfunction isSameParam(a, b) {\n    return (a.name === b.name &&\n        a.optional === b.optional &&\n        a.repeatable === b.repeatable);\n}\n/**\n * Check if a path and its alias have the same required params\n *\n * @param a - original record\n * @param b - alias record\n */\nfunction checkSameParams(a, b) {\n    for (const key of a.keys) {\n        if (!key.optional && !b.keys.find(isSameParam.bind(null, key)))\n            return warn(`Alias \"${b.record.path}\" and the original record: \"${a.record.path}\" must have the exact same param named \"${key.name}\"`);\n    }\n    for (const key of b.keys) {\n        if (!key.optional && !a.keys.find(isSameParam.bind(null, key)))\n            return warn(`Alias \"${b.record.path}\" and the original record: \"${a.record.path}\" must have the exact same param named \"${key.name}\"`);\n    }\n}\n/**\n * A route with a name and a child with an empty path without a name should warn when adding the route\n *\n * @param mainNormalizedRecord - RouteRecordNormalized\n * @param parent - RouteRecordMatcher\n */\nfunction checkChildMissingNameWithEmptyPath(mainNormalizedRecord, parent) {\n    if (parent &&\n        parent.record.name &&\n        !mainNormalizedRecord.name &&\n        !mainNormalizedRecord.path) {\n        warn(`The route named \"${String(parent.record.name)}\" has a child without a name and an empty path. Using that name won't render the empty path child so you probably want to move the name to the child instead. If this is intentional, add a name to the child route to remove the warning.`);\n    }\n}\nfunction checkSameNameAsAncestor(record, parent) {\n    for (let ancestor = parent; ancestor; ancestor = ancestor.parent) {\n        if (ancestor.record.name === record.name) {\n            throw new Error(`A route named \"${String(record.name)}\" has been added as a ${parent === ancestor ? 'child' : 'descendant'} of a route with the same name. Route names must be unique and a nested route cannot use the same name as an ancestor.`);\n        }\n    }\n}\nfunction checkMissingParamsInAbsolutePath(record, parent) {\n    for (const key of parent.keys) {\n        if (!record.keys.find(isSameParam.bind(null, key)))\n            return warn(`Absolute path \"${record.record.path}\" must have the exact same param named \"${key.name}\" as its parent \"${parent.record.path}\".`);\n    }\n}\n/**\n * Performs a binary search to find the correct insertion index for a new matcher.\n *\n * Matchers are primarily sorted by their score. If scores are tied then we also consider parent/child relationships,\n * with descendants coming before ancestors. If there's still a tie, new routes are inserted after existing routes.\n *\n * @param matcher - new matcher to be inserted\n * @param matchers - existing matchers\n */\nfunction findInsertionIndex(matcher, matchers) {\n    // First phase: binary search based on score\n    let lower = 0;\n    let upper = matchers.length;\n    while (lower !== upper) {\n        const mid = (lower + upper) >> 1;\n        const sortOrder = comparePathParserScore(matcher, matchers[mid]);\n        if (sortOrder < 0) {\n            upper = mid;\n        }\n        else {\n            lower = mid + 1;\n        }\n    }\n    // Second phase: check for an ancestor with the same score\n    const insertionAncestor = getInsertionAncestor(matcher);\n    if (insertionAncestor) {\n        upper = matchers.lastIndexOf(insertionAncestor, upper - 1);\n        if ((process.env.NODE_ENV !== 'production') && upper < 0) {\n            // This should never happen\n            warn(`Finding ancestor route \"${insertionAncestor.record.path}\" failed for \"${matcher.record.path}\"`);\n        }\n    }\n    return upper;\n}\nfunction getInsertionAncestor(matcher) {\n    let ancestor = matcher;\n    while ((ancestor = ancestor.parent)) {\n        if (isMatchable(ancestor) &&\n            comparePathParserScore(matcher, ancestor) === 0) {\n            return ancestor;\n        }\n    }\n    return;\n}\n/**\n * Checks if a matcher can be reachable. This means if it's possible to reach it as a route. For example, routes without\n * a component, or name, or redirect, are just used to group other routes.\n * @param matcher\n * @param matcher.record record of the matcher\n * @returns\n */\nfunction isMatchable({ record }) {\n    return !!(record.name ||\n        (record.components && Object.keys(record.components).length) ||\n        record.redirect);\n}\n\n/**\n * Transforms a queryString into a {@link LocationQuery} object. Accept both, a\n * version with the leading `?` and without Should work as URLSearchParams\n\n * @internal\n *\n * @param search - search string to parse\n * @returns a query object\n */\nfunction parseQuery(search) {\n    const query = {};\n    // avoid creating an object with an empty key and empty value\n    // because of split('&')\n    if (search === '' || search === '?')\n        return query;\n    const hasLeadingIM = search[0] === '?';\n    const searchParams = (hasLeadingIM ? search.slice(1) : search).split('&');\n    for (let i = 0; i < searchParams.length; ++i) {\n        // pre decode the + into space\n        const searchParam = searchParams[i].replace(PLUS_RE, ' ');\n        // allow the = character\n        const eqPos = searchParam.indexOf('=');\n        const key = decode(eqPos < 0 ? searchParam : searchParam.slice(0, eqPos));\n        const value = eqPos < 0 ? null : decode(searchParam.slice(eqPos + 1));\n        if (key in query) {\n            // an extra variable for ts types\n            let currentValue = query[key];\n            if (!isArray(currentValue)) {\n                currentValue = query[key] = [currentValue];\n            }\n            currentValue.push(value);\n        }\n        else {\n            query[key] = value;\n        }\n    }\n    return query;\n}\n/**\n * Stringifies a {@link LocationQueryRaw} object. Like `URLSearchParams`, it\n * doesn't prepend a `?`\n *\n * @internal\n *\n * @param query - query object to stringify\n * @returns string version of the query without the leading `?`\n */\nfunction stringifyQuery(query) {\n    let search = '';\n    for (let key in query) {\n        const value = query[key];\n        key = encodeQueryKey(key);\n        if (value == null) {\n            // only null adds the value\n            if (value !== undefined) {\n                search += (search.length ? '&' : '') + key;\n            }\n            continue;\n        }\n        // keep null values\n        const values = isArray(value)\n            ? value.map(v => v && encodeQueryValue(v))\n            : [value && encodeQueryValue(value)];\n        values.forEach(value => {\n            // skip undefined values in arrays as if they were not present\n            // smaller code than using filter\n            if (value !== undefined) {\n                // only append & with non-empty search\n                search += (search.length ? '&' : '') + key;\n                if (value != null)\n                    search += '=' + value;\n            }\n        });\n    }\n    return search;\n}\n/**\n * Transforms a {@link LocationQueryRaw} into a {@link LocationQuery} by casting\n * numbers into strings, removing keys with an undefined value and replacing\n * undefined with null in arrays\n *\n * @param query - query object to normalize\n * @returns a normalized query object\n */\nfunction normalizeQuery(query) {\n    const normalizedQuery = {};\n    for (const key in query) {\n        const value = query[key];\n        if (value !== undefined) {\n            normalizedQuery[key] = isArray(value)\n                ? value.map(v => (v == null ? null : '' + v))\n                : value == null\n                    ? value\n                    : '' + value;\n        }\n    }\n    return normalizedQuery;\n}\n\n/**\n * RouteRecord being rendered by the closest ancestor Router View. Used for\n * `onBeforeRouteUpdate` and `onBeforeRouteLeave`. rvlm stands for Router View\n * Location Matched\n *\n * @internal\n */\nconst matchedRouteKey = Symbol((process.env.NODE_ENV !== 'production') ? 'router view location matched' : '');\n/**\n * Allows overriding the router view depth to control which component in\n * `matched` is rendered. rvd stands for Router View Depth\n *\n * @internal\n */\nconst viewDepthKey = Symbol((process.env.NODE_ENV !== 'production') ? 'router view depth' : '');\n/**\n * Allows overriding the router instance returned by `useRouter` in tests. r\n * stands for router\n *\n * @internal\n */\nconst routerKey = Symbol((process.env.NODE_ENV !== 'production') ? 'router' : '');\n/**\n * Allows overriding the current route returned by `useRoute` in tests. rl\n * stands for route location\n *\n * @internal\n */\nconst routeLocationKey = Symbol((process.env.NODE_ENV !== 'production') ? 'route location' : '');\n/**\n * Allows overriding the current route used by router-view. Internally this is\n * used when the `route` prop is passed.\n *\n * @internal\n */\nconst routerViewLocationKey = Symbol((process.env.NODE_ENV !== 'production') ? 'router view location' : '');\n\n/**\n * Create a list of callbacks that can be reset. Used to create before and after navigation guards list\n */\nfunction useCallbacks() {\n    let handlers = [];\n    function add(handler) {\n        handlers.push(handler);\n        return () => {\n            const i = handlers.indexOf(handler);\n            if (i > -1)\n                handlers.splice(i, 1);\n        };\n    }\n    function reset() {\n        handlers = [];\n    }\n    return {\n        add,\n        list: () => handlers.slice(),\n        reset,\n    };\n}\n\nfunction registerGuard(record, name, guard) {\n    const removeFromList = () => {\n        record[name].delete(guard);\n    };\n    onUnmounted(removeFromList);\n    onDeactivated(removeFromList);\n    onActivated(() => {\n        record[name].add(guard);\n    });\n    record[name].add(guard);\n}\n/**\n * Add a navigation guard that triggers whenever the component for the current\n * location is about to be left. Similar to {@link beforeRouteLeave} but can be\n * used in any component. The guard is removed when the component is unmounted.\n *\n * @param leaveGuard - {@link NavigationGuard}\n */\nfunction onBeforeRouteLeave(leaveGuard) {\n    if ((process.env.NODE_ENV !== 'production') && !getCurrentInstance()) {\n        warn('getCurrentInstance() returned null. onBeforeRouteLeave() must be called at the top of a setup function');\n        return;\n    }\n    const activeRecord = inject(matchedRouteKey, \n    // to avoid warning\n    {}).value;\n    if (!activeRecord) {\n        (process.env.NODE_ENV !== 'production') &&\n            warn('No active route record was found when calling `onBeforeRouteLeave()`. Make sure you call this function inside a component child of <router-view>. Maybe you called it inside of App.vue?');\n        return;\n    }\n    registerGuard(activeRecord, 'leaveGuards', leaveGuard);\n}\n/**\n * Add a navigation guard that triggers whenever the current location is about\n * to be updated. Similar to {@link beforeRouteUpdate} but can be used in any\n * component. The guard is removed when the component is unmounted.\n *\n * @param updateGuard - {@link NavigationGuard}\n */\nfunction onBeforeRouteUpdate(updateGuard) {\n    if ((process.env.NODE_ENV !== 'production') && !getCurrentInstance()) {\n        warn('getCurrentInstance() returned null. onBeforeRouteUpdate() must be called at the top of a setup function');\n        return;\n    }\n    const activeRecord = inject(matchedRouteKey, \n    // to avoid warning\n    {}).value;\n    if (!activeRecord) {\n        (process.env.NODE_ENV !== 'production') &&\n            warn('No active route record was found when calling `onBeforeRouteUpdate()`. Make sure you call this function inside a component child of <router-view>. Maybe you called it inside of App.vue?');\n        return;\n    }\n    registerGuard(activeRecord, 'updateGuards', updateGuard);\n}\nfunction guardToPromiseFn(guard, to, from, record, name, runWithContext = fn => fn()) {\n    // keep a reference to the enterCallbackArray to prevent pushing callbacks if a new navigation took place\n    const enterCallbackArray = record &&\n        // name is defined if record is because of the function overload\n        (record.enterCallbacks[name] = record.enterCallbacks[name] || []);\n    return () => new Promise((resolve, reject) => {\n        const next = (valid) => {\n            if (valid === false) {\n                reject(createRouterError(4 /* ErrorTypes.NAVIGATION_ABORTED */, {\n                    from,\n                    to,\n                }));\n            }\n            else if (valid instanceof Error) {\n                reject(valid);\n            }\n            else if (isRouteLocation(valid)) {\n                reject(createRouterError(2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */, {\n                    from: to,\n                    to: valid,\n                }));\n            }\n            else {\n                if (enterCallbackArray &&\n                    // since enterCallbackArray is truthy, both record and name also are\n                    record.enterCallbacks[name] === enterCallbackArray &&\n                    typeof valid === 'function') {\n                    enterCallbackArray.push(valid);\n                }\n                resolve();\n            }\n        };\n        // wrapping with Promise.resolve allows it to work with both async and sync guards\n        const guardReturn = runWithContext(() => guard.call(record && record.instances[name], to, from, (process.env.NODE_ENV !== 'production') ? canOnlyBeCalledOnce(next, to, from) : next));\n        let guardCall = Promise.resolve(guardReturn);\n        if (guard.length < 3)\n            guardCall = guardCall.then(next);\n        if ((process.env.NODE_ENV !== 'production') && guard.length > 2) {\n            const message = `The \"next\" callback was never called inside of ${guard.name ? '\"' + guard.name + '\"' : ''}:\\n${guard.toString()}\\n. If you are returning a value instead of calling \"next\", make sure to remove the \"next\" parameter from your function.`;\n            if (typeof guardReturn === 'object' && 'then' in guardReturn) {\n                guardCall = guardCall.then(resolvedValue => {\n                    // @ts-expect-error: _called is added at canOnlyBeCalledOnce\n                    if (!next._called) {\n                        warn(message);\n                        return Promise.reject(new Error('Invalid navigation guard'));\n                    }\n                    return resolvedValue;\n                });\n            }\n            else if (guardReturn !== undefined) {\n                // @ts-expect-error: _called is added at canOnlyBeCalledOnce\n                if (!next._called) {\n                    warn(message);\n                    reject(new Error('Invalid navigation guard'));\n                    return;\n                }\n            }\n        }\n        guardCall.catch(err => reject(err));\n    });\n}\nfunction canOnlyBeCalledOnce(next, to, from) {\n    let called = 0;\n    return function () {\n        if (called++ === 1)\n            warn(`The \"next\" callback was called more than once in one navigation guard when going from \"${from.fullPath}\" to \"${to.fullPath}\". It should be called exactly one time in each navigation guard. This will fail in production.`);\n        // @ts-expect-error: we put it in the original one because it's easier to check\n        next._called = true;\n        if (called === 1)\n            next.apply(null, arguments);\n    };\n}\nfunction extractComponentsGuards(matched, guardType, to, from, runWithContext = fn => fn()) {\n    const guards = [];\n    for (const record of matched) {\n        if ((process.env.NODE_ENV !== 'production') && !record.components && !record.children.length) {\n            warn(`Record with path \"${record.path}\" is either missing a \"component(s)\"` +\n                ` or \"children\" property.`);\n        }\n        for (const name in record.components) {\n            let rawComponent = record.components[name];\n            if ((process.env.NODE_ENV !== 'production')) {\n                if (!rawComponent ||\n                    (typeof rawComponent !== 'object' &&\n                        typeof rawComponent !== 'function')) {\n                    warn(`Component \"${name}\" in record with path \"${record.path}\" is not` +\n                        ` a valid component. Received \"${String(rawComponent)}\".`);\n                    // throw to ensure we stop here but warn to ensure the message isn't\n                    // missed by the user\n                    throw new Error('Invalid route component');\n                }\n                else if ('then' in rawComponent) {\n                    // warn if user wrote import('/component.vue') instead of () =>\n                    // import('./component.vue')\n                    warn(`Component \"${name}\" in record with path \"${record.path}\" is a ` +\n                        `Promise instead of a function that returns a Promise. Did you ` +\n                        `write \"import('./MyPage.vue')\" instead of ` +\n                        `\"() => import('./MyPage.vue')\" ? This will break in ` +\n                        `production if not fixed.`);\n                    const promise = rawComponent;\n                    rawComponent = () => promise;\n                }\n                else if (rawComponent.__asyncLoader &&\n                    // warn only once per component\n                    !rawComponent.__warnedDefineAsync) {\n                    rawComponent.__warnedDefineAsync = true;\n                    warn(`Component \"${name}\" in record with path \"${record.path}\" is defined ` +\n                        `using \"defineAsyncComponent()\". ` +\n                        `Write \"() => import('./MyPage.vue')\" instead of ` +\n                        `\"defineAsyncComponent(() => import('./MyPage.vue'))\".`);\n                }\n            }\n            // skip update and leave guards if the route component is not mounted\n            if (guardType !== 'beforeRouteEnter' && !record.instances[name])\n                continue;\n            if (isRouteComponent(rawComponent)) {\n                // __vccOpts is added by vue-class-component and contain the regular options\n                const options = rawComponent.__vccOpts || rawComponent;\n                const guard = options[guardType];\n                guard &&\n                    guards.push(guardToPromiseFn(guard, to, from, record, name, runWithContext));\n            }\n            else {\n                // start requesting the chunk already\n                let componentPromise = rawComponent();\n                if ((process.env.NODE_ENV !== 'production') && !('catch' in componentPromise)) {\n                    warn(`Component \"${name}\" in record with path \"${record.path}\" is a function that does not return a Promise. If you were passing a functional component, make sure to add a \"displayName\" to the component. This will break in production if not fixed.`);\n                    componentPromise = Promise.resolve(componentPromise);\n                }\n                guards.push(() => componentPromise.then(resolved => {\n                    if (!resolved)\n                        throw new Error(`Couldn't resolve component \"${name}\" at \"${record.path}\"`);\n                    const resolvedComponent = isESModule(resolved)\n                        ? resolved.default\n                        : resolved;\n                    // keep the resolved module for plugins like data loaders\n                    record.mods[name] = resolved;\n                    // replace the function with the resolved component\n                    // cannot be null or undefined because we went into the for loop\n                    record.components[name] = resolvedComponent;\n                    // __vccOpts is added by vue-class-component and contain the regular options\n                    const options = resolvedComponent.__vccOpts || resolvedComponent;\n                    const guard = options[guardType];\n                    return (guard &&\n                        guardToPromiseFn(guard, to, from, record, name, runWithContext)());\n                }));\n            }\n        }\n    }\n    return guards;\n}\n/**\n * Ensures a route is loaded, so it can be passed as o prop to `<RouterView>`.\n *\n * @param route - resolved route to load\n */\nfunction loadRouteLocation(route) {\n    return route.matched.every(record => record.redirect)\n        ? Promise.reject(new Error('Cannot load a route that redirects.'))\n        : Promise.all(route.matched.map(record => record.components &&\n            Promise.all(Object.keys(record.components).reduce((promises, name) => {\n                const rawComponent = record.components[name];\n                if (typeof rawComponent === 'function' &&\n                    !('displayName' in rawComponent)) {\n                    promises.push(rawComponent().then(resolved => {\n                        if (!resolved)\n                            return Promise.reject(new Error(`Couldn't resolve component \"${name}\" at \"${record.path}\". Ensure you passed a function that returns a promise.`));\n                        const resolvedComponent = isESModule(resolved)\n                            ? resolved.default\n                            : resolved;\n                        // keep the resolved module for plugins like data loaders\n                        record.mods[name] = resolved;\n                        // replace the function with the resolved component\n                        // cannot be null or undefined because we went into the for loop\n                        record.components[name] = resolvedComponent;\n                        return;\n                    }));\n                }\n                return promises;\n            }, [])))).then(() => route);\n}\n\n// TODO: we could allow currentRoute as a prop to expose `isActive` and\n// `isExactActive` behavior should go through an RFC\n/**\n * Returns the internal behavior of a {@link RouterLink} without the rendering part.\n *\n * @param props - a `to` location and an optional `replace` flag\n */\nfunction useLink(props) {\n    const router = inject(routerKey);\n    const currentRoute = inject(routeLocationKey);\n    let hasPrevious = false;\n    let previousTo = null;\n    const route = computed(() => {\n        const to = unref(props.to);\n        if ((process.env.NODE_ENV !== 'production') && (!hasPrevious || to !== previousTo)) {\n            if (!isRouteLocation(to)) {\n                if (hasPrevious) {\n                    warn(`Invalid value for prop \"to\" in useLink()\\n- to:`, to, `\\n- previous to:`, previousTo, `\\n- props:`, props);\n                }\n                else {\n                    warn(`Invalid value for prop \"to\" in useLink()\\n- to:`, to, `\\n- props:`, props);\n                }\n            }\n            previousTo = to;\n            hasPrevious = true;\n        }\n        return router.resolve(to);\n    });\n    const activeRecordIndex = computed(() => {\n        const { matched } = route.value;\n        const { length } = matched;\n        const routeMatched = matched[length - 1];\n        const currentMatched = currentRoute.matched;\n        if (!routeMatched || !currentMatched.length)\n            return -1;\n        const index = currentMatched.findIndex(isSameRouteRecord.bind(null, routeMatched));\n        if (index > -1)\n            return index;\n        // possible parent record\n        const parentRecordPath = getOriginalPath(matched[length - 2]);\n        return (\n        // we are dealing with nested routes\n        length > 1 &&\n            // if the parent and matched route have the same path, this link is\n            // referring to the empty child. Or we currently are on a different\n            // child of the same parent\n            getOriginalPath(routeMatched) === parentRecordPath &&\n            // avoid comparing the child with its parent\n            currentMatched[currentMatched.length - 1].path !== parentRecordPath\n            ? currentMatched.findIndex(isSameRouteRecord.bind(null, matched[length - 2]))\n            : index);\n    });\n    const isActive = computed(() => activeRecordIndex.value > -1 &&\n        includesParams(currentRoute.params, route.value.params));\n    const isExactActive = computed(() => activeRecordIndex.value > -1 &&\n        activeRecordIndex.value === currentRoute.matched.length - 1 &&\n        isSameRouteLocationParams(currentRoute.params, route.value.params));\n    function navigate(e = {}) {\n        if (guardEvent(e)) {\n            const p = router[unref(props.replace) ? 'replace' : 'push'](unref(props.to)\n            // avoid uncaught errors are they are logged anyway\n            ).catch(noop);\n            if (props.viewTransition &&\n                typeof document !== 'undefined' &&\n                'startViewTransition' in document) {\n                document.startViewTransition(() => p);\n            }\n            return p;\n        }\n        return Promise.resolve();\n    }\n    // devtools only\n    if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) && isBrowser) {\n        const instance = getCurrentInstance();\n        if (instance) {\n            const linkContextDevtools = {\n                route: route.value,\n                isActive: isActive.value,\n                isExactActive: isExactActive.value,\n                error: null,\n            };\n            // @ts-expect-error: this is internal\n            instance.__vrl_devtools = instance.__vrl_devtools || [];\n            // @ts-expect-error: this is internal\n            instance.__vrl_devtools.push(linkContextDevtools);\n            watchEffect(() => {\n                linkContextDevtools.route = route.value;\n                linkContextDevtools.isActive = isActive.value;\n                linkContextDevtools.isExactActive = isExactActive.value;\n                linkContextDevtools.error = isRouteLocation(unref(props.to))\n                    ? null\n                    : 'Invalid \"to\" value';\n            }, { flush: 'post' });\n        }\n    }\n    /**\n     * NOTE: update {@link _RouterLinkI}'s `$slots` type when updating this\n     */\n    return {\n        route,\n        href: computed(() => route.value.href),\n        isActive,\n        isExactActive,\n        navigate,\n    };\n}\nfunction preferSingleVNode(vnodes) {\n    return vnodes.length === 1 ? vnodes[0] : vnodes;\n}\nconst RouterLinkImpl = /*#__PURE__*/ defineComponent({\n    name: 'RouterLink',\n    compatConfig: { MODE: 3 },\n    props: {\n        to: {\n            type: [String, Object],\n            required: true,\n        },\n        replace: Boolean,\n        activeClass: String,\n        // inactiveClass: String,\n        exactActiveClass: String,\n        custom: Boolean,\n        ariaCurrentValue: {\n            type: String,\n            default: 'page',\n        },\n        viewTransition: Boolean,\n    },\n    useLink,\n    setup(props, { slots }) {\n        const link = reactive(useLink(props));\n        const { options } = inject(routerKey);\n        const elClass = computed(() => ({\n            [getLinkClass(props.activeClass, options.linkActiveClass, 'router-link-active')]: link.isActive,\n            // [getLinkClass(\n            //   props.inactiveClass,\n            //   options.linkInactiveClass,\n            //   'router-link-inactive'\n            // )]: !link.isExactActive,\n            [getLinkClass(props.exactActiveClass, options.linkExactActiveClass, 'router-link-exact-active')]: link.isExactActive,\n        }));\n        return () => {\n            const children = slots.default && preferSingleVNode(slots.default(link));\n            return props.custom\n                ? children\n                : h('a', {\n                    'aria-current': link.isExactActive\n                        ? props.ariaCurrentValue\n                        : null,\n                    href: link.href,\n                    // this would override user added attrs but Vue will still add\n                    // the listener, so we end up triggering both\n                    onClick: link.navigate,\n                    class: elClass.value,\n                }, children);\n        };\n    },\n});\n// export the public type for h/tsx inference\n// also to avoid inline import() in generated d.ts files\n/**\n * Component to render a link that triggers a navigation on click.\n */\nconst RouterLink = RouterLinkImpl;\nfunction guardEvent(e) {\n    // don't redirect with control keys\n    if (e.metaKey || e.altKey || e.ctrlKey || e.shiftKey)\n        return;\n    // don't redirect when preventDefault called\n    if (e.defaultPrevented)\n        return;\n    // don't redirect on right click\n    if (e.button !== undefined && e.button !== 0)\n        return;\n    // don't redirect if `target=\"_blank\"`\n    // @ts-expect-error getAttribute does exist\n    if (e.currentTarget && e.currentTarget.getAttribute) {\n        // @ts-expect-error getAttribute exists\n        const target = e.currentTarget.getAttribute('target');\n        if (/\\b_blank\\b/i.test(target))\n            return;\n    }\n    // this may be a Weex event which doesn't have this method\n    if (e.preventDefault)\n        e.preventDefault();\n    return true;\n}\nfunction includesParams(outer, inner) {\n    for (const key in inner) {\n        const innerValue = inner[key];\n        const outerValue = outer[key];\n        if (typeof innerValue === 'string') {\n            if (innerValue !== outerValue)\n                return false;\n        }\n        else {\n            if (!isArray(outerValue) ||\n                outerValue.length !== innerValue.length ||\n                innerValue.some((value, i) => value !== outerValue[i]))\n                return false;\n        }\n    }\n    return true;\n}\n/**\n * Get the original path value of a record by following its aliasOf\n * @param record\n */\nfunction getOriginalPath(record) {\n    return record ? (record.aliasOf ? record.aliasOf.path : record.path) : '';\n}\n/**\n * Utility class to get the active class based on defaults.\n * @param propClass\n * @param globalClass\n * @param defaultClass\n */\nconst getLinkClass = (propClass, globalClass, defaultClass) => propClass != null\n    ? propClass\n    : globalClass != null\n        ? globalClass\n        : defaultClass;\n\nconst RouterViewImpl = /*#__PURE__*/ defineComponent({\n    name: 'RouterView',\n    // #674 we manually inherit them\n    inheritAttrs: false,\n    props: {\n        name: {\n            type: String,\n            default: 'default',\n        },\n        route: Object,\n    },\n    // Better compat for @vue/compat users\n    // https://github.com/vuejs/router/issues/1315\n    compatConfig: { MODE: 3 },\n    setup(props, { attrs, slots }) {\n        (process.env.NODE_ENV !== 'production') && warnDeprecatedUsage();\n        const injectedRoute = inject(routerViewLocationKey);\n        const routeToDisplay = computed(() => props.route || injectedRoute.value);\n        const injectedDepth = inject(viewDepthKey, 0);\n        // The depth changes based on empty components option, which allows passthrough routes e.g. routes with children\n        // that are used to reuse the `path` property\n        const depth = computed(() => {\n            let initialDepth = unref(injectedDepth);\n            const { matched } = routeToDisplay.value;\n            let matchedRoute;\n            while ((matchedRoute = matched[initialDepth]) &&\n                !matchedRoute.components) {\n                initialDepth++;\n            }\n            return initialDepth;\n        });\n        const matchedRouteRef = computed(() => routeToDisplay.value.matched[depth.value]);\n        provide(viewDepthKey, computed(() => depth.value + 1));\n        provide(matchedRouteKey, matchedRouteRef);\n        provide(routerViewLocationKey, routeToDisplay);\n        const viewRef = ref();\n        // watch at the same time the component instance, the route record we are\n        // rendering, and the name\n        watch(() => [viewRef.value, matchedRouteRef.value, props.name], ([instance, to, name], [oldInstance, from, oldName]) => {\n            // copy reused instances\n            if (to) {\n                // this will update the instance for new instances as well as reused\n                // instances when navigating to a new route\n                to.instances[name] = instance;\n                // the component instance is reused for a different route or name, so\n                // we copy any saved update or leave guards. With async setup, the\n                // mounting component will mount before the matchedRoute changes,\n                // making instance === oldInstance, so we check if guards have been\n                // added before. This works because we remove guards when\n                // unmounting/deactivating components\n                if (from && from !== to && instance && instance === oldInstance) {\n                    if (!to.leaveGuards.size) {\n                        to.leaveGuards = from.leaveGuards;\n                    }\n                    if (!to.updateGuards.size) {\n                        to.updateGuards = from.updateGuards;\n                    }\n                }\n            }\n            // trigger beforeRouteEnter next callbacks\n            if (instance &&\n                to &&\n                // if there is no instance but to and from are the same this might be\n                // the first visit\n                (!from || !isSameRouteRecord(to, from) || !oldInstance)) {\n                (to.enterCallbacks[name] || []).forEach(callback => callback(instance));\n            }\n        }, { flush: 'post' });\n        return () => {\n            const route = routeToDisplay.value;\n            // we need the value at the time we render because when we unmount, we\n            // navigated to a different location so the value is different\n            const currentName = props.name;\n            const matchedRoute = matchedRouteRef.value;\n            const ViewComponent = matchedRoute && matchedRoute.components[currentName];\n            if (!ViewComponent) {\n                return normalizeSlot(slots.default, { Component: ViewComponent, route });\n            }\n            // props from route configuration\n            const routePropsOption = matchedRoute.props[currentName];\n            const routeProps = routePropsOption\n                ? routePropsOption === true\n                    ? route.params\n                    : typeof routePropsOption === 'function'\n                        ? routePropsOption(route)\n                        : routePropsOption\n                : null;\n            const onVnodeUnmounted = vnode => {\n                // remove the instance reference to prevent leak\n                if (vnode.component.isUnmounted) {\n                    matchedRoute.instances[currentName] = null;\n                }\n            };\n            const component = h(ViewComponent, assign({}, routeProps, attrs, {\n                onVnodeUnmounted,\n                ref: viewRef,\n            }));\n            if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) &&\n                isBrowser &&\n                component.ref) {\n                // TODO: can display if it's an alias, its props\n                const info = {\n                    depth: depth.value,\n                    name: matchedRoute.name,\n                    path: matchedRoute.path,\n                    meta: matchedRoute.meta,\n                };\n                const internalInstances = isArray(component.ref)\n                    ? component.ref.map(r => r.i)\n                    : [component.ref.i];\n                internalInstances.forEach(instance => {\n                    // @ts-expect-error\n                    instance.__vrv_devtools = info;\n                });\n            }\n            return (\n            // pass the vnode to the slot as a prop.\n            // h and <component :is=\"...\"> both accept vnodes\n            normalizeSlot(slots.default, { Component: component, route }) ||\n                component);\n        };\n    },\n});\nfunction normalizeSlot(slot, data) {\n    if (!slot)\n        return null;\n    const slotContent = slot(data);\n    return slotContent.length === 1 ? slotContent[0] : slotContent;\n}\n// export the public type for h/tsx inference\n// also to avoid inline import() in generated d.ts files\n/**\n * Component to display the current route the user is at.\n */\nconst RouterView = RouterViewImpl;\n// warn against deprecated usage with <transition> & <keep-alive>\n// due to functional component being no longer eager in Vue 3\nfunction warnDeprecatedUsage() {\n    const instance = getCurrentInstance();\n    const parentName = instance.parent && instance.parent.type.name;\n    const parentSubTreeType = instance.parent && instance.parent.subTree && instance.parent.subTree.type;\n    if (parentName &&\n        (parentName === 'KeepAlive' || parentName.includes('Transition')) &&\n        typeof parentSubTreeType === 'object' &&\n        parentSubTreeType.name === 'RouterView') {\n        const comp = parentName === 'KeepAlive' ? 'keep-alive' : 'transition';\n        warn(`<router-view> can no longer be used directly inside <transition> or <keep-alive>.\\n` +\n            `Use slot props instead:\\n\\n` +\n            `<router-view v-slot=\"{ Component }\">\\n` +\n            `  <${comp}>\\n` +\n            `    <component :is=\"Component\" />\\n` +\n            `  </${comp}>\\n` +\n            `</router-view>`);\n    }\n}\n\n/**\n * Copies a route location and removes any problematic properties that cannot be shown in devtools (e.g. Vue instances).\n *\n * @param routeLocation - routeLocation to format\n * @param tooltip - optional tooltip\n * @returns a copy of the routeLocation\n */\nfunction formatRouteLocation(routeLocation, tooltip) {\n    const copy = assign({}, routeLocation, {\n        // remove variables that can contain vue instances\n        matched: routeLocation.matched.map(matched => omit(matched, ['instances', 'children', 'aliasOf'])),\n    });\n    return {\n        _custom: {\n            type: null,\n            readOnly: true,\n            display: routeLocation.fullPath,\n            tooltip,\n            value: copy,\n        },\n    };\n}\nfunction formatDisplay(display) {\n    return {\n        _custom: {\n            display,\n        },\n    };\n}\n// to support multiple router instances\nlet routerId = 0;\nfunction addDevtools(app, router, matcher) {\n    // Take over router.beforeEach and afterEach\n    // make sure we are not registering the devtool twice\n    if (router.__hasDevtools)\n        return;\n    router.__hasDevtools = true;\n    // increment to support multiple router instances\n    const id = routerId++;\n    setupDevtoolsPlugin({\n        id: 'org.vuejs.router' + (id ? '.' + id : ''),\n        label: 'Vue Router',\n        packageName: 'vue-router',\n        homepage: 'https://router.vuejs.org',\n        logo: 'https://router.vuejs.org/logo.png',\n        componentStateTypes: ['Routing'],\n        app,\n    }, api => {\n        if (typeof api.now !== 'function') {\n            console.warn('[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html.');\n        }\n        // display state added by the router\n        api.on.inspectComponent((payload, ctx) => {\n            if (payload.instanceData) {\n                payload.instanceData.state.push({\n                    type: 'Routing',\n                    key: '$route',\n                    editable: false,\n                    value: formatRouteLocation(router.currentRoute.value, 'Current Route'),\n                });\n            }\n        });\n        // mark router-link as active and display tags on router views\n        api.on.visitComponentTree(({ treeNode: node, componentInstance }) => {\n            if (componentInstance.__vrv_devtools) {\n                const info = componentInstance.__vrv_devtools;\n                node.tags.push({\n                    label: (info.name ? `${info.name.toString()}: ` : '') + info.path,\n                    textColor: 0,\n                    tooltip: 'This component is rendered by &lt;router-view&gt;',\n                    backgroundColor: PINK_500,\n                });\n            }\n            // if multiple useLink are used\n            if (isArray(componentInstance.__vrl_devtools)) {\n                componentInstance.__devtoolsApi = api;\n                componentInstance.__vrl_devtools.forEach(devtoolsData => {\n                    let label = devtoolsData.route.path;\n                    let backgroundColor = ORANGE_400;\n                    let tooltip = '';\n                    let textColor = 0;\n                    if (devtoolsData.error) {\n                        label = devtoolsData.error;\n                        backgroundColor = RED_100;\n                        textColor = RED_700;\n                    }\n                    else if (devtoolsData.isExactActive) {\n                        backgroundColor = LIME_500;\n                        tooltip = 'This is exactly active';\n                    }\n                    else if (devtoolsData.isActive) {\n                        backgroundColor = BLUE_600;\n                        tooltip = 'This link is active';\n                    }\n                    node.tags.push({\n                        label,\n                        textColor,\n                        tooltip,\n                        backgroundColor,\n                    });\n                });\n            }\n        });\n        watch(router.currentRoute, () => {\n            // refresh active state\n            refreshRoutesView();\n            api.notifyComponentUpdate();\n            api.sendInspectorTree(routerInspectorId);\n            api.sendInspectorState(routerInspectorId);\n        });\n        const navigationsLayerId = 'router:navigations:' + id;\n        api.addTimelineLayer({\n            id: navigationsLayerId,\n            label: `Router${id ? ' ' + id : ''} Navigations`,\n            color: 0x40a8c4,\n        });\n        // const errorsLayerId = 'router:errors'\n        // api.addTimelineLayer({\n        //   id: errorsLayerId,\n        //   label: 'Router Errors',\n        //   color: 0xea5455,\n        // })\n        router.onError((error, to) => {\n            api.addTimelineEvent({\n                layerId: navigationsLayerId,\n                event: {\n                    title: 'Error during Navigation',\n                    subtitle: to.fullPath,\n                    logType: 'error',\n                    time: api.now(),\n                    data: { error },\n                    groupId: to.meta.__navigationId,\n                },\n            });\n        });\n        // attached to `meta` and used to group events\n        let navigationId = 0;\n        router.beforeEach((to, from) => {\n            const data = {\n                guard: formatDisplay('beforeEach'),\n                from: formatRouteLocation(from, 'Current Location during this navigation'),\n                to: formatRouteLocation(to, 'Target location'),\n            };\n            // Used to group navigations together, hide from devtools\n            Object.defineProperty(to.meta, '__navigationId', {\n                value: navigationId++,\n            });\n            api.addTimelineEvent({\n                layerId: navigationsLayerId,\n                event: {\n                    time: api.now(),\n                    title: 'Start of navigation',\n                    subtitle: to.fullPath,\n                    data,\n                    groupId: to.meta.__navigationId,\n                },\n            });\n        });\n        router.afterEach((to, from, failure) => {\n            const data = {\n                guard: formatDisplay('afterEach'),\n            };\n            if (failure) {\n                data.failure = {\n                    _custom: {\n                        type: Error,\n                        readOnly: true,\n                        display: failure ? failure.message : '',\n                        tooltip: 'Navigation Failure',\n                        value: failure,\n                    },\n                };\n                data.status = formatDisplay('❌');\n            }\n            else {\n                data.status = formatDisplay('✅');\n            }\n            // we set here to have the right order\n            data.from = formatRouteLocation(from, 'Current Location during this navigation');\n            data.to = formatRouteLocation(to, 'Target location');\n            api.addTimelineEvent({\n                layerId: navigationsLayerId,\n                event: {\n                    title: 'End of navigation',\n                    subtitle: to.fullPath,\n                    time: api.now(),\n                    data,\n                    logType: failure ? 'warning' : 'default',\n                    groupId: to.meta.__navigationId,\n                },\n            });\n        });\n        /**\n         * Inspector of Existing routes\n         */\n        const routerInspectorId = 'router-inspector:' + id;\n        api.addInspector({\n            id: routerInspectorId,\n            label: 'Routes' + (id ? ' ' + id : ''),\n            icon: 'book',\n            treeFilterPlaceholder: 'Search routes',\n        });\n        function refreshRoutesView() {\n            // the routes view isn't active\n            if (!activeRoutesPayload)\n                return;\n            const payload = activeRoutesPayload;\n            // children routes will appear as nested\n            let routes = matcher.getRoutes().filter(route => !route.parent ||\n                // these routes have a parent with no component which will not appear in the view\n                // therefore we still need to include them\n                !route.parent.record.components);\n            // reset match state to false\n            routes.forEach(resetMatchStateOnRouteRecord);\n            // apply a match state if there is a payload\n            if (payload.filter) {\n                routes = routes.filter(route => \n                // save matches state based on the payload\n                isRouteMatching(route, payload.filter.toLowerCase()));\n            }\n            // mark active routes\n            routes.forEach(route => markRouteRecordActive(route, router.currentRoute.value));\n            payload.rootNodes = routes.map(formatRouteRecordForInspector);\n        }\n        let activeRoutesPayload;\n        api.on.getInspectorTree(payload => {\n            activeRoutesPayload = payload;\n            if (payload.app === app && payload.inspectorId === routerInspectorId) {\n                refreshRoutesView();\n            }\n        });\n        /**\n         * Display information about the currently selected route record\n         */\n        api.on.getInspectorState(payload => {\n            if (payload.app === app && payload.inspectorId === routerInspectorId) {\n                const routes = matcher.getRoutes();\n                const route = routes.find(route => route.record.__vd_id === payload.nodeId);\n                if (route) {\n                    payload.state = {\n                        options: formatRouteRecordMatcherForStateInspector(route),\n                    };\n                }\n            }\n        });\n        api.sendInspectorTree(routerInspectorId);\n        api.sendInspectorState(routerInspectorId);\n    });\n}\nfunction modifierForKey(key) {\n    if (key.optional) {\n        return key.repeatable ? '*' : '?';\n    }\n    else {\n        return key.repeatable ? '+' : '';\n    }\n}\nfunction formatRouteRecordMatcherForStateInspector(route) {\n    const { record } = route;\n    const fields = [\n        { editable: false, key: 'path', value: record.path },\n    ];\n    if (record.name != null) {\n        fields.push({\n            editable: false,\n            key: 'name',\n            value: record.name,\n        });\n    }\n    fields.push({ editable: false, key: 'regexp', value: route.re });\n    if (route.keys.length) {\n        fields.push({\n            editable: false,\n            key: 'keys',\n            value: {\n                _custom: {\n                    type: null,\n                    readOnly: true,\n                    display: route.keys\n                        .map(key => `${key.name}${modifierForKey(key)}`)\n                        .join(' '),\n                    tooltip: 'Param keys',\n                    value: route.keys,\n                },\n            },\n        });\n    }\n    if (record.redirect != null) {\n        fields.push({\n            editable: false,\n            key: 'redirect',\n            value: record.redirect,\n        });\n    }\n    if (route.alias.length) {\n        fields.push({\n            editable: false,\n            key: 'aliases',\n            value: route.alias.map(alias => alias.record.path),\n        });\n    }\n    if (Object.keys(route.record.meta).length) {\n        fields.push({\n            editable: false,\n            key: 'meta',\n            value: route.record.meta,\n        });\n    }\n    fields.push({\n        key: 'score',\n        editable: false,\n        value: {\n            _custom: {\n                type: null,\n                readOnly: true,\n                display: route.score.map(score => score.join(', ')).join(' | '),\n                tooltip: 'Score used to sort routes',\n                value: route.score,\n            },\n        },\n    });\n    return fields;\n}\n/**\n * Extracted from tailwind palette\n */\nconst PINK_500 = 0xec4899;\nconst BLUE_600 = 0x2563eb;\nconst LIME_500 = 0x84cc16;\nconst CYAN_400 = 0x22d3ee;\nconst ORANGE_400 = 0xfb923c;\n// const GRAY_100 = 0xf4f4f5\nconst DARK = 0x666666;\nconst RED_100 = 0xfee2e2;\nconst RED_700 = 0xb91c1c;\nfunction formatRouteRecordForInspector(route) {\n    const tags = [];\n    const { record } = route;\n    if (record.name != null) {\n        tags.push({\n            label: String(record.name),\n            textColor: 0,\n            backgroundColor: CYAN_400,\n        });\n    }\n    if (record.aliasOf) {\n        tags.push({\n            label: 'alias',\n            textColor: 0,\n            backgroundColor: ORANGE_400,\n        });\n    }\n    if (route.__vd_match) {\n        tags.push({\n            label: 'matches',\n            textColor: 0,\n            backgroundColor: PINK_500,\n        });\n    }\n    if (route.__vd_exactActive) {\n        tags.push({\n            label: 'exact',\n            textColor: 0,\n            backgroundColor: LIME_500,\n        });\n    }\n    if (route.__vd_active) {\n        tags.push({\n            label: 'active',\n            textColor: 0,\n            backgroundColor: BLUE_600,\n        });\n    }\n    if (record.redirect) {\n        tags.push({\n            label: typeof record.redirect === 'string'\n                ? `redirect: ${record.redirect}`\n                : 'redirects',\n            textColor: 0xffffff,\n            backgroundColor: DARK,\n        });\n    }\n    // add an id to be able to select it. Using the `path` is not possible because\n    // empty path children would collide with their parents\n    let id = record.__vd_id;\n    if (id == null) {\n        id = String(routeRecordId++);\n        record.__vd_id = id;\n    }\n    return {\n        id,\n        label: record.path,\n        tags,\n        children: route.children.map(formatRouteRecordForInspector),\n    };\n}\n//  incremental id for route records and inspector state\nlet routeRecordId = 0;\nconst EXTRACT_REGEXP_RE = /^\\/(.*)\\/([a-z]*)$/;\nfunction markRouteRecordActive(route, currentRoute) {\n    // no route will be active if matched is empty\n    // reset the matching state\n    const isExactActive = currentRoute.matched.length &&\n        isSameRouteRecord(currentRoute.matched[currentRoute.matched.length - 1], route.record);\n    route.__vd_exactActive = route.__vd_active = isExactActive;\n    if (!isExactActive) {\n        route.__vd_active = currentRoute.matched.some(match => isSameRouteRecord(match, route.record));\n    }\n    route.children.forEach(childRoute => markRouteRecordActive(childRoute, currentRoute));\n}\nfunction resetMatchStateOnRouteRecord(route) {\n    route.__vd_match = false;\n    route.children.forEach(resetMatchStateOnRouteRecord);\n}\nfunction isRouteMatching(route, filter) {\n    const found = String(route.re).match(EXTRACT_REGEXP_RE);\n    route.__vd_match = false;\n    if (!found || found.length < 3) {\n        return false;\n    }\n    // use a regexp without $ at the end to match nested routes better\n    const nonEndingRE = new RegExp(found[1].replace(/\\$$/, ''), found[2]);\n    if (nonEndingRE.test(filter)) {\n        // mark children as matches\n        route.children.forEach(child => isRouteMatching(child, filter));\n        // exception case: `/`\n        if (route.record.path !== '/' || filter === '/') {\n            route.__vd_match = route.re.test(filter);\n            return true;\n        }\n        // hide the / route\n        return false;\n    }\n    const path = route.record.path.toLowerCase();\n    const decodedPath = decode(path);\n    // also allow partial matching on the path\n    if (!filter.startsWith('/') &&\n        (decodedPath.includes(filter) || path.includes(filter)))\n        return true;\n    if (decodedPath.startsWith(filter) || path.startsWith(filter))\n        return true;\n    if (route.record.name && String(route.record.name).includes(filter))\n        return true;\n    return route.children.some(child => isRouteMatching(child, filter));\n}\nfunction omit(obj, keys) {\n    const ret = {};\n    for (const key in obj) {\n        if (!keys.includes(key)) {\n            // @ts-expect-error\n            ret[key] = obj[key];\n        }\n    }\n    return ret;\n}\n\n/**\n * Creates a Router instance that can be used by a Vue app.\n *\n * @param options - {@link RouterOptions}\n */\nfunction createRouter(options) {\n    const matcher = createRouterMatcher(options.routes, options);\n    const parseQuery$1 = options.parseQuery || parseQuery;\n    const stringifyQuery$1 = options.stringifyQuery || stringifyQuery;\n    const routerHistory = options.history;\n    if ((process.env.NODE_ENV !== 'production') && !routerHistory)\n        throw new Error('Provide the \"history\" option when calling \"createRouter()\":' +\n            ' https://router.vuejs.org/api/interfaces/RouterOptions.html#history');\n    const beforeGuards = useCallbacks();\n    const beforeResolveGuards = useCallbacks();\n    const afterGuards = useCallbacks();\n    const currentRoute = shallowRef(START_LOCATION_NORMALIZED);\n    let pendingLocation = START_LOCATION_NORMALIZED;\n    // leave the scrollRestoration if no scrollBehavior is provided\n    if (isBrowser && options.scrollBehavior && 'scrollRestoration' in history) {\n        history.scrollRestoration = 'manual';\n    }\n    const normalizeParams = applyToParams.bind(null, paramValue => '' + paramValue);\n    const encodeParams = applyToParams.bind(null, encodeParam);\n    const decodeParams = \n    // @ts-expect-error: intentionally avoid the type check\n    applyToParams.bind(null, decode);\n    function addRoute(parentOrRoute, route) {\n        let parent;\n        let record;\n        if (isRouteName(parentOrRoute)) {\n            parent = matcher.getRecordMatcher(parentOrRoute);\n            if ((process.env.NODE_ENV !== 'production') && !parent) {\n                warn(`Parent route \"${String(parentOrRoute)}\" not found when adding child route`, route);\n            }\n            record = route;\n        }\n        else {\n            record = parentOrRoute;\n        }\n        return matcher.addRoute(record, parent);\n    }\n    function removeRoute(name) {\n        const recordMatcher = matcher.getRecordMatcher(name);\n        if (recordMatcher) {\n            matcher.removeRoute(recordMatcher);\n        }\n        else if ((process.env.NODE_ENV !== 'production')) {\n            warn(`Cannot remove non-existent route \"${String(name)}\"`);\n        }\n    }\n    function getRoutes() {\n        return matcher.getRoutes().map(routeMatcher => routeMatcher.record);\n    }\n    function hasRoute(name) {\n        return !!matcher.getRecordMatcher(name);\n    }\n    function resolve(rawLocation, currentLocation) {\n        // const resolve: Router['resolve'] = (rawLocation: RouteLocationRaw, currentLocation) => {\n        // const objectLocation = routerLocationAsObject(rawLocation)\n        // we create a copy to modify it later\n        currentLocation = assign({}, currentLocation || currentRoute.value);\n        if (typeof rawLocation === 'string') {\n            const locationNormalized = parseURL(parseQuery$1, rawLocation, currentLocation.path);\n            const matchedRoute = matcher.resolve({ path: locationNormalized.path }, currentLocation);\n            const href = routerHistory.createHref(locationNormalized.fullPath);\n            if ((process.env.NODE_ENV !== 'production')) {\n                if (href.startsWith('//'))\n                    warn(`Location \"${rawLocation}\" resolved to \"${href}\". A resolved location cannot start with multiple slashes.`);\n                else if (!matchedRoute.matched.length) {\n                    warn(`No match found for location with path \"${rawLocation}\"`);\n                }\n            }\n            // locationNormalized is always a new object\n            return assign(locationNormalized, matchedRoute, {\n                params: decodeParams(matchedRoute.params),\n                hash: decode(locationNormalized.hash),\n                redirectedFrom: undefined,\n                href,\n            });\n        }\n        if ((process.env.NODE_ENV !== 'production') && !isRouteLocation(rawLocation)) {\n            warn(`router.resolve() was passed an invalid location. This will fail in production.\\n- Location:`, rawLocation);\n            return resolve({});\n        }\n        let matcherLocation;\n        // path could be relative in object as well\n        if (rawLocation.path != null) {\n            if ((process.env.NODE_ENV !== 'production') &&\n                'params' in rawLocation &&\n                !('name' in rawLocation) &&\n                // @ts-expect-error: the type is never\n                Object.keys(rawLocation.params).length) {\n                warn(`Path \"${rawLocation.path}\" was passed with params but they will be ignored. Use a named route alongside params instead.`);\n            }\n            matcherLocation = assign({}, rawLocation, {\n                path: parseURL(parseQuery$1, rawLocation.path, currentLocation.path).path,\n            });\n        }\n        else {\n            // remove any nullish param\n            const targetParams = assign({}, rawLocation.params);\n            for (const key in targetParams) {\n                if (targetParams[key] == null) {\n                    delete targetParams[key];\n                }\n            }\n            // pass encoded values to the matcher, so it can produce encoded path and fullPath\n            matcherLocation = assign({}, rawLocation, {\n                params: encodeParams(targetParams),\n            });\n            // current location params are decoded, we need to encode them in case the\n            // matcher merges the params\n            currentLocation.params = encodeParams(currentLocation.params);\n        }\n        const matchedRoute = matcher.resolve(matcherLocation, currentLocation);\n        const hash = rawLocation.hash || '';\n        if ((process.env.NODE_ENV !== 'production') && hash && !hash.startsWith('#')) {\n            warn(`A \\`hash\\` should always start with the character \"#\". Replace \"${hash}\" with \"#${hash}\".`);\n        }\n        // the matcher might have merged current location params, so\n        // we need to run the decoding again\n        matchedRoute.params = normalizeParams(decodeParams(matchedRoute.params));\n        const fullPath = stringifyURL(stringifyQuery$1, assign({}, rawLocation, {\n            hash: encodeHash(hash),\n            path: matchedRoute.path,\n        }));\n        const href = routerHistory.createHref(fullPath);\n        if ((process.env.NODE_ENV !== 'production')) {\n            if (href.startsWith('//')) {\n                warn(`Location \"${rawLocation}\" resolved to \"${href}\". A resolved location cannot start with multiple slashes.`);\n            }\n            else if (!matchedRoute.matched.length) {\n                warn(`No match found for location with path \"${rawLocation.path != null ? rawLocation.path : rawLocation}\"`);\n            }\n        }\n        return assign({\n            fullPath,\n            // keep the hash encoded so fullPath is effectively path + encodedQuery +\n            // hash\n            hash,\n            query: \n            // if the user is using a custom query lib like qs, we might have\n            // nested objects, so we keep the query as is, meaning it can contain\n            // numbers at `$route.query`, but at the point, the user will have to\n            // use their own type anyway.\n            // https://github.com/vuejs/router/issues/328#issuecomment-649481567\n            stringifyQuery$1 === stringifyQuery\n                ? normalizeQuery(rawLocation.query)\n                : (rawLocation.query || {}),\n        }, matchedRoute, {\n            redirectedFrom: undefined,\n            href,\n        });\n    }\n    function locationAsObject(to) {\n        return typeof to === 'string'\n            ? parseURL(parseQuery$1, to, currentRoute.value.path)\n            : assign({}, to);\n    }\n    function checkCanceledNavigation(to, from) {\n        if (pendingLocation !== to) {\n            return createRouterError(8 /* ErrorTypes.NAVIGATION_CANCELLED */, {\n                from,\n                to,\n            });\n        }\n    }\n    function push(to) {\n        return pushWithRedirect(to);\n    }\n    function replace(to) {\n        return push(assign(locationAsObject(to), { replace: true }));\n    }\n    function handleRedirectRecord(to) {\n        const lastMatched = to.matched[to.matched.length - 1];\n        if (lastMatched && lastMatched.redirect) {\n            const { redirect } = lastMatched;\n            let newTargetLocation = typeof redirect === 'function' ? redirect(to) : redirect;\n            if (typeof newTargetLocation === 'string') {\n                newTargetLocation =\n                    newTargetLocation.includes('?') || newTargetLocation.includes('#')\n                        ? (newTargetLocation = locationAsObject(newTargetLocation))\n                        : // force empty params\n                            { path: newTargetLocation };\n                // @ts-expect-error: force empty params when a string is passed to let\n                // the router parse them again\n                newTargetLocation.params = {};\n            }\n            if ((process.env.NODE_ENV !== 'production') &&\n                newTargetLocation.path == null &&\n                !('name' in newTargetLocation)) {\n                warn(`Invalid redirect found:\\n${JSON.stringify(newTargetLocation, null, 2)}\\n when navigating to \"${to.fullPath}\". A redirect must contain a name or path. This will break in production.`);\n                throw new Error('Invalid redirect');\n            }\n            return assign({\n                query: to.query,\n                hash: to.hash,\n                // avoid transferring params if the redirect has a path\n                params: newTargetLocation.path != null ? {} : to.params,\n            }, newTargetLocation);\n        }\n    }\n    function pushWithRedirect(to, redirectedFrom) {\n        const targetLocation = (pendingLocation = resolve(to));\n        const from = currentRoute.value;\n        const data = to.state;\n        const force = to.force;\n        // to could be a string where `replace` is a function\n        const replace = to.replace === true;\n        const shouldRedirect = handleRedirectRecord(targetLocation);\n        if (shouldRedirect)\n            return pushWithRedirect(assign(locationAsObject(shouldRedirect), {\n                state: typeof shouldRedirect === 'object'\n                    ? assign({}, data, shouldRedirect.state)\n                    : data,\n                force,\n                replace,\n            }), \n            // keep original redirectedFrom if it exists\n            redirectedFrom || targetLocation);\n        // if it was a redirect we already called `pushWithRedirect` above\n        const toLocation = targetLocation;\n        toLocation.redirectedFrom = redirectedFrom;\n        let failure;\n        if (!force && isSameRouteLocation(stringifyQuery$1, from, targetLocation)) {\n            failure = createRouterError(16 /* ErrorTypes.NAVIGATION_DUPLICATED */, { to: toLocation, from });\n            // trigger scroll to allow scrolling to the same anchor\n            handleScroll(from, from, \n            // this is a push, the only way for it to be triggered from a\n            // history.listen is with a redirect, which makes it become a push\n            true, \n            // This cannot be the first navigation because the initial location\n            // cannot be manually navigated to\n            false);\n        }\n        return (failure ? Promise.resolve(failure) : navigate(toLocation, from))\n            .catch((error) => isNavigationFailure(error)\n            ? // navigation redirects still mark the router as ready\n                isNavigationFailure(error, 2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */)\n                    ? error\n                    : markAsReady(error) // also returns the error\n            : // reject any unknown error\n                triggerError(error, toLocation, from))\n            .then((failure) => {\n            if (failure) {\n                if (isNavigationFailure(failure, 2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */)) {\n                    if ((process.env.NODE_ENV !== 'production') &&\n                        // we are redirecting to the same location we were already at\n                        isSameRouteLocation(stringifyQuery$1, resolve(failure.to), toLocation) &&\n                        // and we have done it a couple of times\n                        redirectedFrom &&\n                        // @ts-expect-error: added only in dev\n                        (redirectedFrom._count = redirectedFrom._count\n                            ? // @ts-expect-error\n                                redirectedFrom._count + 1\n                            : 1) > 30) {\n                        warn(`Detected a possibly infinite redirection in a navigation guard when going from \"${from.fullPath}\" to \"${toLocation.fullPath}\". Aborting to avoid a Stack Overflow.\\n Are you always returning a new location within a navigation guard? That would lead to this error. Only return when redirecting or aborting, that should fix this. This might break in production if not fixed.`);\n                        return Promise.reject(new Error('Infinite redirect in navigation guard'));\n                    }\n                    return pushWithRedirect(\n                    // keep options\n                    assign({\n                        // preserve an existing replacement but allow the redirect to override it\n                        replace,\n                    }, locationAsObject(failure.to), {\n                        state: typeof failure.to === 'object'\n                            ? assign({}, data, failure.to.state)\n                            : data,\n                        force,\n                    }), \n                    // preserve the original redirectedFrom if any\n                    redirectedFrom || toLocation);\n                }\n            }\n            else {\n                // if we fail we don't finalize the navigation\n                failure = finalizeNavigation(toLocation, from, true, replace, data);\n            }\n            triggerAfterEach(toLocation, from, failure);\n            return failure;\n        });\n    }\n    /**\n     * Helper to reject and skip all navigation guards if a new navigation happened\n     * @param to\n     * @param from\n     */\n    function checkCanceledNavigationAndReject(to, from) {\n        const error = checkCanceledNavigation(to, from);\n        return error ? Promise.reject(error) : Promise.resolve();\n    }\n    function runWithContext(fn) {\n        const app = installedApps.values().next().value;\n        // support Vue < 3.3\n        return app && typeof app.runWithContext === 'function'\n            ? app.runWithContext(fn)\n            : fn();\n    }\n    // TODO: refactor the whole before guards by internally using router.beforeEach\n    function navigate(to, from) {\n        let guards;\n        const [leavingRecords, updatingRecords, enteringRecords] = extractChangingRecords(to, from);\n        // all components here have been resolved once because we are leaving\n        guards = extractComponentsGuards(leavingRecords.reverse(), 'beforeRouteLeave', to, from);\n        // leavingRecords is already reversed\n        for (const record of leavingRecords) {\n            record.leaveGuards.forEach(guard => {\n                guards.push(guardToPromiseFn(guard, to, from));\n            });\n        }\n        const canceledNavigationCheck = checkCanceledNavigationAndReject.bind(null, to, from);\n        guards.push(canceledNavigationCheck);\n        // run the queue of per route beforeRouteLeave guards\n        return (runGuardQueue(guards)\n            .then(() => {\n            // check global guards beforeEach\n            guards = [];\n            for (const guard of beforeGuards.list()) {\n                guards.push(guardToPromiseFn(guard, to, from));\n            }\n            guards.push(canceledNavigationCheck);\n            return runGuardQueue(guards);\n        })\n            .then(() => {\n            // check in components beforeRouteUpdate\n            guards = extractComponentsGuards(updatingRecords, 'beforeRouteUpdate', to, from);\n            for (const record of updatingRecords) {\n                record.updateGuards.forEach(guard => {\n                    guards.push(guardToPromiseFn(guard, to, from));\n                });\n            }\n            guards.push(canceledNavigationCheck);\n            // run the queue of per route beforeEnter guards\n            return runGuardQueue(guards);\n        })\n            .then(() => {\n            // check the route beforeEnter\n            guards = [];\n            for (const record of enteringRecords) {\n                // do not trigger beforeEnter on reused views\n                if (record.beforeEnter) {\n                    if (isArray(record.beforeEnter)) {\n                        for (const beforeEnter of record.beforeEnter)\n                            guards.push(guardToPromiseFn(beforeEnter, to, from));\n                    }\n                    else {\n                        guards.push(guardToPromiseFn(record.beforeEnter, to, from));\n                    }\n                }\n            }\n            guards.push(canceledNavigationCheck);\n            // run the queue of per route beforeEnter guards\n            return runGuardQueue(guards);\n        })\n            .then(() => {\n            // NOTE: at this point to.matched is normalized and does not contain any () => Promise<Component>\n            // clear existing enterCallbacks, these are added by extractComponentsGuards\n            to.matched.forEach(record => (record.enterCallbacks = {}));\n            // check in-component beforeRouteEnter\n            guards = extractComponentsGuards(enteringRecords, 'beforeRouteEnter', to, from, runWithContext);\n            guards.push(canceledNavigationCheck);\n            // run the queue of per route beforeEnter guards\n            return runGuardQueue(guards);\n        })\n            .then(() => {\n            // check global guards beforeResolve\n            guards = [];\n            for (const guard of beforeResolveGuards.list()) {\n                guards.push(guardToPromiseFn(guard, to, from));\n            }\n            guards.push(canceledNavigationCheck);\n            return runGuardQueue(guards);\n        })\n            // catch any navigation canceled\n            .catch(err => isNavigationFailure(err, 8 /* ErrorTypes.NAVIGATION_CANCELLED */)\n            ? err\n            : Promise.reject(err)));\n    }\n    function triggerAfterEach(to, from, failure) {\n        // navigation is confirmed, call afterGuards\n        // TODO: wrap with error handlers\n        afterGuards\n            .list()\n            .forEach(guard => runWithContext(() => guard(to, from, failure)));\n    }\n    /**\n     * - Cleans up any navigation guards\n     * - Changes the url if necessary\n     * - Calls the scrollBehavior\n     */\n    function finalizeNavigation(toLocation, from, isPush, replace, data) {\n        // a more recent navigation took place\n        const error = checkCanceledNavigation(toLocation, from);\n        if (error)\n            return error;\n        // only consider as push if it's not the first navigation\n        const isFirstNavigation = from === START_LOCATION_NORMALIZED;\n        const state = !isBrowser ? {} : history.state;\n        // change URL only if the user did a push/replace and if it's not the initial navigation because\n        // it's just reflecting the url\n        if (isPush) {\n            // on the initial navigation, we want to reuse the scroll position from\n            // history state if it exists\n            if (replace || isFirstNavigation)\n                routerHistory.replace(toLocation.fullPath, assign({\n                    scroll: isFirstNavigation && state && state.scroll,\n                }, data));\n            else\n                routerHistory.push(toLocation.fullPath, data);\n        }\n        // accept current navigation\n        currentRoute.value = toLocation;\n        handleScroll(toLocation, from, isPush, isFirstNavigation);\n        markAsReady();\n    }\n    let removeHistoryListener;\n    // attach listener to history to trigger navigations\n    function setupListeners() {\n        // avoid setting up listeners twice due to an invalid first navigation\n        if (removeHistoryListener)\n            return;\n        removeHistoryListener = routerHistory.listen((to, _from, info) => {\n            if (!router.listening)\n                return;\n            // cannot be a redirect route because it was in history\n            const toLocation = resolve(to);\n            // due to dynamic routing, and to hash history with manual navigation\n            // (manually changing the url or calling history.hash = '#/somewhere'),\n            // there could be a redirect record in history\n            const shouldRedirect = handleRedirectRecord(toLocation);\n            if (shouldRedirect) {\n                pushWithRedirect(assign(shouldRedirect, { replace: true, force: true }), toLocation).catch(noop);\n                return;\n            }\n            pendingLocation = toLocation;\n            const from = currentRoute.value;\n            // TODO: should be moved to web history?\n            if (isBrowser) {\n                saveScrollPosition(getScrollKey(from.fullPath, info.delta), computeScrollPosition());\n            }\n            navigate(toLocation, from)\n                .catch((error) => {\n                if (isNavigationFailure(error, 4 /* ErrorTypes.NAVIGATION_ABORTED */ | 8 /* ErrorTypes.NAVIGATION_CANCELLED */)) {\n                    return error;\n                }\n                if (isNavigationFailure(error, 2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */)) {\n                    // Here we could call if (info.delta) routerHistory.go(-info.delta,\n                    // false) but this is bug prone as we have no way to wait the\n                    // navigation to be finished before calling pushWithRedirect. Using\n                    // a setTimeout of 16ms seems to work but there is no guarantee for\n                    // it to work on every browser. So instead we do not restore the\n                    // history entry and trigger a new navigation as requested by the\n                    // navigation guard.\n                    // the error is already handled by router.push we just want to avoid\n                    // logging the error\n                    pushWithRedirect(assign(locationAsObject(error.to), {\n                        force: true,\n                    }), toLocation\n                    // avoid an uncaught rejection, let push call triggerError\n                    )\n                        .then(failure => {\n                        // manual change in hash history #916 ending up in the URL not\n                        // changing, but it was changed by the manual url change, so we\n                        // need to manually change it ourselves\n                        if (isNavigationFailure(failure, 4 /* ErrorTypes.NAVIGATION_ABORTED */ |\n                            16 /* ErrorTypes.NAVIGATION_DUPLICATED */) &&\n                            !info.delta &&\n                            info.type === NavigationType.pop) {\n                            routerHistory.go(-1, false);\n                        }\n                    })\n                        .catch(noop);\n                    // avoid the then branch\n                    return Promise.reject();\n                }\n                // do not restore history on unknown direction\n                if (info.delta) {\n                    routerHistory.go(-info.delta, false);\n                }\n                // unrecognized error, transfer to the global handler\n                return triggerError(error, toLocation, from);\n            })\n                .then((failure) => {\n                failure =\n                    failure ||\n                        finalizeNavigation(\n                        // after navigation, all matched components are resolved\n                        toLocation, from, false);\n                // revert the navigation\n                if (failure) {\n                    if (info.delta &&\n                        // a new navigation has been triggered, so we do not want to revert, that will change the current history\n                        // entry while a different route is displayed\n                        !isNavigationFailure(failure, 8 /* ErrorTypes.NAVIGATION_CANCELLED */)) {\n                        routerHistory.go(-info.delta, false);\n                    }\n                    else if (info.type === NavigationType.pop &&\n                        isNavigationFailure(failure, 4 /* ErrorTypes.NAVIGATION_ABORTED */ | 16 /* ErrorTypes.NAVIGATION_DUPLICATED */)) {\n                        // manual change in hash history #916\n                        // it's like a push but lacks the information of the direction\n                        routerHistory.go(-1, false);\n                    }\n                }\n                triggerAfterEach(toLocation, from, failure);\n            })\n                // avoid warnings in the console about uncaught rejections, they are logged by triggerErrors\n                .catch(noop);\n        });\n    }\n    // Initialization and Errors\n    let readyHandlers = useCallbacks();\n    let errorListeners = useCallbacks();\n    let ready;\n    /**\n     * Trigger errorListeners added via onError and throws the error as well\n     *\n     * @param error - error to throw\n     * @param to - location we were navigating to when the error happened\n     * @param from - location we were navigating from when the error happened\n     * @returns the error as a rejected promise\n     */\n    function triggerError(error, to, from) {\n        markAsReady(error);\n        const list = errorListeners.list();\n        if (list.length) {\n            list.forEach(handler => handler(error, to, from));\n        }\n        else {\n            if ((process.env.NODE_ENV !== 'production')) {\n                warn('uncaught error during route navigation:');\n            }\n            console.error(error);\n        }\n        // reject the error no matter there were error listeners or not\n        return Promise.reject(error);\n    }\n    function isReady() {\n        if (ready && currentRoute.value !== START_LOCATION_NORMALIZED)\n            return Promise.resolve();\n        return new Promise((resolve, reject) => {\n            readyHandlers.add([resolve, reject]);\n        });\n    }\n    function markAsReady(err) {\n        if (!ready) {\n            // still not ready if an error happened\n            ready = !err;\n            setupListeners();\n            readyHandlers\n                .list()\n                .forEach(([resolve, reject]) => (err ? reject(err) : resolve()));\n            readyHandlers.reset();\n        }\n        return err;\n    }\n    // Scroll behavior\n    function handleScroll(to, from, isPush, isFirstNavigation) {\n        const { scrollBehavior } = options;\n        if (!isBrowser || !scrollBehavior)\n            return Promise.resolve();\n        const scrollPosition = (!isPush && getSavedScrollPosition(getScrollKey(to.fullPath, 0))) ||\n            ((isFirstNavigation || !isPush) &&\n                history.state &&\n                history.state.scroll) ||\n            null;\n        return nextTick()\n            .then(() => scrollBehavior(to, from, scrollPosition))\n            .then(position => position && scrollToPosition(position))\n            .catch(err => triggerError(err, to, from));\n    }\n    const go = (delta) => routerHistory.go(delta);\n    let started;\n    const installedApps = new Set();\n    const router = {\n        currentRoute,\n        listening: true,\n        addRoute,\n        removeRoute,\n        clearRoutes: matcher.clearRoutes,\n        hasRoute,\n        getRoutes,\n        resolve,\n        options,\n        push,\n        replace,\n        go,\n        back: () => go(-1),\n        forward: () => go(1),\n        beforeEach: beforeGuards.add,\n        beforeResolve: beforeResolveGuards.add,\n        afterEach: afterGuards.add,\n        onError: errorListeners.add,\n        isReady,\n        install(app) {\n            const router = this;\n            app.component('RouterLink', RouterLink);\n            app.component('RouterView', RouterView);\n            app.config.globalProperties.$router = router;\n            Object.defineProperty(app.config.globalProperties, '$route', {\n                enumerable: true,\n                get: () => unref(currentRoute),\n            });\n            // this initial navigation is only necessary on client, on server it doesn't\n            // make sense because it will create an extra unnecessary navigation and could\n            // lead to problems\n            if (isBrowser &&\n                // used for the initial navigation client side to avoid pushing\n                // multiple times when the router is used in multiple apps\n                !started &&\n                currentRoute.value === START_LOCATION_NORMALIZED) {\n                // see above\n                started = true;\n                push(routerHistory.location).catch(err => {\n                    if ((process.env.NODE_ENV !== 'production'))\n                        warn('Unexpected error when starting the router:', err);\n                });\n            }\n            const reactiveRoute = {};\n            for (const key in START_LOCATION_NORMALIZED) {\n                Object.defineProperty(reactiveRoute, key, {\n                    get: () => currentRoute.value[key],\n                    enumerable: true,\n                });\n            }\n            app.provide(routerKey, router);\n            app.provide(routeLocationKey, shallowReactive(reactiveRoute));\n            app.provide(routerViewLocationKey, currentRoute);\n            const unmountApp = app.unmount;\n            installedApps.add(app);\n            app.unmount = function () {\n                installedApps.delete(app);\n                // the router is not attached to an app anymore\n                if (installedApps.size < 1) {\n                    // invalidate the current navigation\n                    pendingLocation = START_LOCATION_NORMALIZED;\n                    removeHistoryListener && removeHistoryListener();\n                    removeHistoryListener = null;\n                    currentRoute.value = START_LOCATION_NORMALIZED;\n                    started = false;\n                    ready = false;\n                }\n                unmountApp();\n            };\n            // TODO: this probably needs to be updated so it can be used by vue-termui\n            if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) && isBrowser) {\n                addDevtools(app, router, matcher);\n            }\n        },\n    };\n    // TODO: type this as NavigationGuardReturn or similar instead of any\n    function runGuardQueue(guards) {\n        return guards.reduce((promise, guard) => promise.then(() => runWithContext(guard)), Promise.resolve());\n    }\n    return router;\n}\nfunction extractChangingRecords(to, from) {\n    const leavingRecords = [];\n    const updatingRecords = [];\n    const enteringRecords = [];\n    const len = Math.max(from.matched.length, to.matched.length);\n    for (let i = 0; i < len; i++) {\n        const recordFrom = from.matched[i];\n        if (recordFrom) {\n            if (to.matched.find(record => isSameRouteRecord(record, recordFrom)))\n                updatingRecords.push(recordFrom);\n            else\n                leavingRecords.push(recordFrom);\n        }\n        const recordTo = to.matched[i];\n        if (recordTo) {\n            // the type doesn't matter because we are comparing per reference\n            if (!from.matched.find(record => isSameRouteRecord(record, recordTo))) {\n                enteringRecords.push(recordTo);\n            }\n        }\n    }\n    return [leavingRecords, updatingRecords, enteringRecords];\n}\n\n/**\n * Returns the router instance. Equivalent to using `$router` inside\n * templates.\n */\nfunction useRouter() {\n    return inject(routerKey);\n}\n/**\n * Returns the current route location. Equivalent to using `$route` inside\n * templates.\n */\nfunction useRoute(_name) {\n    return inject(routeLocationKey);\n}\n\nexport { NavigationFailureType, RouterLink, RouterView, START_LOCATION_NORMALIZED as START_LOCATION, createMemoryHistory, createRouter, createRouterMatcher, createWebHashHistory, createWebHistory, isNavigationFailure, loadRouteLocation, matchedRouteKey, onBeforeRouteLeave, onBeforeRouteUpdate, parseQuery, routeLocationKey, routerKey, routerViewLocationKey, stringifyQuery, useLink, useRoute, useRouter, viewDepthKey };\n", "import { defineComponent, ref, onMounted } from 'vue';\n\nfunction documentReady(_passThrough) {\n  if (document.readyState === \"loading\") {\n    return new Promise((resolve) => {\n      document.addEventListener(\"DOMContentLoaded\", () => resolve(_passThrough));\n    });\n  }\n  return Promise.resolve(_passThrough);\n}\n\nconst ClientOnly = defineComponent({\n  setup(props, { slots }) {\n    const mounted = ref(false);\n    onMounted(() => mounted.value = true);\n    return () => {\n      if (!mounted.value)\n        return slots.placeholder && slots.placeholder({});\n      return slots.default && slots.default({});\n    };\n  }\n});\n\nexport { ClientOnly as C, documentReady as d };\n", "const UNSAFE_CHARS_REGEXP = /[<>/\\u2028\\u2029]/g;\nconst ESCAPED_CHARS = {\n  \"<\": \"\\\\u003C\",\n  \">\": \"\\\\u003E\",\n  \"/\": \"\\\\u002F\",\n  \"\\u2028\": \"\\\\u2028\",\n  \"\\u2029\": \"\\\\u2029\"\n};\nfunction escapeUnsafeChars(unsafeChar) {\n  return ESCAPED_CHARS[unsafeChar];\n}\nfunction serializeState(state) {\n  if (state == null || Object.keys(state).length === 0)\n    return null;\n  try {\n    return JSON.stringify(JSON.stringify(state || {})).replace(\n      UNSAFE_CHARS_REGEXP,\n      escapeUnsafeChars\n    );\n  } catch (error) {\n    console.error(\"[SSG] On state serialization -\", error, state);\n    return null;\n  }\n}\nfunction deserializeState(state) {\n  try {\n    return JSON.parse(state || \"{}\");\n  } catch (error) {\n    console.error(\"[SSG] On state deserialization -\", error, state);\n    return {};\n  }\n}\n\nexport { deserializeState as d, serializeState as s };\n", "import { createHead } from '@unhead/vue';\nimport { createApp, createSSRApp } from 'vue';\nimport { createRouter, createWebHistory, createMemoryHistory } from 'vue-router';\nimport { C as ClientOnly, d as documentReady } from './shared/vite-ssg.ETIvV-80.mjs';\nimport { d as deserializeState } from './shared/vite-ssg.C6pK7rvr.mjs';\n\nfunction ViteSSG(App, routerOptions, fn, options = {}) {\n  const {\n    transformState,\n    registerComponents = true,\n    useHead = true,\n    rootContainer = \"#app\"\n  } = options;\n  const isClient = typeof window !== \"undefined\";\n  async function createApp$1(client = false, routePath) {\n    const app = client ? createApp(App) : createSSRApp(App);\n    let head;\n    if (useHead) {\n      head = createHead();\n      app.use(head);\n    }\n    const router = createRouter({\n      history: client ? createWebHistory(routerOptions.base) : createMemoryHistory(routerOptions.base),\n      ...routerOptions\n    });\n    const { routes } = routerOptions;\n    if (registerComponents)\n      app.component(\"ClientOnly\", ClientOnly);\n    const appRenderCallbacks = [];\n    const onSSRAppRendered = client ? () => {\n    } : (cb) => appRenderCallbacks.push(cb);\n    const triggerOnSSRAppRendered = () => {\n      return Promise.all(appRenderCallbacks.map((cb) => cb()));\n    };\n    const context = {\n      app,\n      head,\n      isClient,\n      router,\n      routes,\n      onSSRAppRendered,\n      triggerOnSSRAppRendered,\n      initialState: {},\n      transformState,\n      routePath\n    };\n    if (client) {\n      await documentReady();\n      context.initialState = transformState?.(window.__INITIAL_STATE__ || {}) || deserializeState(window.__INITIAL_STATE__);\n    }\n    await fn?.(context);\n    app.use(router);\n    let entryRoutePath;\n    let isFirstRoute = true;\n    router.beforeEach((to, from, next) => {\n      if (isFirstRoute || entryRoutePath && entryRoutePath === to.path) {\n        isFirstRoute = false;\n        entryRoutePath = to.path;\n        to.meta.state = context.initialState;\n      }\n      next();\n    });\n    if (!client) {\n      const route = context.routePath ?? \"/\";\n      router.push(route);\n      await router.isReady();\n      context.initialState = router.currentRoute.value.meta.state || {};\n    }\n    const initialState = context.initialState;\n    return {\n      ...context,\n      initialState\n    };\n  }\n  if (isClient) {\n    (async () => {\n      const { app, router } = await createApp$1(true);\n      await router.isReady();\n      app.mount(rootContainer, true);\n    })();\n  }\n  return createApp$1;\n}\n\nexport { ViteSSG };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,uBAAuC,oBAAI,IAAI,CAAC,SAAS,iBAAiB,UAAU,SAAS,UAAU,CAAC;AAC9G,IAAM,iBAAiC,oBAAI,IAAI;AAAA,EAC7C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAM,gBAAgC,oBAAI,IAAI;AAAA,EAC5C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAM,aAA6B,oBAAI,IAAI,CAAC,QAAQ,SAAS,iBAAiB,aAAa,aAAa,gBAAgB,CAAC;AACzH,IAAM,gBAAgC,oBAAI,IAAI,CAAC,eAAe,eAAe,wBAAwB,YAAY,aAAa,eAAe,uBAAuB,CAAC;AACrK,IAAM,YAAY,OAAO,WAAW;AACpC,IAAM,kBAAkB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,iBAAiB,QAAQ;AAChC,SAAO;AACT;AAEA,SAAS,SAAS,GAAG;AACnB,MAAIA,KAAI;AACR,WAAS,IAAI,GAAG,IAAI,EAAE;AACpB,IAAAA,KAAI,KAAK,KAAKA,KAAI,EAAE,WAAW,GAAG,GAAG,KAAK,CAAC;AAC7C,WAASA,KAAIA,OAAM,KAAK,OAAO,SAAS,EAAE,EAAE,UAAU,GAAG,CAAC,EAAE,YAAY;AAC1E;AACA,SAAS,QAAQ,KAAK;AACpB,MAAI,IAAI,IAAI;AACV,WAAO,IAAI;AAAA,EACb;AACA,MAAI,IAAI,IAAI;AACV,WAAO,SAAS,IAAI,EAAE;AAAA,EACxB;AACA,MAAI,UAAU,GAAG,IAAI,GAAG,IAAI,IAAI,eAAe,IAAI,aAAa,EAAE;AAClE,aAAW,OAAO,IAAI,OAAO;AAC3B,eAAW,GAAG,GAAG,IAAI,OAAO,IAAI,MAAM,GAAG,CAAC,CAAC;AAAA,EAC7C;AACA,SAAO,SAAS,OAAO;AACzB;AAEA,IAAM,IAAI,CAAC,QAAQ,EAAE,UAAU,IAAI,SAAS,WAAW;AACvD,IAAM,IAAI,CAAC,QAAQ,EAAE,UAAU,GAAG;AAClC,IAAM,oBAAoB;AAAA,EACxB,gBAAgB;AAAA,IACd,QAAQ;AAAA,MACN,gBAAgB;AAAA,MAChB,QAAQ,EAAE,KAAK,MAAM,GAAG;AACtB,eAAO,GAAG,WAAW,GAAG,CAAC,IAAI,KAAK;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AAAA,EACA,uBAAuB,EAAE,yBAAyB;AAAA,EAClD,qBAAqB,EAAE,uBAAuB;AAAA,EAC9C,sBAAsB,EAAE,wBAAwB;AAAA,EAChD,iBAAiB,EAAE,mBAAmB;AAAA,EACtC,SAAS;AAAA,IACP,SAAS;AAAA,EACX;AAAA,EACA,uBAAuB;AAAA,IACrB,QAAQ;AAAA,MACN,gBAAgB;AAAA,MAChB,QAAQ,EAAE,KAAK,MAAM,GAAG;AACtB,eAAO,GAAG,WAAW,GAAG,CAAC,IAAI,KAAK;AAAA,MACpC;AAAA,IACF;AAAA,IACA,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,SAAS,EAAE,WAAW;AAAA,EACtB,qBAAqB,EAAE,sBAAsB;AAAA,EAC7C,wBAAwB,EAAE,yBAAyB;AAAA,EACnD,wBAAwB,EAAE,yBAAyB;AAAA,EACnD,kBAAkB,EAAE,qBAAqB;AAAA,EACzC,YAAY,EAAE,UAAU;AAAA,EACxB,kBAAkB,EAAE,qBAAqB;AAAA,EACzC,YAAY,EAAE,UAAU;AAAA,EACxB,YAAY,EAAE,cAAc;AAAA,EAC5B,kBAAkB,EAAE,qBAAqB;AAAA,EACzC,YAAY,EAAE,UAAU;AAAA,EACxB,kBAAkB,EAAE,oBAAoB;AAAA,EACxC,iBAAiB,EAAE,mBAAmB;AAAA,EACtC,iBAAiB,EAAE,kBAAkB;AAAA,EACrC,SAAS;AAAA,IACP,SAAS;AAAA,IACT,QAAQ;AAAA,MACN,gBAAgB;AAAA,MAChB,QAAQ,EAAE,KAAK,MAAM,GAAG;AACtB,YAAI,QAAQ;AACV,iBAAO,GAAG,KAAK;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,QAAQ;AAAA,MACN,gBAAgB;AAAA,MAChB,QAAQ,EAAE,KAAK,MAAM,GAAG;AACtB,YAAI,OAAO,UAAU;AACnB,iBAAO,GAAG,WAAW,GAAG,CAAC;AAAA;AAEzB,iBAAO,GAAG,WAAW,GAAG,CAAC,IAAI,KAAK;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe;AAAA,IACb,SAAS;AAAA,EACX;AACF;AACA,IAAM,sBAAsC,oBAAI,IAAI;AAAA,EAClD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAWD,SAAS,WAAW,KAAK;AACvB,QAAM,UAAU,IAAI,QAAQ,YAAY,KAAK,EAAE,YAAY;AAC3D,QAAM,cAAc,QAAQ,QAAQ,GAAG;AACvC,QAAM,OAAO,QAAQ,UAAU,GAAG,WAAW;AAC7C,MAAI,SAAS,aAAa,oBAAoB,IAAI,IAAI;AACpD,WAAO,IAAI,QAAQ,YAAY,KAAK,EAAE,YAAY;AACpD,SAAO;AACT;AA8HA,SAAS,SAAS,KAAK,QAAQ;AAC7B,MAAI,eAAe,SAAS;AAC1B,WAAO,IAAI,KAAK,MAAM;AAAA,EACxB;AACA,SAAO,OAAO,GAAG;AACnB;AAEA,SAAS,aAAa,SAAS,OAAO,GAAG,iBAAiB;AACxD,QAAM,QAAQ,mBAAmB;AAAA;AAAA;AAAA,IAG/B,OAAO,UAAU,YAAY,OAAO,UAAU,cAAc,EAAE,iBAAiB,WAAW,EAAE,GAAG,MAAM,IAAI,EAAE,CAAC,YAAY,YAAY,YAAY,cAAc,YAAY,UAAU,cAAc,aAAa,GAAG,MAAM;AAAA,IACxN,YAAY,oBAAoB,YAAY;AAAA,EAC9C;AACA,MAAI,iBAAiB,SAAS;AAC5B,WAAO,MAAM,KAAK,CAAC,QAAQ,aAAa,SAAS,OAAO,GAAG,GAAG,CAAC;AAAA,EACjE;AACA,QAAM,MAAM;AAAA,IACV,KAAK;AAAA,IACL;AAAA,EACF;AACA,aAAWC,MAAK,eAAe;AAC7B,UAAM,MAAM,IAAI,MAAMA,EAAC,MAAM,SAAS,IAAI,MAAMA,EAAC,IAAI,EAAEA,EAAC;AACxD,QAAI,QAAQ,QAAQ;AAClB,UAAI,EAAEA,OAAM,eAAeA,OAAM,iBAAiBA,OAAM,eAAe,qBAAqB,IAAI,IAAI,GAAG,GAAG;AACxG,YAAIA,OAAM,aAAa,cAAcA,EAAC,IAAI;AAAA,MAC5C;AACA,aAAO,IAAI,MAAMA,EAAC;AAAA,IACpB;AAAA,EACF;AACA,MAAI,IAAI,MAAM,MAAM;AAClB,QAAI,cAAc;AAClB,WAAO,IAAI,MAAM;AAAA,EACnB;AACA,MAAI,IAAI,QAAQ,UAAU;AACxB,QAAI,OAAO,IAAI,cAAc,UAAU;AACrC,UAAI,YAAY,KAAK,UAAU,IAAI,SAAS;AAC5C,UAAI,MAAM,OAAO,IAAI,MAAM,QAAQ;AAAA,IACrC;AAAA,EACF;AACA,SAAO,MAAM,QAAQ,IAAI,MAAM,OAAO,IAAI,IAAI,MAAM,QAAQ,IAAI,CAAC,OAAO,EAAE,GAAG,KAAK,OAAO,EAAE,GAAG,IAAI,OAAO,SAAS,EAAE,EAAE,EAAE,IAAI;AAC9H;AACA,SAAS,yBAAyB,KAAK,GAAG;AAzU1C;AA0UE,QAAM,MAAM,QAAQ,UAAU,MAAM;AACpC,MAAI,KAAK,OAAO,MAAM,YAAY,CAAC,MAAM,QAAQ,CAAC,GAAG;AACnD,QAAI,OAAO,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,CAACA,IAAG,EAAE,MAAM,QAAQ,UAAU,GAAGA,EAAC,IAAI,EAAE,KAAKA,EAAC;AAAA,EAClG;AACA,UAAO,YAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,CAAC,MAAzC,mBAA4C,MAAM,KAAK,OAAO,CAAC,MAAM,QAAQ,EAAE,KAAK,CAAC,GAAG,KAAK;AACtG;AACA,SAAS,qBAAqB,OAAO,SAAS,MAAM,YAAY;AAC9D,WAAS,IAAI,YAAY,IAAI,KAAK,QAAQ,KAAK,GAAG;AAChD,UAAMA,KAAI,KAAK,CAAC;AAChB,QAAIA,OAAM,WAAWA,OAAM,SAAS;AAClC,YAAMA,EAAC,IAAI,yBAAyBA,IAAG,MAAMA,EAAC,CAAC;AAC/C;AAAA,IACF;AACA,QAAI,MAAMA,EAAC,aAAa,SAAS;AAC/B,aAAO,MAAMA,EAAC,EAAE,KAAK,CAAC,QAAQ;AAC5B,cAAMA,EAAC,IAAI;AACX,eAAO,qBAAqB,OAAO,SAAS,MAAM,CAAC;AAAA,MACrD,CAAC;AAAA,IACH;AACA,QAAI,CAAC,WAAW,CAAC,cAAc,IAAIA,EAAC,GAAG;AACrC,YAAM,IAAI,OAAO,MAAMA,EAAC,CAAC;AACzB,YAAM,YAAYA,GAAE,WAAW,OAAO;AACtC,UAAI,MAAM,UAAU,MAAM,IAAI;AAC5B,cAAMA,EAAC,IAAI,YAAY,SAAS;AAAA,MAClC,WAAW,CAAC,MAAMA,EAAC,GAAG;AACpB,YAAI,aAAa,MAAM;AACrB,gBAAMA,EAAC,IAAI;AAAA;AAEX,iBAAO,MAAMA,EAAC;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,eAAe,OAAO,UAAU,OAAO;AAC9C,QAAM,gBAAgB,qBAAqB,OAAO,SAAS,OAAO,KAAK,KAAK,GAAG,CAAC;AAChF,MAAI,yBAAyB,SAAS;AACpC,WAAO,cAAc,KAAK,MAAM,KAAK;AAAA,EACvC;AACA,SAAO;AACT;AACA,IAAM,gBAAgB;AACtB,SAAS,yBAAyB,UAAU,aAAa,YAAY;AACnE,WAAS,IAAI,YAAY,IAAI,YAAY,QAAQ,KAAK,GAAG;AACvD,UAAM,OAAO,YAAY,CAAC;AAC1B,QAAI,gBAAgB,SAAS;AAC3B,aAAO,KAAK,KAAK,CAAC,QAAQ;AACxB,oBAAY,CAAC,IAAI;AACjB,eAAO,yBAAyB,UAAU,aAAa,CAAC;AAAA,MAC1D,CAAC;AAAA,IACH;AACA,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,eAAS,KAAK,GAAG,IAAI;AAAA,IACvB,OAAO;AACL,eAAS,KAAK,IAAI;AAAA,IACpB;AAAA,EACF;AACF;AACA,SAAS,mBAAmB,GAAG;AAC7B,QAAM,cAAc,CAAC;AACrB,QAAM,QAAQ,EAAE;AAChB,aAAWA,MAAK,OAAO;AACrB,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,OAAOA,EAAC,GAAG;AACnD;AAAA,IACF;AACA,UAAM,IAAI,MAAMA,EAAC;AACjB,QAAI,MAAM,UAAU,CAAC,cAAc,IAAIA,EAAC,GAAG;AACzC;AAAA,IACF;AACA,QAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,iBAAW,SAAS,GAAG;AACrB,oBAAY,KAAK,aAAaA,IAAG,OAAO,CAAC,CAAC;AAAA,MAC5C;AACA;AAAA,IACF;AACA,gBAAY,KAAK,aAAaA,IAAG,GAAG,CAAC,CAAC;AAAA,EACxC;AACA,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAO,CAAC;AAAA,EACV;AACA,QAAM,WAAW,CAAC;AAClB,SAAO,SAAS,yBAAyB,UAAU,aAAa,CAAC,GAAG,MAAM,SAAS,IAAI,CAAC,GAAG,MAAM;AAC/F,MAAE,KAAK,EAAE;AACT,MAAE,SAAS,EAAE,KAAK,EAAE;AACpB,MAAE,MAAM,EAAE,MAAM,iBAAiB;AACjC,WAAO;AAAA,EACT,CAAC,CAAC;AACJ;AA+GA,IAAM,gBAAgC,oBAAI,IAAI,CAAC,UAAU,WAAW,WAAW,cAAc,aAAa,CAAC;AAG3G,IAAM,cAAc;AAAA;AAAA,EAElB,MAAM;AAAA,EACN,OAAO;AACT;AACA,IAAM,cAAc;AAAA;AAAA,EAElB,UAAU;AAAA,EACV,MAAM;AAAA,EACN,KAAK;AACP;AACA,SAAS,UAAU,KAAK;AACtB,QAAM,WAAW,IAAI;AACrB,MAAI,OAAO,aAAa;AACtB,WAAO;AACT,MAAI,SAAS;AACb,MAAI,IAAI,QAAQ,QAAQ;AACtB,QAAI,IAAI,MAAM,YAAY,MAAM;AAC9B,eAAS;AAAA,aACF,IAAI,MAAM;AACjB,eAAS;AAAA,aACF,IAAI,MAAM,SAAS;AAC1B,eAAS;AAAA,EACb,WAAW,IAAI,QAAQ,UAAU,IAAI,MAAM,QAAQ,cAAc;AAC/D,aAAS;AAAA,EACX,WAAW,IAAI,OAAO,aAAa;AACjC,aAAS,YAAY,IAAI,GAAG;AAAA,EAC9B;AACA,MAAI,YAAY,YAAY,aAAa;AACvC,WAAO,SAAS,YAAY,QAAQ;AAAA,EACtC;AACA,SAAO;AACT;AACA,IAAM,gBAAgB,CAAC,EAAE,QAAQ,WAAW,QAAQ,GAAG,GAAG,EAAE,QAAQ,UAAU,QAAQ,EAAE,CAAC;AAEzF,IAAM,wBAAwB,CAAC,QAAQ,YAAY,YAAY;AAC/D,SAAS,aAAa,KAAK;AACzB,QAAM,EAAE,OAAO,KAAK,QAAQ,IAAI;AAChC,MAAI,WAAW,IAAI,OAAO;AACxB,WAAO;AACT,MAAI,YAAY,UAAU,MAAM,QAAQ;AACtC,WAAO;AACT,MAAI,MAAM;AACR,WAAO;AACT,MAAI,MAAM,IAAI;AACZ,WAAO,GAAG,OAAO,OAAO,MAAM,EAAE;AAAA,EAClC;AACA,aAAW,KAAK,uBAAuB;AACrC,QAAI,MAAM,CAAC,MAAM,QAAQ;AACvB,aAAO,GAAG,OAAO,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,IACpC;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,SAAS;AACf,SAAS,IAAIC,IAAG,OAAO,SAAS,OAAO;AA1kBvC;AA2kBE,MAAI;AACJ,MAAI,UAAU,OAAO,UAAU,aAAa;AAC1C,UAAMA,GAAE;AAAA,EACV,WAAW,MAAM,SAAS,GAAG,GAAG;AAC9B,UAAM,WAAW,MAAM,QAAQ,GAAG;AAClC,WAAM,KAAAA,GAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,MAA9B,mBAAkC,MAAM,UAAU,WAAW,CAAC;AAAA,EACtE,OAAO;AACL,UAAMA,GAAE,KAAK;AAAA,EACf;AACA,MAAI,QAAQ,QAAQ;AAClB,WAAO,UAAU,OAAO,IAAI,QAAQ,MAAM,KAAK,IAAI,OAAO;AAAA,EAC5D;AACA,SAAO;AACT;AACA,IAAM,WAAW,IAAI,OAAO,GAAG,MAAM,UAAU,MAAM,MAAM,GAAG;AAC9D,SAAS,sBAAsB,GAAGA,IAAG,KAAK,SAAS,OAAO;AACxD,MAAI,OAAO,MAAM,YAAY,CAAC,EAAE,SAAS,GAAG;AAC1C,WAAO;AACT,MAAI,UAAU;AACd,MAAI;AACF,cAAU,UAAU,CAAC;AAAA,EACvB,QAAQ;AAAA,EACR;AACA,QAAM,SAAS,QAAQ,MAAM,iBAAiB;AAC9C,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,QAAM,YAAY,EAAE,SAAS,MAAM;AACnC,MAAI,EAAE,QAAQ,mBAAmB,CAAC,UAAU;AAC1C,QAAI,UAAU,UAAU,CAAC,OAAO,SAAS,KAAK,GAAG;AAC/C,aAAO;AAAA,IACT;AACA,UAAM,KAAK,IAAIA,IAAG,MAAM,MAAM,CAAC,GAAG,MAAM;AACxC,WAAO,OAAO,SAAS,KAAK;AAAA,EAC9B,CAAC,EAAE,KAAK;AACR,MAAI,WAAW;AACb,QAAI,EAAE,SAAS,MAAM;AACnB,UAAI,EAAE,MAAM,GAAG,CAAC,OAAO,MAAM;AAC/B,QAAI,EAAE,WAAW,MAAM;AACrB,UAAI,EAAE,MAAM,OAAO,MAAM;AAC3B,QAAI,EAAE,QAAQ,UAAU,GAAG,EAAE,KAAK;AAAA,EACpC;AACA,SAAO;AACT;AAEA,SAAS,qBAAqB,UAAU,OAAO;AAC7C,MAAI,YAAY;AACd,WAAO,SAAS;AAClB,MAAI,OAAO,aAAa;AACtB,WAAO,SAAS,KAAK;AACvB,SAAO;AACT;;;AC5nBA,eAAe,cAAc,MAAM,UAAU,CAAC,GAAG;AAC/C,QAAM,MAAM,QAAQ,YAAY,KAAK,gBAAgB;AACrD,MAAI,CAAC,OAAO,CAAC,KAAK;AAChB;AACF,QAAM,kBAAkB,EAAE,cAAc,MAAM,MAAM,CAAC,EAAE;AACvD,QAAM,KAAK,MAAM,SAAS,oBAAoB,eAAe;AAC7D,MAAI,CAAC,gBAAgB;AACnB;AACF,MAAI,KAAK,mBAAmB;AAC1B,WAAO,KAAK;AAAA,EACd;AACA,OAAK,oBAAoB,IAAI,QAAQ,OAAO,YAAY;AAb1D;AAcI,UAAM,QAAQ,MAAM,KAAK,YAAY,GAAG,IAAI,CAAC,SAAS;AAAA,MACpD;AAAA,MACA,IAAI,eAAe,IAAI,IAAI,GAAG,IAAI,QAAQ,GAAG,IAAI,IAAI;AAAA,MACrD,cAAc;AAAA,IAChB,EAAE;AACF,QAAI,QAAQ,KAAK;AACjB,QAAI,CAAC,OAAO;AACV,cAAQ;AAAA,QACN,OAAO,EAAE,WAAW,IAAI,iBAAiB,WAAW,IAAI,KAAK;AAAA,MAC/D;AACA,YAAM,kBAAkC,oBAAI,IAAI;AAChD,iBAAW,OAAO,CAAC,QAAQ,MAAM,GAAG;AAClC,cAAM,YAAW,SAAI,GAAG,MAAP,mBAAU;AAC3B,mBAAW,KAAK,UAAU;AACxB,gBAAM,MAAM,EAAE,QAAQ,YAAY;AAClC,cAAI,CAAC,eAAe,IAAI,GAAG,GAAG;AAC5B;AAAA,UACF;AACA,gBAAM,IAAI;AAAA,YACR;AAAA,YACA,OAAO,MAAM;AAAA,cACX,EAAE,kBAAkB,EAAE,OAAO,CAAC,OAAO,UAAU,EAAE,GAAG,OAAO,CAAC,IAAI,GAAG,EAAE,aAAa,IAAI,EAAE,IAAI,CAAC,CAAC;AAAA,YAChG;AAAA,YACA,WAAW,EAAE;AAAA,UACf;AACA,gBAAM,YAAY,aAAa,CAAC;AAChC,cAAI,IAAI;AACR,cAAI,IAAI;AACR,iBAAO,KAAK,gBAAgB,IAAI,CAAC;AAC/B,gBAAI,GAAG,SAAS,IAAI,GAAG;AACzB,cAAI,GAAG;AACL,cAAE,KAAK;AACP,4BAAgB,IAAI,CAAC;AAAA,UACvB;AACA,gBAAM,MAAM,EAAE,aAAa,UAAU,KAAK,QAAQ,CAAC,CAAC,IAAI;AAAA,QAC1D;AAAA,MACF;AAAA,IACF;AACA,UAAM,qBAAqB,EAAE,GAAG,MAAM,YAAY;AAClD,UAAM,cAAc,CAAC;AACrB,aAAS,MAAM,IAAI,OAAO,IAAI;AAC5B,YAAMC,KAAI,GAAG,EAAE,IAAI,KAAK;AACxB,YAAM,YAAYA,EAAC,IAAI;AACvB,aAAO,MAAM,mBAAmBA,EAAC;AAAA,IACnC;AACA,aAAS,SAAS,EAAE,IAAI,KAAK,IAAI,GAAG;AAClC,YAAM,YAAY,IAAI,IAAI,SAAS,OAAO;AAC1C,YAAM,MAAM,EAAE,IAAI;AAClB,UAAI,CAAC,WAAW;AACd,YAAI,IAAI,eAAe,IAAI,gBAAgB,IAAI,aAAa;AAC1D,cAAI,cAAc,IAAI;AAAA,QACxB;AACA,YAAI,IAAI,aAAa,IAAI,cAAc,IAAI,WAAW;AACpD,cAAI,YAAY,IAAI;AAAA,QACtB;AACA,cAAM,IAAI,MAAM,MAAM;AArE9B,cAAAC;AAsEU,WAAAA,MAAA,MAAM,MAAM,EAAE,MAAd,gBAAAA,IAAiB;AACjB,iBAAO,MAAM,MAAM,EAAE;AAAA,QACvB,CAAC;AAAA,MACH;AACA,UAAI,IAAI,gBAAgB;AACtB,mBAAWD,MAAK,IAAI,gBAAgB;AAClC,cAAI,CAAC,OAAO,UAAU,eAAe,KAAK,IAAI,gBAAgBA,EAAC,GAAG;AAChE;AAAA,UACF;AACA,cAAI,IAAI,aAAa,QAAQA,EAAC,EAAE,MAAM,IAAI;AACxC,aAAC,IAAI,QAAQ,cAAc,IAAI,cAAc,KAAK;AAAA;AAAA,cAEhDA,GAAE,UAAU,CAAC;AAAA,cACb,IAAI,eAAeA,EAAC,EAAE,KAAK,GAAG;AAAA,YAChC;AACA,gBAAI,aAAa,QAAQA,EAAC,IAAI,EAAE;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AACA,iBAAWA,MAAK,IAAI,OAAO;AACzB,YAAI,CAAC,OAAO,UAAU,eAAe,KAAK,IAAI,OAAOA,EAAC,GAAG;AACvD;AAAA,QACF;AACA,cAAM,QAAQ,IAAI,MAAMA,EAAC;AACzB,cAAM,KAAK,QAAQA,EAAC;AACpB,YAAIA,OAAM,SAAS;AACjB,cAAI,CAAC,OAAO;AACV;AAAA,UACF;AACA,qBAAW,KAAK,MAAM,MAAM,GAAG,GAAG;AAChC,yBAAa,MAAM,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,MAAM,IAAI,UAAU,OAAO,CAAC,CAAC;AAClE,aAAC,IAAI,UAAU,SAAS,CAAC,KAAK,IAAI,UAAU,IAAI,CAAC;AAAA,UACnD;AAAA,QACF,WAAWA,OAAM,SAAS;AACxB,cAAI,CAAC,OAAO;AACV;AAAA,UACF;AACA,qBAAW,KAAK,MAAM,MAAM,GAAG,GAAG;AAChC,kBAAM,YAAY,EAAE,QAAQ,GAAG;AAC/B,kBAAME,MAAK,EAAE,UAAU,GAAG,SAAS,EAAE,KAAK;AAC1C,kBAAM,IAAI,EAAE,UAAU,YAAY,CAAC,EAAE,KAAK;AAC1C,kBAAM,IAAI,GAAG,EAAE,IAAIA,GAAE,IAAI,MAAM;AAC7B,kBAAI,MAAM,eAAeA,GAAE;AAAA,YAC7B,CAAC;AACD,gBAAI,MAAM,YAAYA,KAAI,CAAC;AAAA,UAC7B;AAAA,QACF,OAAO;AACL,cAAI,aAAaF,EAAC,MAAM,SAAS,IAAI,aAAaA,IAAG,UAAU,OAAO,KAAK,OAAO,KAAK,CAAC;AACxF,uBAAa,MAAM,IAAI,IAAI,MAAM,IAAI,gBAAgBA,EAAC,CAAC;AAAA,QACzD;AAAA,MACF;AAAA,IACF;AACA,UAAM,UAAU,CAAC;AACjB,UAAM,OAAO;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,MAAM;AAAA,IACR;AACA,eAAW,OAAO,MAAM;AACtB,YAAM,EAAE,KAAK,cAAc,GAAG,IAAI;AAClC,UAAI,CAAC;AACH;AACF,UAAI,IAAI,QAAQ,SAAS;AACvB,YAAI,QAAQ,IAAI;AAChB;AAAA,MACF;AACA,UAAI,MAAM,IAAI,OAAO,MAAM,MAAM,EAAE;AACnC,UAAI,IAAI,KAAK;AACX,iBAAS,GAAG;AAAA,MACd,WAAW,eAAe,IAAI,IAAI,GAAG,GAAG;AACtC,gBAAQ,KAAK,GAAG;AAAA,MAClB;AAAA,IACF;AACA,eAAW,OAAO,SAAS;AACzB,YAAM,MAAM,IAAI,IAAI,eAAe;AACnC,UAAI,MAAM,IAAI,cAAc,IAAI,IAAI,GAAG;AACvC,eAAS,GAAG;AACZ,WAAK,GAAG,IAAI,KAAK,GAAG,KAAK,IAAI,uBAAuB;AACpD,WAAK,GAAG,EAAE,YAAY,IAAI,GAAG;AAAA,IAC/B;AACA,eAAW,OAAO;AAChB,YAAM,KAAK,MAAM,SAAS,iBAAiB,KAAK,KAAK,KAAK;AAC5D,SAAK,QAAQ,IAAI,KAAK,YAAY,KAAK,IAAI;AAC3C,SAAK,YAAY,IAAI,KAAK,aAAa,KAAK,UAAU,IAAI,KAAK,UAAU;AACzE,SAAK,aAAa,IAAI,KAAK,YAAY,KAAK,SAAS;AACrD,eAAWA,MAAK,MAAM,oBAAoB;AACxC,YAAM,mBAAmBA,EAAC,EAAE;AAAA,IAC9B;AACA,SAAK,OAAO;AACZ,UAAM,KAAK,MAAM,SAAS,gBAAgB,EAAE,SAAS,KAAK,CAAC;AAC3D,YAAQ;AAAA,EACV,CAAC,EAAE,QAAQ,MAAM;AACf,SAAK,oBAAoB;AACzB,SAAK,QAAQ;AAAA,EACf,CAAC;AACD,SAAO,KAAK;AACd;AAEA,SAAS,uBAAuB,MAAM,UAAU,CAAC,GAAG;AAClD,QAAM,KAAK,QAAQ,YAAY,CAAC,QAAQ,WAAW,KAAK,EAAE;AAC1D,SAAO,KAAK,6BAA6B,KAAK,8BAA8B,IAAI,QAAQ,CAAC,YAAY,GAAG,MAAM;AAC5G,WAAO,cAAc,MAAM,OAAO,EAAE,KAAK,MAAM;AAC7C,aAAO,KAAK;AACZ,cAAQ;AAAA,IACV,CAAC;AAAA,EACH,CAAC,CAAC;AACJ;AAGA,SAAS,UAAU,SAAS;AAC1B,SAAO,iBAAiB,CAAC,SAAS;AApLpC;AAqLI,UAAM,mBAAiB,gBAAK,gBAAgB,aAArB,mBAA+B,KAAK,cAAc,mCAAlD,mBAAkF,cAAa;AACtH,QAAI,gBAAgB;AAClB,WAAK,KAAK,KAAK,MAAM,cAAc,CAAC;AAAA,IACtC;AACA,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,QACL,mBAAmB,CAAC,UAAU;AAC5B,iCAAuB,OAAO,OAAO;AAAA,QACvC;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;AClMA,SAAS,UAAU,aAAa,QAAQ,CAAC,GAAG,YAAY;AACtD,aAAW,OAAO,aAAa;AAC7B,UAAM,UAAU,YAAY,GAAG;AAC/B,UAAM,OAAO,aAAa,GAAG,UAAU,IAAI,GAAG,KAAK;AACnD,QAAI,OAAO,YAAY,YAAY,YAAY,MAAM;AACnD,gBAAU,SAAS,OAAO,IAAI;AAAA,IAChC,WAAW,OAAO,YAAY,YAAY;AACxC,YAAM,IAAI,IAAI;AAAA,IAChB;AAAA,EACF;AACA,SAAO;AACT;AA6BA,IAAM,cAAc,EAAE,KAAK,CAAC,cAAc,UAAU,EAAE;AACtD,IAAM,cAAc,MAAM;AAC1B,IAAM,aAAa,OAAO,QAAQ,eAAe,cAAc,QAAQ,aAAa;AACpF,SAAS,iBAAiB,OAAO,MAAM;AACrC,QAAM,OAAO,KAAK,MAAM;AACxB,QAAM,OAAO,WAAW,IAAI;AAC5B,SAAO,MAAM;AAAA,IACX,CAAC,SAAS,iBAAiB,QAAQ,KAAK,MAAM,KAAK,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,CAAC;AAAA,IACnF,QAAQ,QAAQ;AAAA,EAClB;AACF;AACA,SAAS,mBAAmB,OAAO,MAAM;AACvC,QAAM,OAAO,KAAK,MAAM;AACxB,QAAM,OAAO,WAAW,IAAI;AAC5B,SAAO,QAAQ,IAAI,MAAM,IAAI,CAAC,SAAS,KAAK,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC;AACvE;AAUA,SAAS,aAAa,WAAW,MAAM;AACrC,aAAW,YAAY,CAAC,GAAG,SAAS,GAAG;AACrC,aAAS,IAAI;AAAA,EACf;AACF;AAEA,IAAM,WAAN,MAAe;AAAA,EACb,cAAc;AACZ,SAAK,SAAS,CAAC;AACf,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,sBAAsB;AAC3B,SAAK,mBAAmB,CAAC;AACzB,SAAK,OAAO,KAAK,KAAK,KAAK,IAAI;AAC/B,SAAK,WAAW,KAAK,SAAS,KAAK,IAAI;AACvC,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAAA,EACjD;AAAA,EACA,KAAK,MAAM,WAAW,UAAU,CAAC,GAAG;AAClC,QAAI,CAAC,QAAQ,OAAO,cAAc,YAAY;AAC5C,aAAO,MAAM;AAAA,MACb;AAAA,IACF;AACA,UAAM,eAAe;AACrB,QAAI;AACJ,WAAO,KAAK,iBAAiB,IAAI,GAAG;AAClC,YAAM,KAAK,iBAAiB,IAAI;AAChC,aAAO,IAAI;AAAA,IACb;AACA,QAAI,OAAO,CAAC,QAAQ,iBAAiB;AACnC,UAAI,UAAU,IAAI;AAClB,UAAI,CAAC,SAAS;AACZ,kBAAU,GAAG,YAAY,+BAA+B,IAAI,KAAK,gBAAgB,IAAI,EAAE,KAAK;AAAA,MAC9F;AACA,UAAI,CAAC,KAAK,qBAAqB;AAC7B,aAAK,sBAAsC,oBAAI,IAAI;AAAA,MACrD;AACA,UAAI,CAAC,KAAK,oBAAoB,IAAI,OAAO,GAAG;AAC1C,gBAAQ,KAAK,OAAO;AACpB,aAAK,oBAAoB,IAAI,OAAO;AAAA,MACtC;AAAA,IACF;AACA,QAAI,CAAC,UAAU,MAAM;AACnB,UAAI;AACF,eAAO,eAAe,WAAW,QAAQ;AAAA,UACvC,KAAK,MAAM,MAAM,KAAK,QAAQ,QAAQ,GAAG,IAAI;AAAA,UAC7C,cAAc;AAAA,QAChB,CAAC;AAAA,MACH,QAAQ;AAAA,MACR;AAAA,IACF;AACA,SAAK,OAAO,IAAI,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC;AAC1C,SAAK,OAAO,IAAI,EAAE,KAAK,SAAS;AAChC,WAAO,MAAM;AACX,UAAI,WAAW;AACb,aAAK,WAAW,MAAM,SAAS;AAC/B,oBAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,MAAM,WAAW;AACxB,QAAI;AACJ,QAAI,YAAY,IAAI,eAAe;AACjC,UAAI,OAAO,WAAW,YAAY;AAChC,eAAO;AAAA,MACT;AACA,eAAS;AACT,kBAAY;AACZ,aAAO,UAAU,GAAG,UAAU;AAAA,IAChC;AACA,aAAS,KAAK,KAAK,MAAM,SAAS;AAClC,WAAO;AAAA,EACT;AAAA,EACA,WAAW,MAAM,WAAW;AAC1B,QAAI,KAAK,OAAO,IAAI,GAAG;AACrB,YAAM,QAAQ,KAAK,OAAO,IAAI,EAAE,QAAQ,SAAS;AACjD,UAAI,UAAU,IAAI;AAChB,aAAK,OAAO,IAAI,EAAE,OAAO,OAAO,CAAC;AAAA,MACnC;AACA,UAAI,KAAK,OAAO,IAAI,EAAE,WAAW,GAAG;AAClC,eAAO,KAAK,OAAO,IAAI;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc,MAAM,YAAY;AAC9B,SAAK,iBAAiB,IAAI,IAAI,OAAO,eAAe,WAAW,EAAE,IAAI,WAAW,IAAI;AACpF,UAAM,SAAS,KAAK,OAAO,IAAI,KAAK,CAAC;AACrC,WAAO,KAAK,OAAO,IAAI;AACvB,eAAW,QAAQ,QAAQ;AACzB,WAAK,KAAK,MAAM,IAAI;AAAA,IACtB;AAAA,EACF;AAAA,EACA,eAAe,iBAAiB;AAC9B,WAAO,OAAO,KAAK,kBAAkB,eAAe;AACpD,eAAW,QAAQ,iBAAiB;AAClC,WAAK,cAAc,MAAM,gBAAgB,IAAI,CAAC;AAAA,IAChD;AAAA,EACF;AAAA,EACA,SAAS,aAAa;AACpB,UAAM,QAAQ,UAAU,WAAW;AACnC,UAAM,YAAY,OAAO,KAAK,KAAK,EAAE;AAAA,MACnC,CAAC,QAAQ,KAAK,KAAK,KAAK,MAAM,GAAG,CAAC;AAAA,IACpC;AACA,WAAO,MAAM;AACX,iBAAW,SAAS,UAAU,OAAO,GAAG,UAAU,MAAM,GAAG;AACzD,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,aAAa;AACvB,UAAM,QAAQ,UAAU,WAAW;AACnC,eAAW,OAAO,OAAO;AACvB,WAAK,WAAW,KAAK,MAAM,GAAG,CAAC;AAAA,IACjC;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,eAAW,OAAO,KAAK,QAAQ;AAC7B,aAAO,KAAK,OAAO,GAAG;AAAA,IACxB;AAAA,EACF;AAAA,EACA,SAAS,SAAS,YAAY;AAC5B,eAAW,QAAQ,IAAI;AACvB,WAAO,KAAK,aAAa,kBAAkB,MAAM,GAAG,UAAU;AAAA,EAChE;AAAA,EACA,iBAAiB,SAAS,YAAY;AACpC,eAAW,QAAQ,IAAI;AACvB,WAAO,KAAK,aAAa,oBAAoB,MAAM,GAAG,UAAU;AAAA,EAClE;AAAA,EACA,aAAa,QAAQ,SAAS,YAAY;AACxC,UAAM,QAAQ,KAAK,WAAW,KAAK,SAAS,EAAE,MAAM,MAAM,YAAY,SAAS,CAAC,EAAE,IAAI;AACtF,QAAI,KAAK,SAAS;AAChB,mBAAa,KAAK,SAAS,KAAK;AAAA,IAClC;AACA,UAAM,SAAS;AAAA,MACb,QAAQ,KAAK,SAAS,CAAC,GAAG,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC;AAAA,MAChD;AAAA,IACF;AACA,QAAI,kBAAkB,SAAS;AAC7B,aAAO,OAAO,QAAQ,MAAM;AAC1B,YAAI,KAAK,UAAU,OAAO;AACxB,uBAAa,KAAK,QAAQ,KAAK;AAAA,QACjC;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,KAAK,UAAU,OAAO;AACxB,mBAAa,KAAK,QAAQ,KAAK;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,UAAU,KAAK,WAAW,CAAC;AAChC,SAAK,QAAQ,KAAK,SAAS;AAC3B,WAAO,MAAM;AACX,UAAI,KAAK,YAAY,QAAQ;AAC3B,cAAM,QAAQ,KAAK,QAAQ,QAAQ,SAAS;AAC5C,YAAI,UAAU,IAAI;AAChB,eAAK,QAAQ,OAAO,OAAO,CAAC;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,WAAW;AACnB,SAAK,SAAS,KAAK,UAAU,CAAC;AAC9B,SAAK,OAAO,KAAK,SAAS;AAC1B,WAAO,MAAM;AACX,UAAI,KAAK,WAAW,QAAQ;AAC1B,cAAM,QAAQ,KAAK,OAAO,QAAQ,SAAS;AAC3C,YAAI,UAAU,IAAI;AAChB,eAAK,OAAO,OAAO,OAAO,CAAC;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,cAAc;AACrB,SAAO,IAAI,SAAS;AACtB;;;AC3OA,IAAM,oBAAoC,oBAAI,IAAI,CAAC,kBAAkB,aAAa,WAAW,CAAC;AAC9F,IAAM,eAAe,iBAAiB;AAAA,EACpC,OAAO;AAAA,IACL,iBAAiB,CAAC,EAAE,IAAI,MAAM;AAC5B,UAAI,IAAI,MAAM,KAAK;AACjB,YAAI,MAAM,IAAI,MAAM;AACpB,eAAO,IAAI,MAAM;AAAA,MACnB;AACA,UAAI,IAAI,MAAM,MAAM;AAClB,YAAI,MAAM,IAAI,MAAM;AACpB,eAAO,IAAI,MAAM;AAAA,MACnB;AACA,UAAI,IAAI,MAAM,KAAK;AACjB,YAAI,MAAM,IAAI,MAAM;AACpB,eAAO,IAAI,MAAM;AAAA,MACnB;AACA,YAAM,eAAe,aAAa,GAAG;AACrC,UAAI,gBAAgB,CAAC,aAAa,WAAW,UAAU,KAAK,CAAC,aAAa,WAAW,eAAe,GAAG;AACrG,eAAO,IAAI;AAAA,MACb;AACA,YAAM,SAAS,iBAAiB,IAAI,MAAM,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,KAAK;AACpE,UAAI;AACF,YAAI,KAAK;AAAA,IACb;AAAA,IACA,gBAAgB,CAAC,QAAQ;AACvB,YAAM,WAA2B,uBAAO,OAAO,IAAI;AACnD,iBAAW,OAAO,IAAI,MAAM;AAC1B,cAAM,aAAa,IAAI,MAAM,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,KAAK,IAAI,OAAO,QAAQ,GAAG;AAC7E,cAAM,WAAW,SAAS,SAAS;AACnC,YAAI,UAAU;AACZ,cAAI,WAAW,2BAAK;AACpB,cAAI,CAAC,YAAY,kBAAkB,IAAI,IAAI,GAAG;AAC5C,uBAAW;AACb,cAAI,aAAa,SAAS;AACxB,kBAAM,WAAW,SAAS;AAC1B,gBAAI,SAAS,SAAS,IAAI,MAAM,OAAO;AACrC,kBAAI,SAAS,MAAM,SAAS,MAAM,SAAS,CAAC,MAAM,KAAK;AACrD,yBAAS,SAAS;AAAA,cACpB;AACA,kBAAI,MAAM,QAAQ,GAAG,SAAS,KAAK,IAAI,IAAI,MAAM,KAAK;AAAA,YACxD;AACA,gBAAI,SAAS,SAAS,IAAI,MAAM,OAAO;AACrC,kBAAI,MAAM,QAAQ,GAAG,SAAS,KAAK,IAAI,IAAI,MAAM,KAAK;AAAA,YACxD,WAAW,SAAS,OAAO;AACzB,kBAAI,MAAM,QAAQ,SAAS;AAAA,YAC7B;AACA,qBAAS,SAAS,EAAE,QAAQ;AAAA,cAC1B,GAAG;AAAA,cACH,GAAG,IAAI;AAAA,YACT;AACA;AAAA,UACF,WAAW,IAAI,OAAO,SAAS,IAAI;AACjC,qBAAS,SAAS,SAAS,UAAU,CAAC;AACtC,gBAAI,KAAK,GAAG,SAAS,EAAE,IAAI,SAAS,OAAO,SAAS,CAAC;AACrD,qBAAS,OAAO,KAAK,GAAG;AACxB;AAAA,UACF,WAAW,UAAU,GAAG,IAAI,UAAU,QAAQ,GAAG;AAC/C;AAAA,UACF;AAAA,QACF;AACA,cAAM,WAAW,IAAI,aAAa,IAAI,eAAe,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW;AACvF,YAAI,CAAC,YAAY,eAAe,IAAI,IAAI,GAAG,GAAG;AAC5C,iBAAO,SAAS,SAAS;AACzB;AAAA,QACF;AACA,iBAAS,SAAS,IAAI;AAAA,MACxB;AACA,YAAM,UAAU,CAAC;AACjB,iBAAW,OAAO,UAAU;AAC1B,cAAM,MAAM,SAAS,GAAG;AACxB,cAAM,QAAQ,IAAI;AAClB,gBAAQ,KAAK,GAAG;AAChB,YAAI,OAAO;AACT,iBAAO,IAAI;AACX,kBAAQ,KAAK,GAAG,KAAK;AAAA,QACvB;AAAA,MACF;AACA,UAAI,OAAO;AACX,UAAI,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,EAAE,EAAE,QAAQ,WAAW,EAAE,MAAM,QAAQ,EAAE,MAAM,aAAa,CAAC,EAAE,MAAM,QAAQ;AAAA,IACjH;AAAA,EACF;AACF,CAAC;AAED,IAAM,iBAAiC,oBAAI,IAAI,CAAC,UAAU,QAAQ,WAAW,CAAC;AAC9E,IAAM,sBAAsB,iBAAiB,CAAC,UAAU;AAAA,EACtD,OAAO;AAAA,IACL,gBAAgB,CAAC,QAAQ;AACvB,iBAAW,OAAO,IAAI,MAAM;AAC1B,YAAI,CAAC,eAAe,IAAI,IAAI,GAAG,GAAG;AAChC;AAAA,QACF;AACA,cAAM,QAAQ,IAAI;AAClB,mBAAW,OAAO,OAAO;AACvB,cAAI,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,KAAK;AACpC;AAAA,UACF;AACA,cAAI,CAAC,OAAO,UAAU,eAAe,KAAK,OAAO,GAAG,GAAG;AACrD;AAAA,UACF;AACA,gBAAM,QAAQ,MAAM,GAAG;AACvB,cAAI,OAAO,UAAU,YAAY;AAC/B;AAAA,UACF;AACA,cAAI,KAAK,OAAO,cAAc,IAAI,GAAG,GAAG;AACtC,kBAAM,GAAG,IAAI,gBAAgB,GAAG;AAAA,UAClC,OAAO;AACL,mBAAO,MAAM,GAAG;AAAA,UAClB;AACA,cAAI,iBAAiB,IAAI,kBAAkB,CAAC;AAC5C,cAAI,eAAe,GAAG,IAAI;AAAA,QAC5B;AACA,YAAI,KAAK,OAAO,IAAI,mBAAmB,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO;AACvE,cAAI,MAAM,IAAI,OAAO,SAAS,IAAI,MAAM,OAAO,IAAI,MAAM,IAAI;AAAA,QAC/D;AAAA,MACF;AAAA,IACF;AAAA,IACA,iBAAiB,CAAC,EAAE,KAAK,IAAI,MAAM;AAzHvC;AA0HM,YAAM,UAAU,2BAAK;AACrB,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AACA,iBAAWG,MAAK,SAAS;AACvB,YAAI,CAACA,GAAE,SAAS,OAAO,GAAG;AACxB;AAAA,QACF;AACA,cAAM,KAAKA,GAAE,MAAM,GAAG,EAAE;AACxB,YAAI,CAAC,cAAc,IAAI,EAAE,GAAG;AAC1B;AAAA,QACF;AACA,wBAAI,mBAAJ,mBAAqB,QAArB,mBAA0B,KAAK,KAAK,IAAI,MAAM,GAAG,UAAU,CAAC,CAAC;AAAA,MAC/D;AAAA,IACF;AAAA,EACF;AACF,EAAE;AAEF,IAAM,eAA+B,oBAAI,IAAI,CAAC,QAAQ,SAAS,UAAU,UAAU,CAAC;AACpF,IAAM,kBAAkB,iBAAiB;AAAA,EACvC,OAAO;AAAA,IACL,iBAAiB,CAAC,EAAE,IAAI,MAAM;AAC5B,UAAI,IAAI,OAAO,aAAa,IAAI,IAAI,GAAG,GAAG;AACxC,YAAI,MAAM,UAAU,IAAI,IAAI,KAAK,SAAS,IAAI,GAAG;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAED,IAAM,gBAAgB,iBAAiB;AAAA,EACrC,MAAM;AAAA,EACN,OAAO;AAAA,IACL,sBAAsB,CAAC,QAAQ;AAC7B,YAAM,UAAU,CAAC;AACjB,UAAI,aAAa;AACjB,iBAAW,OAAO,IAAI,MAAM;AAC1B,YAAI,IAAI,OAAO,YAAY,IAAI,QAAQ,mBAAmB,IAAI,QAAQ,oBAAoB,IAAI,QAAQ,SAAS;AAC7G;AAAA,QACF;AACA,gBAAQ,IAAI,GAAG,IAAI,IAAI,QAAQ,WAAW,IAAI,QAAQ,kBAAkB,IAAI,cAAc,IAAI;AAC9F,qBAAa;AAAA,MACf;AACA,UAAI,YAAY;AACd,YAAI,KAAK,KAAK;AAAA,UACZ,KAAK;AAAA,UACL,WAAW,KAAK,UAAU,OAAO;AAAA,UACjC,OAAO,EAAE,IAAI,kBAAkB,MAAM,mBAAmB;AAAA,QAC1D,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAED,IAAM,aAAa,iBAAiB;AAAA,EAClC,OAAO;AAAA,IACL,gBAAgB,CAAC,QAAQ;AAjL7B;AAkLM,iBAAW,OAAO,IAAI,MAAM;AAC1B,YAAI,OAAO,IAAI,gBAAgB,UAAU;AACvC;AAAA,QACF;AACA,mBAAW,EAAE,QAAQ,OAAO,KAAK,eAAe;AAC9C,cAAI,CAAC,IAAI,YAAY,WAAW,MAAM,GAAG;AACvC;AAAA,UACF;AACA,gBAAM,MAAM,IAAI,YAAY,UAAU,OAAO,MAAM;AACnD,gBAAM,YAAW,SAAI,KAAK,KAAK,CAAC,SAAS,KAAK,OAAO,GAAG,MAAvC,mBAA0C;AAC3D,cAAI,aAAa,QAAQ;AACvB,gBAAI,KAAK,WAAW;AACpB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,KAAK,KAAK,CAAC,GAAG,MAAM;AACtB,cAAM,UAAU,UAAU,CAAC;AAC3B,cAAM,UAAU,UAAU,CAAC;AAC3B,YAAI,UAAU,SAAS;AACrB,iBAAO;AAAA,QACT,WAAW,UAAU,SAAS;AAC5B,iBAAO;AAAA,QACT;AACA,eAAO,EAAE,KAAK,EAAE;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;AAED,IAAM,iBAAiB;AAAA,EACrB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,WAAW;AACb;AACA,IAAM,eAAe,CAAC,aAAa,aAAa;AAChD,IAAM,uBAAuB,iBAAiB,CAAC,UAAU;AAAA,EACvD,OAAO;AAAA,IACL,gBAAgB,CAAC,QAAQ;AAxN7B;AAyNM,YAAM,EAAE,KAAK,IAAI;AACjB,UAAI;AACJ,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,cAAM,MAAM,KAAK,CAAC;AAClB,YAAI,IAAI,QAAQ,kBAAkB;AAChC;AAAA,QACF;AACA,yBAAiB,IAAI,KAAK,OAAO,GAAG,CAAC,EAAE,CAAC,EAAE;AAC1C,aAAK;AAAA,MACP;AACA,YAAM,SAAS,kBAAkB,CAAC;AAClC,YAAM,MAAM,OAAO,aAAa;AAChC,aAAO,OAAO;AACd,aAAO,YAAY;AAAA;AAAA,QAEjB,OAAO,eAAa,UAAK,KAAK,CAAC,QAAQ,IAAI,QAAQ,OAAO,MAAtC,mBAAyC,gBAAe;AAAA,QAC5E;AAAA,QACA;AAAA,MACF;AACA,iBAAW,OAAO,MAAM;AACtB,YAAI,IAAI,0BAA0B,OAAO;AACvC;AAAA,QACF;AACA,cAAM,IAAI,eAAe,IAAI,GAAG;AAChC,YAAI,KAAK,OAAO,IAAI,MAAM,CAAC,MAAM,UAAU;AACzC,cAAI,MAAM,CAAC,IAAI,sBAAsB,IAAI,MAAM,CAAC,GAAG,QAAQ,GAAG;AAAA,QAChE,WAAW,IAAI,yBAAyB,IAAI,QAAQ,mBAAmB,IAAI,QAAQ,SAAS;AAC1F,qBAAWC,MAAK,cAAc;AAC5B,gBAAI,OAAO,IAAIA,EAAC,MAAM;AACpB,kBAAIA,EAAC,IAAI,sBAAsB,IAAIA,EAAC,GAAG,QAAQ,KAAK,IAAI,QAAQ,YAAY,IAAI,MAAM,KAAK,SAAS,MAAM,CAAC;AAAA,UAC/G;AAAA,QACF;AAAA,MACF;AACA,WAAK,kBAAkB;AACvB,WAAK,aAAa;AAAA,IACpB;AAAA,IACA,qBAAqB,CAAC,EAAE,KAAK,MAAM;AACjC,UAAI;AACJ,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,cAAM,MAAM,KAAK,CAAC;AAClB,YAAI,IAAI,QAAQ,WAAW,IAAI,0BAA0B,OAAO;AAC9D,kBAAQ;AAAA,QACV;AAAA,MACF;AACA,UAAI,+BAAO,aAAa;AACtB,cAAM,cAAc,sBAAsB,MAAM,aAAa,KAAK,iBAAiB,KAAK,UAAU;AAAA,MACpG;AAAA,IACF;AAAA,EACF;AACF,EAAE;AAEF,IAAM,sBAAsB,iBAAiB;AAAA,EAC3C,OAAO;AAAA,IACL,gBAAgB,CAAC,QAAQ;AACvB,YAAM,EAAE,KAAK,IAAI;AACjB,UAAI;AACJ,UAAI;AACJ,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACvC,cAAM,MAAM,KAAK,CAAC;AAClB,YAAI,IAAI,QAAQ,SAAS;AACvB,qBAAW;AAAA,QACb,WAAW,IAAI,QAAQ,iBAAiB;AACtC,6BAAmB;AAAA,QACrB;AAAA,MACF;AACA,UAAI,oBAAoB,UAAU;AAChC,cAAM,WAAW;AAAA,UACf,iBAAiB;AAAA,UACjB,SAAS;AAAA,QACX;AACA,YAAI,aAAa,MAAM;AACrB,mBAAS,cAAc,YAAY,SAAS;AAAA,QAC9C,OAAO;AACL,cAAI,KAAK,OAAO,IAAI,KAAK,QAAQ,QAAQ,GAAG,CAAC;AAAA,QAC/C;AAAA,MACF,WAAW,kBAAkB;AAC3B,cAAM,WAAW;AAAA,UACf,iBAAiB;AAAA,QACnB;AACA,YAAI,aAAa,MAAM;AACrB,2BAAiB,cAAc;AAC/B,2BAAiB,MAAM;AACvB,6BAAmB;AAAA,QACrB;AAAA,MACF;AACA,UAAI,kBAAkB;AACpB,YAAI,KAAK,OAAO,IAAI,KAAK,QAAQ,gBAAgB,GAAG,CAAC;AAAA,MACvD;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAED,IAAM,YAAY,iBAAiB;AAAA,EACjC,OAAO;AAAA,IACL,qBAAqB,CAAC,QAAQ;AAC5B,iBAAW,OAAO,IAAI,MAAM;AAC1B,YAAI,OAAO,IAAI,cAAc,UAAU;AACrC,cAAI,IAAI,cAAc,IAAI,MAAM,SAAS,yBAAyB,IAAI,MAAM,SAAS,qBAAqB;AACxG,gBAAI,YAAY,IAAI,UAAU,QAAQ,MAAM,SAAS;AAAA,UACvD,OAAO;AACL,gBAAI,YAAY,IAAI,UAAU,QAAQ,IAAI,OAAO,KAAK,IAAI,GAAG,IAAI,GAAG,GAAG,OAAO,IAAI,GAAG,EAAE;AAAA,UACzF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAED,IAAI;AAEJ,SAAS,WAAW,UAAU,CAAC,GAAG;AAChC,QAAM,OAAO,eAAe,OAAO;AACnC,OAAK,IAAI,UAAU,CAAC;AACpB,SAAO,aAAa;AACtB;AAKA,SAAS,WAAW,MAAM,KAAK;AAC7B,SAAO,CAAC,QAAQ,SAAS,YAAY,OAAO,SAAS,YAAY,CAAC;AACpE;AACA,SAAS,eAAe,UAAU,CAAC,GAAG;AACpC,QAAM,QAAQ,YAAY;AAC1B,QAAM,SAAS,QAAQ,SAAS,CAAC,CAAC;AAClC,UAAQ,WAAW,QAAQ,aAAa,YAAY,WAAW;AAC/D,QAAM,MAAM,CAAC,QAAQ;AACrB,QAAM,UAAU,MAAM;AACpB,SAAK,QAAQ;AACb,UAAM,SAAS,mBAAmB,IAAI;AAAA,EACxC;AACA,MAAI,aAAa;AACjB,MAAI,UAAU,CAAC;AACf,QAAM,UAAU,CAAC;AACjB,QAAM,OAAO;AAAA,IACX;AAAA,IACA,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB;AAAA,IACA,cAAc;AACZ,aAAO;AAAA,IACT;AAAA,IACA,IAAIC,IAAG;AACL,YAAM,SAAS,OAAOA,OAAM,aAAaA,GAAE,IAAI,IAAIA;AACnD,UAAI,CAAC,OAAO,OAAO,CAAC,QAAQ,KAAK,CAACC,QAAOA,IAAG,QAAQ,OAAO,GAAG,GAAG;AAC/D,gBAAQ,KAAK,MAAM;AACnB,mBAAW,OAAO,MAAM,GAAG,KAAK,MAAM,SAAS,OAAO,SAAS,CAAC,CAAC;AAAA,MACnE;AAAA,IACF;AAAA,IACA,KAAK,OAAO,cAAc;AACxB,wDAAqB;AACrB,YAAM,QAAQ;AAAA,QACZ,IAAI;AAAA,QACJ;AAAA,QACA,GAAG;AAAA,MACL;AACA,UAAI,WAAW,MAAM,MAAM,GAAG,GAAG;AAC/B,gBAAQ,KAAK,KAAK;AAClB,gBAAQ;AAAA,MACV;AACA,aAAO;AAAA,QACL,UAAU;AACR,oBAAU,QAAQ,OAAO,CAAC,MAAM,EAAE,OAAO,MAAM,EAAE;AACjD,kBAAQ;AAAA,QACV;AAAA;AAAA,QAEA,MAAM,QAAQ;AACZ,qBAAW,KAAK,SAAS;AACvB,gBAAI,EAAE,OAAO,MAAM,IAAI;AACrB,gBAAE,QAAQ,MAAM,QAAQ;AAAA,YAC1B;AAAA,UACF;AACA,kBAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,IACA,MAAM,cAAc;AAClB,YAAM,aAAa,EAAE,MAAM,CAAC,GAAG,SAAS,CAAC,GAAG,OAAO,EAAE;AACrD,YAAM,MAAM,SAAS,mBAAmB,UAAU;AAClD,iBAAW,SAAS,WAAW,SAAS;AACtC,cAAM,WAAW,MAAM,iBAAiB,MAAM;AAC9C,cAAM,gBAAgB,OAAO,MAAM,YAAY,MAAM,UAAU,QAAQ,IAAI;AAC3E,YAAI,MAAM,eAAe;AACvB,qBAAW,OAAO,MAAM,mBAAmB,KAAK,GAAG;AACjD,kBAAM,SAAS,EAAE,KAAK,OAAO,iBAAiB,KAAK,gBAAgB;AACnE,kBAAM,MAAM,SAAS,iBAAiB,MAAM;AAC5C,uBAAW,KAAK,KAAK,OAAO,GAAG;AAAA,UACjC;AAAA,QACF;AAAA,MACF;AACA,YAAM,MAAM,SAAS,sBAAsB,UAAU;AACrD,YAAM,MAAM,SAAS,gBAAgB,UAAU;AAC/C,YAAM,MAAM,SAAS,qBAAqB,UAAU;AACpD,aAAO,WAAW;AAAA,IACpB;AAAA,IACA;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,IAAG,mCAAS,YAAW,CAAC;AAAA,EAC1B,EAAE,QAAQ,CAACD,OAAM,KAAK,IAAIA,EAAC,CAAC;AAC5B,OAAK,MAAM,SAAS,QAAQ,IAAI;AAChC,SAAO;AACT;AAyBA,IAAM,oBAAoB,OAAO,mBAAmB;AACpD,SAAS,cAAc;AACvB;AACA,YAAY,iBAAiB,IAAI;;;ACncjC,IAAM,OAAO,QAAQ,CAAC,MAAM;AAE5B,SAAS,aAAa,GAAG;AACvB,SAAO,OAAO,MAAM,aAAa,EAAE,IAAI,MAAM,CAAC;AAChD;AACA,SAAS,sBAAsBE,MAAK;AAClC,MAAIA,gBAAe,WAAWA,gBAAe,QAAQA,gBAAe;AAClE,WAAOA;AACT,QAAM,OAAO,aAAaA,IAAG;AAC7B,MAAI,CAACA,QAAO,CAAC;AACX,WAAO;AACT,MAAI,MAAM,QAAQ,IAAI;AACpB,WAAO,KAAK,IAAI,CAAC,MAAM,sBAAsB,CAAC,CAAC;AACjD,MAAI,OAAO,SAAS,UAAU;AAC5B,UAAM,WAAW,CAAC;AAClB,eAAWC,MAAK,MAAM;AACpB,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,MAAMA,EAAC,GAAG;AAClD;AAAA,MACF;AACA,UAAIA,OAAM,mBAAmBA,GAAE,CAAC,MAAM,OAAOA,GAAE,CAAC,MAAM,KAAK;AACzD,iBAASA,EAAC,IAAI,MAAM,KAAKA,EAAC,CAAC;AAC3B;AAAA,MACF;AACA,eAASA,EAAC,IAAI,sBAAsB,KAAKA,EAAC,CAAC;AAAA,IAC7C;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,IAAM,sBAAsB,iBAAiB;AAAA,EAC3C,OAAO;AAAA,IACL,mBAAmB,CAAC,QAAQ;AAC1B,iBAAW,SAAS,IAAI;AACtB,cAAM,gBAAgB,sBAAsB,MAAM,KAAK;AAAA,IAC3D;AAAA,EACF;AACF,CAAC;AAED,IAAM,aAAa;AACnB,SAAS,WAAW,MAAM;AACxB,QAAM,SAAS;AAAA,IACb,QAAQ,KAAK;AACX,UAAI,MAAM;AACR,YAAI,OAAO,iBAAiB,UAAU;AACtC,YAAI,OAAO,iBAAiB,QAAQ;AACpC,YAAI,QAAQ,YAAY,IAAI;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AACA,SAAO,OAAO;AAChB;AAOA,SAASC,YAAW,UAAU,CAAC,GAAG;AAChC,UAAQ,aAAa,QAAQ,eAAe,CAAC,OAAO,SAAS,MAAM,WAAW,MAAM,GAAG,GAAG,CAAC,CAAC;AAC5F,QAAM,OAAO,WAAa,OAAO;AACjC,OAAK,IAAI,mBAAmB;AAC5B,OAAK,UAAU,WAAW,IAAI;AAC9B,SAAO;AACT;;;AC5DA,IAAM,sBAAsB;AAAA,EAC1B;AACF;AACA,IAAM,8BAA8B;AAAA,EAClC,eAAe,CAAC,GAAG,qBAAqB,GAAG,eAAe;AAC5D;;;ACbO,SAAS,wBAAwB;AACpC,SAAO,UAAU,EAAE;AACvB;AACO,SAAS,YAAY;AAExB,SAAQ,OAAO,cAAc,eAAe,OAAO,WAAW,cACxD,SACA,OAAO,eAAe,cAClB,aACA,CAAC;AACf;AACO,IAAM,mBAAmB,OAAO,UAAU;;;ACX1C,IAAM,aAAa;AACnB,IAAM,2BAA2B;;;ACDxC,IAAI;AACJ,IAAI;AACG,SAAS,yBAAyB;AACrC,MAAI;AACJ,MAAI,cAAc,QAAW;AACzB,WAAO;AAAA,EACX;AACA,MAAI,OAAO,WAAW,eAAe,OAAO,aAAa;AACrD,gBAAY;AACZ,WAAO,OAAO;AAAA,EAClB,WACS,OAAO,eAAe,iBAAiB,KAAK,WAAW,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc;AAC9H,gBAAY;AACZ,WAAO,WAAW,WAAW;AAAA,EACjC,OACK;AACD,gBAAY;AAAA,EAChB;AACA,SAAO;AACX;AACO,SAAS,MAAM;AAClB,SAAO,uBAAuB,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI;AAC5D;;;ACpBO,IAAM,WAAN,MAAe;AAAA,EAClB,YAAY,QAAQ,MAAM;AACtB,SAAK,SAAS;AACd,SAAK,cAAc,CAAC;AACpB,SAAK,UAAU,CAAC;AAChB,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,UAAM,kBAAkB,CAAC;AACzB,QAAI,OAAO,UAAU;AACjB,iBAAW,MAAM,OAAO,UAAU;AAC9B,cAAM,OAAO,OAAO,SAAS,EAAE;AAC/B,wBAAgB,EAAE,IAAI,KAAK;AAAA,MAC/B;AAAA,IACJ;AACA,UAAM,sBAAsB,mCAAmC,OAAO,EAAE;AACxE,QAAI,kBAAkB,OAAO,OAAO,CAAC,GAAG,eAAe;AACvD,QAAI;AACA,YAAM,MAAM,aAAa,QAAQ,mBAAmB;AACpD,YAAM,OAAO,KAAK,MAAM,GAAG;AAC3B,aAAO,OAAO,iBAAiB,IAAI;AAAA,IACvC,SACO,GAAG;AAAA,IAEV;AACA,SAAK,YAAY;AAAA,MACb,cAAc;AACV,eAAO;AAAA,MACX;AAAA,MACA,YAAY,OAAO;AACf,YAAI;AACA,uBAAa,QAAQ,qBAAqB,KAAK,UAAU,KAAK,CAAC;AAAA,QACnE,SACO,GAAG;AAAA,QAEV;AACA,0BAAkB;AAAA,MACtB;AAAA,MACA,MAAM;AACF,eAAO,IAAI;AAAA,MACf;AAAA,IACJ;AACA,QAAI,MAAM;AACN,WAAK,GAAG,0BAA0B,CAAC,UAAU,UAAU;AACnD,YAAI,aAAa,KAAK,OAAO,IAAI;AAC7B,eAAK,UAAU,YAAY,KAAK;AAAA,QACpC;AAAA,MACJ,CAAC;AAAA,IACL;AACA,SAAK,YAAY,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3B,KAAK,CAAC,SAAS,SAAS;AACpB,YAAI,KAAK,QAAQ;AACb,iBAAO,KAAK,OAAO,GAAG,IAAI;AAAA,QAC9B,OACK;AACD,iBAAO,IAAI,SAAS;AAChB,iBAAK,QAAQ,KAAK;AAAA,cACd,QAAQ;AAAA,cACR;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,SAAK,gBAAgB,IAAI,MAAM,CAAC,GAAG;AAAA,MAC/B,KAAK,CAAC,SAAS,SAAS;AACpB,YAAI,KAAK,QAAQ;AACb,iBAAO,KAAK,OAAO,IAAI;AAAA,QAC3B,WACS,SAAS,MAAM;AACpB,iBAAO,KAAK;AAAA,QAChB,WACS,OAAO,KAAK,KAAK,SAAS,EAAE,SAAS,IAAI,GAAG;AACjD,iBAAO,IAAI,SAAS;AAChB,iBAAK,YAAY,KAAK;AAAA,cAClB,QAAQ;AAAA,cACR;AAAA,cACA,SAAS,MAAM;AAAA,cAAE;AAAA,YACrB,CAAC;AACD,mBAAO,KAAK,UAAU,IAAI,EAAE,GAAG,IAAI;AAAA,UACvC;AAAA,QACJ,OACK;AACD,iBAAO,IAAI,SAAS;AAChB,mBAAO,IAAI,QAAQ,CAAC,YAAY;AAC5B,mBAAK,YAAY,KAAK;AAAA,gBAClB,QAAQ;AAAA,gBACR;AAAA,gBACA;AAAA,cACJ,CAAC;AAAA,YACL,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,MAAM,cAAc,QAAQ;AACxB,SAAK,SAAS;AACd,eAAW,QAAQ,KAAK,SAAS;AAC7B,WAAK,OAAO,GAAG,KAAK,MAAM,EAAE,GAAG,KAAK,IAAI;AAAA,IAC5C;AACA,eAAW,QAAQ,KAAK,aAAa;AACjC,WAAK,QAAQ,MAAM,KAAK,OAAO,KAAK,MAAM,EAAE,GAAG,KAAK,IAAI,CAAC;AAAA,IAC7D;AAAA,EACJ;AACJ;;;ACpGO,SAAS,oBAAoB,kBAAkB,SAAS;AAC3D,QAAM,aAAa;AACnB,QAAM,SAAS,UAAU;AACzB,QAAM,OAAO,sBAAsB;AACnC,QAAM,cAAc,oBAAoB,WAAW;AACnD,MAAI,SAAS,OAAO,yCAAyC,CAAC,cAAc;AACxE,SAAK,KAAK,YAAY,kBAAkB,OAAO;AAAA,EACnD,OACK;AACD,UAAM,QAAQ,cAAc,IAAI,SAAS,YAAY,IAAI,IAAI;AAC7D,UAAM,OAAO,OAAO,2BAA2B,OAAO,4BAA4B,CAAC;AACnF,SAAK,KAAK;AAAA,MACN,kBAAkB;AAAA,MAClB;AAAA,MACA;AAAA,IACJ,CAAC;AACD,QAAI,OAAO;AACP,cAAQ,MAAM,aAAa;AAAA,IAC/B;AAAA,EACJ;AACJ;;;AClBA,IAAM,YAAY,OAAO,aAAa;AAQtC,SAAS,iBAAiB,WAAW;AACjC,SAAQ,OAAO,cAAc,YACzB,iBAAiB,aACjB,WAAW,aACX,eAAe;AACvB;AACA,SAAS,WAAW,KAAK;AACrB,SAAQ,IAAI,cACR,IAAI,OAAO,WAAW,MAAM;AAAA;AAAA,EAG3B,IAAI,WAAW,iBAAiB,IAAI,OAAO;AACpD;AACA,IAAM,SAAS,OAAO;AACtB,SAAS,cAAc,IAAI,QAAQ;AAC/B,QAAM,YAAY,CAAC;AACnB,aAAW,OAAO,QAAQ;AACtB,UAAM,QAAQ,OAAO,GAAG;AACxB,cAAU,GAAG,IAAI,QAAQ,KAAK,IACxB,MAAM,IAAI,EAAE,IACZ,GAAG,KAAK;AAAA,EAClB;AACA,SAAO;AACX;AACA,IAAM,OAAO,MAAM;AAAE;AAKrB,IAAM,UAAU,MAAM;AAEtB,SAAS,KAAK,KAAK;AAEf,QAAM,OAAO,MAAM,KAAK,SAAS,EAAE,MAAM,CAAC;AAC1C,UAAQ,KAAK,MAAM,SAAS,CAAC,wBAAwB,GAAG,EAAE,OAAO,IAAI,CAAC;AAC1E;AAqBA,IAAM,UAAU;AAChB,IAAM,eAAe;AACrB,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,UAAU;AAehB,IAAM,sBAAsB;AAC5B,IAAM,uBAAuB;AAC7B,IAAM,eAAe;AACrB,IAAM,kBAAkB;AACxB,IAAM,oBAAoB;AAC1B,IAAM,cAAc;AACpB,IAAM,qBAAqB;AAC3B,IAAM,eAAe;AASrB,SAAS,aAAa,MAAM;AACxB,SAAO,UAAU,KAAK,IAAI,EACrB,QAAQ,aAAa,GAAG,EACxB,QAAQ,qBAAqB,GAAG,EAChC,QAAQ,sBAAsB,GAAG;AAC1C;AAOA,SAAS,WAAW,MAAM;AACtB,SAAO,aAAa,IAAI,EACnB,QAAQ,mBAAmB,GAAG,EAC9B,QAAQ,oBAAoB,GAAG,EAC/B,QAAQ,cAAc,GAAG;AAClC;AAQA,SAAS,iBAAiB,MAAM;AAC5B,SAAQ,aAAa,IAAI,EAEpB,QAAQ,SAAS,KAAK,EACtB,QAAQ,cAAc,GAAG,EACzB,QAAQ,SAAS,KAAK,EACtB,QAAQ,cAAc,KAAK,EAC3B,QAAQ,iBAAiB,GAAG,EAC5B,QAAQ,mBAAmB,GAAG,EAC9B,QAAQ,oBAAoB,GAAG,EAC/B,QAAQ,cAAc,GAAG;AAClC;AAMA,SAAS,eAAe,MAAM;AAC1B,SAAO,iBAAiB,IAAI,EAAE,QAAQ,UAAU,KAAK;AACzD;AAOA,SAAS,WAAW,MAAM;AACtB,SAAO,aAAa,IAAI,EAAE,QAAQ,SAAS,KAAK,EAAE,QAAQ,OAAO,KAAK;AAC1E;AAUA,SAAS,YAAY,MAAM;AACvB,SAAO,QAAQ,OAAO,KAAK,WAAW,IAAI,EAAE,QAAQ,UAAU,KAAK;AACvE;AAQA,SAAS,OAAO,MAAM;AAClB,MAAI;AACA,WAAO,mBAAmB,KAAK,IAAI;AAAA,EACvC,SACO,KAAK;AACR,IAA2C,KAAK,mBAAmB,IAAI,yBAAyB;AAAA,EACpG;AACA,SAAO,KAAK;AAChB;AAEA,IAAM,oBAAoB;AAC1B,IAAM,sBAAsB,CAAC,SAAS,KAAK,QAAQ,mBAAmB,EAAE;AAUxE,SAAS,SAASC,aAAYC,WAAU,kBAAkB,KAAK;AAC3D,MAAI,MAAM,QAAQ,CAAC,GAAG,eAAe,IAAI,OAAO;AAGhD,QAAM,UAAUA,UAAS,QAAQ,GAAG;AACpC,MAAI,YAAYA,UAAS,QAAQ,GAAG;AAEpC,MAAI,UAAU,aAAa,WAAW,GAAG;AACrC,gBAAY;AAAA,EAChB;AACA,MAAI,YAAY,IAAI;AAChB,WAAOA,UAAS,MAAM,GAAG,SAAS;AAClC,mBAAeA,UAAS,MAAM,YAAY,GAAG,UAAU,KAAK,UAAUA,UAAS,MAAM;AACrF,YAAQD,YAAW,YAAY;AAAA,EACnC;AACA,MAAI,UAAU,IAAI;AACd,WAAO,QAAQC,UAAS,MAAM,GAAG,OAAO;AAExC,WAAOA,UAAS,MAAM,SAASA,UAAS,MAAM;AAAA,EAClD;AAEA,SAAO,oBAAoB,QAAQ,OAAO,OAAOA,WAAU,eAAe;AAE1E,SAAO;AAAA,IACH,UAAU,QAAQ,gBAAgB,OAAO,eAAe;AAAA,IACxD;AAAA,IACA;AAAA,IACA,MAAM,OAAO,IAAI;AAAA,EACrB;AACJ;AAOA,SAAS,aAAaC,iBAAgBD,WAAU;AAC5C,QAAM,QAAQA,UAAS,QAAQC,gBAAeD,UAAS,KAAK,IAAI;AAChE,SAAOA,UAAS,QAAQ,SAAS,OAAO,SAASA,UAAS,QAAQ;AACtE;AAOA,SAAS,UAAU,UAAU,MAAM;AAE/B,MAAI,CAAC,QAAQ,CAAC,SAAS,YAAY,EAAE,WAAW,KAAK,YAAY,CAAC;AAC9D,WAAO;AACX,SAAO,SAAS,MAAM,KAAK,MAAM,KAAK;AAC1C;AAUA,SAAS,oBAAoBC,iBAAgB,GAAG,GAAG;AAC/C,QAAM,aAAa,EAAE,QAAQ,SAAS;AACtC,QAAM,aAAa,EAAE,QAAQ,SAAS;AACtC,SAAQ,aAAa,MACjB,eAAe,cACf,kBAAkB,EAAE,QAAQ,UAAU,GAAG,EAAE,QAAQ,UAAU,CAAC,KAC9D,0BAA0B,EAAE,QAAQ,EAAE,MAAM,KAC5CA,gBAAe,EAAE,KAAK,MAAMA,gBAAe,EAAE,KAAK,KAClD,EAAE,SAAS,EAAE;AACrB;AAQA,SAAS,kBAAkB,GAAG,GAAG;AAI7B,UAAQ,EAAE,WAAW,QAAQ,EAAE,WAAW;AAC9C;AACA,SAAS,0BAA0B,GAAG,GAAG;AACrC,MAAI,OAAO,KAAK,CAAC,EAAE,WAAW,OAAO,KAAK,CAAC,EAAE;AACzC,WAAO;AACX,aAAW,OAAO,GAAG;AACjB,QAAI,CAAC,+BAA+B,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAC9C,aAAO;AAAA,EACf;AACA,SAAO;AACX;AACA,SAAS,+BAA+B,GAAG,GAAG;AAC1C,SAAO,QAAQ,CAAC,IACV,kBAAkB,GAAG,CAAC,IACtB,QAAQ,CAAC,IACL,kBAAkB,GAAG,CAAC,IACtB,MAAM;AACpB;AAQA,SAAS,kBAAkB,GAAG,GAAG;AAC7B,SAAO,QAAQ,CAAC,IACV,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,CAAC,OAAO,MAAM,UAAU,EAAE,CAAC,CAAC,IAC7D,EAAE,WAAW,KAAK,EAAE,CAAC,MAAM;AACrC;AAOA,SAAS,oBAAoB,IAAI,MAAM;AACnC,MAAI,GAAG,WAAW,GAAG;AACjB,WAAO;AACX,MAA+C,CAAC,KAAK,WAAW,GAAG,GAAG;AAClE,SAAK,mFAAmF,EAAE,WAAW,IAAI,4BAA4B,IAAI,IAAI;AAC7I,WAAO;AAAA,EACX;AACA,MAAI,CAAC;AACD,WAAO;AACX,QAAM,eAAe,KAAK,MAAM,GAAG;AACnC,QAAM,aAAa,GAAG,MAAM,GAAG;AAC/B,QAAM,gBAAgB,WAAW,WAAW,SAAS,CAAC;AAGtD,MAAI,kBAAkB,QAAQ,kBAAkB,KAAK;AACjD,eAAW,KAAK,EAAE;AAAA,EACtB;AACA,MAAI,WAAW,aAAa,SAAS;AACrC,MAAI;AACJ,MAAI;AACJ,OAAK,aAAa,GAAG,aAAa,WAAW,QAAQ,cAAc;AAC/D,cAAU,WAAW,UAAU;AAE/B,QAAI,YAAY;AACZ;AAEJ,QAAI,YAAY,MAAM;AAElB,UAAI,WAAW;AACX;AAAA,IAER;AAGI;AAAA,EACR;AACA,SAAQ,aAAa,MAAM,GAAG,QAAQ,EAAE,KAAK,GAAG,IAC5C,MACA,WAAW,MAAM,UAAU,EAAE,KAAK,GAAG;AAC7C;AAgBA,IAAM,4BAA4B;AAAA,EAC9B,MAAM;AAAA;AAAA,EAEN,MAAM;AAAA,EACN,QAAQ,CAAC;AAAA,EACT,OAAO,CAAC;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS,CAAC;AAAA,EACV,MAAM,CAAC;AAAA,EACP,gBAAgB;AACpB;AAEA,IAAI;AAAA,CACH,SAAUC,iBAAgB;AACvB,EAAAA,gBAAe,KAAK,IAAI;AACxB,EAAAA,gBAAe,MAAM,IAAI;AAC7B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAI;AAAA,CACH,SAAUC,sBAAqB;AAC5B,EAAAA,qBAAoB,MAAM,IAAI;AAC9B,EAAAA,qBAAoB,SAAS,IAAI;AACjC,EAAAA,qBAAoB,SAAS,IAAI;AACrC,GAAG,wBAAwB,sBAAsB,CAAC,EAAE;AAIpD,IAAM,QAAQ;AAQd,SAAS,cAAc,MAAM;AACzB,MAAI,CAAC,MAAM;AACP,QAAI,WAAW;AAEX,YAAM,SAAS,SAAS,cAAc,MAAM;AAC5C,aAAQ,UAAU,OAAO,aAAa,MAAM,KAAM;AAElD,aAAO,KAAK,QAAQ,mBAAmB,EAAE;AAAA,IAC7C,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AAIA,MAAI,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM;AAC/B,WAAO,MAAM;AAGjB,SAAO,oBAAoB,IAAI;AACnC;AAEA,IAAM,iBAAiB;AACvB,SAAS,WAAW,MAAMH,WAAU;AAChC,SAAO,KAAK,QAAQ,gBAAgB,GAAG,IAAIA;AAC/C;AAEA,SAAS,mBAAmB,IAAI,QAAQ;AACpC,QAAM,UAAU,SAAS,gBAAgB,sBAAsB;AAC/D,QAAM,SAAS,GAAG,sBAAsB;AACxC,SAAO;AAAA,IACH,UAAU,OAAO;AAAA,IACjB,MAAM,OAAO,OAAO,QAAQ,QAAQ,OAAO,QAAQ;AAAA,IACnD,KAAK,OAAO,MAAM,QAAQ,OAAO,OAAO,OAAO;AAAA,EACnD;AACJ;AACA,IAAM,wBAAwB,OAAO;AAAA,EACjC,MAAM,OAAO;AAAA,EACb,KAAK,OAAO;AAChB;AACA,SAAS,iBAAiB,UAAU;AAChC,MAAI;AACJ,MAAI,QAAQ,UAAU;AAClB,UAAM,aAAa,SAAS;AAC5B,UAAM,eAAe,OAAO,eAAe,YAAY,WAAW,WAAW,GAAG;AAsBhF,QAA+C,OAAO,SAAS,OAAO,UAAU;AAC5E,UAAI,CAAC,gBAAgB,CAAC,SAAS,eAAe,SAAS,GAAG,MAAM,CAAC,CAAC,GAAG;AACjE,YAAI;AACA,gBAAM,UAAU,SAAS,cAAc,SAAS,EAAE;AAClD,cAAI,gBAAgB,SAAS;AACzB,iBAAK,iBAAiB,SAAS,EAAE,sDAAsD,SAAS,EAAE,iCAAiC;AAEnI;AAAA,UACJ;AAAA,QACJ,SACO,KAAK;AACR,eAAK,iBAAiB,SAAS,EAAE,4QAA4Q;AAE7S;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,KAAK,OAAO,eAAe,WAC3B,eACI,SAAS,eAAe,WAAW,MAAM,CAAC,CAAC,IAC3C,SAAS,cAAc,UAAU,IACrC;AACN,QAAI,CAAC,IAAI;AACL,MACI,KAAK,yCAAyC,SAAS,EAAE,+BAA+B;AAC5F;AAAA,IACJ;AACA,sBAAkB,mBAAmB,IAAI,QAAQ;AAAA,EACrD,OACK;AACD,sBAAkB;AAAA,EACtB;AACA,MAAI,oBAAoB,SAAS,gBAAgB;AAC7C,WAAO,SAAS,eAAe;AAAA,OAC9B;AACD,WAAO,SAAS,gBAAgB,QAAQ,OAAO,gBAAgB,OAAO,OAAO,SAAS,gBAAgB,OAAO,OAAO,gBAAgB,MAAM,OAAO,OAAO;AAAA,EAC5J;AACJ;AACA,SAAS,aAAa,MAAM,OAAO;AAC/B,QAAM,WAAW,QAAQ,QAAQ,QAAQ,MAAM,WAAW,QAAQ;AAClE,SAAO,WAAW;AACtB;AACA,IAAM,kBAAkB,oBAAI,IAAI;AAChC,SAAS,mBAAmB,KAAK,gBAAgB;AAC7C,kBAAgB,IAAI,KAAK,cAAc;AAC3C;AACA,SAAS,uBAAuB,KAAK;AACjC,QAAM,SAAS,gBAAgB,IAAI,GAAG;AAEtC,kBAAgB,OAAO,GAAG;AAC1B,SAAO;AACX;AAiBA,IAAI,qBAAqB,MAAM,SAAS,WAAW,OAAO,SAAS;AAMnE,SAAS,sBAAsB,MAAMA,WAAU;AAC3C,QAAM,EAAE,UAAU,QAAQ,KAAK,IAAIA;AAEnC,QAAM,UAAU,KAAK,QAAQ,GAAG;AAChC,MAAI,UAAU,IAAI;AACd,QAAI,WAAW,KAAK,SAAS,KAAK,MAAM,OAAO,CAAC,IAC1C,KAAK,MAAM,OAAO,EAAE,SACpB;AACN,QAAI,eAAe,KAAK,MAAM,QAAQ;AAEtC,QAAI,aAAa,CAAC,MAAM;AACpB,qBAAe,MAAM;AACzB,WAAO,UAAU,cAAc,EAAE;AAAA,EACrC;AACA,QAAM,OAAO,UAAU,UAAU,IAAI;AACrC,SAAO,OAAO,SAAS;AAC3B;AACA,SAAS,oBAAoB,MAAM,cAAc,iBAAiB,SAAS;AACvE,MAAI,YAAY,CAAC;AACjB,MAAI,YAAY,CAAC;AAGjB,MAAI,aAAa;AACjB,QAAM,kBAAkB,CAAC,EAAE,MAAO,MAAM;AACpC,UAAM,KAAK,sBAAsB,MAAM,QAAQ;AAC/C,UAAM,OAAO,gBAAgB;AAC7B,UAAM,YAAY,aAAa;AAC/B,QAAI,QAAQ;AACZ,QAAI,OAAO;AACP,sBAAgB,QAAQ;AACxB,mBAAa,QAAQ;AAErB,UAAI,cAAc,eAAe,MAAM;AACnC,qBAAa;AACb;AAAA,MACJ;AACA,cAAQ,YAAY,MAAM,WAAW,UAAU,WAAW;AAAA,IAC9D,OACK;AACD,cAAQ,EAAE;AAAA,IACd;AAMA,cAAU,QAAQ,cAAY;AAC1B,eAAS,gBAAgB,OAAO,MAAM;AAAA,QAClC;AAAA,QACA,MAAM,eAAe;AAAA,QACrB,WAAW,QACL,QAAQ,IACJ,oBAAoB,UACpB,oBAAoB,OACxB,oBAAoB;AAAA,MAC9B,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AACA,WAAS,iBAAiB;AACtB,iBAAa,gBAAgB;AAAA,EACjC;AACA,WAAS,OAAO,UAAU;AAEtB,cAAU,KAAK,QAAQ;AACvB,UAAM,WAAW,MAAM;AACnB,YAAM,QAAQ,UAAU,QAAQ,QAAQ;AACxC,UAAI,QAAQ;AACR,kBAAU,OAAO,OAAO,CAAC;AAAA,IACjC;AACA,cAAU,KAAK,QAAQ;AACvB,WAAO;AAAA,EACX;AACA,WAAS,uBAAuB;AAC5B,UAAM,EAAE,SAAAI,SAAQ,IAAI;AACpB,QAAI,CAACA,SAAQ;AACT;AACJ,IAAAA,SAAQ,aAAa,OAAO,CAAC,GAAGA,SAAQ,OAAO,EAAE,QAAQ,sBAAsB,EAAE,CAAC,GAAG,EAAE;AAAA,EAC3F;AACA,WAAS,UAAU;AACf,eAAW,YAAY;AACnB,eAAS;AACb,gBAAY,CAAC;AACb,WAAO,oBAAoB,YAAY,eAAe;AACtD,WAAO,oBAAoB,gBAAgB,oBAAoB;AAAA,EACnE;AAEA,SAAO,iBAAiB,YAAY,eAAe;AAGnD,SAAO,iBAAiB,gBAAgB,sBAAsB;AAAA,IAC1D,SAAS;AAAA,EACb,CAAC;AACD,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAIA,SAAS,WAAW,MAAM,SAAS,SAAS,WAAW,OAAO,gBAAgB,OAAO;AACjF,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,OAAO,QAAQ;AAAA,IACzB,QAAQ,gBAAgB,sBAAsB,IAAI;AAAA,EACtD;AACJ;AACA,SAAS,0BAA0B,MAAM;AACrC,QAAM,EAAE,SAAAA,UAAS,UAAAJ,UAAS,IAAI;AAE9B,QAAM,kBAAkB;AAAA,IACpB,OAAO,sBAAsB,MAAMA,SAAQ;AAAA,EAC/C;AACA,QAAM,eAAe,EAAE,OAAOI,SAAQ,MAAM;AAE5C,MAAI,CAAC,aAAa,OAAO;AACrB,mBAAe,gBAAgB,OAAO;AAAA,MAClC,MAAM;AAAA,MACN,SAAS,gBAAgB;AAAA,MACzB,SAAS;AAAA;AAAA,MAET,UAAUA,SAAQ,SAAS;AAAA,MAC3B,UAAU;AAAA;AAAA;AAAA,MAGV,QAAQ;AAAA,IACZ,GAAG,IAAI;AAAA,EACX;AACA,WAAS,eAAe,IAAI,OAAOC,UAAS;AAUxC,UAAM,YAAY,KAAK,QAAQ,GAAG;AAClC,UAAM,MAAM,YAAY,MACjBL,UAAS,QAAQ,SAAS,cAAc,MAAM,IAC3C,OACA,KAAK,MAAM,SAAS,KAAK,KAC7B,mBAAmB,IAAI,OAAO;AACpC,QAAI;AAGA,MAAAI,SAAQC,WAAU,iBAAiB,WAAW,EAAE,OAAO,IAAI,GAAG;AAC9D,mBAAa,QAAQ;AAAA,IACzB,SACO,KAAK;AACR,UAAK,MAAwC;AACzC,aAAK,iCAAiC,GAAG;AAAA,MAC7C,OACK;AACD,gBAAQ,MAAM,GAAG;AAAA,MACrB;AAEA,MAAAL,UAASK,WAAU,YAAY,QAAQ,EAAE,GAAG;AAAA,IAChD;AAAA,EACJ;AACA,WAAS,QAAQ,IAAI,MAAM;AACvB,UAAM,QAAQ,OAAO,CAAC,GAAGD,SAAQ,OAAO;AAAA,MAAW,aAAa,MAAM;AAAA;AAAA,MAEtE;AAAA,MAAI,aAAa,MAAM;AAAA,MAAS;AAAA,IAAI,GAAG,MAAM,EAAE,UAAU,aAAa,MAAM,SAAS,CAAC;AACtF,mBAAe,IAAI,OAAO,IAAI;AAC9B,oBAAgB,QAAQ;AAAA,EAC5B;AACA,WAAS,KAAK,IAAI,MAAM;AAGpB,UAAM,eAAe;AAAA,MAAO,CAAC;AAAA;AAAA;AAAA;AAAA,MAI7B,aAAa;AAAA,MAAOA,SAAQ;AAAA,MAAO;AAAA,QAC/B,SAAS;AAAA,QACT,QAAQ,sBAAsB;AAAA,MAClC;AAAA,IAAC;AACD,QAA+C,CAACA,SAAQ,OAAO;AAC3D,WAAK;AAAA;AAAA;AAAA;AAAA,kGAEkG;AAAA,IAC3G;AACA,mBAAe,aAAa,SAAS,cAAc,IAAI;AACvD,UAAM,QAAQ,OAAO,CAAC,GAAG,WAAW,gBAAgB,OAAO,IAAI,IAAI,GAAG,EAAE,UAAU,aAAa,WAAW,EAAE,GAAG,IAAI;AACnH,mBAAe,IAAI,OAAO,KAAK;AAC/B,oBAAgB,QAAQ;AAAA,EAC5B;AACA,SAAO;AAAA,IACH,UAAU;AAAA,IACV,OAAO;AAAA,IACP;AAAA,IACA;AAAA,EACJ;AACJ;AAMA,SAAS,iBAAiB,MAAM;AAC5B,SAAO,cAAc,IAAI;AACzB,QAAM,oBAAoB,0BAA0B,IAAI;AACxD,QAAM,mBAAmB,oBAAoB,MAAM,kBAAkB,OAAO,kBAAkB,UAAU,kBAAkB,OAAO;AACjI,WAAS,GAAG,OAAO,mBAAmB,MAAM;AACxC,QAAI,CAAC;AACD,uBAAiB,eAAe;AACpC,YAAQ,GAAG,KAAK;AAAA,EACpB;AACA,QAAM,gBAAgB,OAAO;AAAA;AAAA,IAEzB,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA,YAAY,WAAW,KAAK,MAAM,IAAI;AAAA,EAC1C,GAAG,mBAAmB,gBAAgB;AACtC,SAAO,eAAe,eAAe,YAAY;AAAA,IAC7C,YAAY;AAAA,IACZ,KAAK,MAAM,kBAAkB,SAAS;AAAA,EAC1C,CAAC;AACD,SAAO,eAAe,eAAe,SAAS;AAAA,IAC1C,YAAY;AAAA,IACZ,KAAK,MAAM,kBAAkB,MAAM;AAAA,EACvC,CAAC;AACD,SAAO;AACX;AASA,SAAS,oBAAoB,OAAO,IAAI;AACpC,MAAI,YAAY,CAAC;AACjB,MAAI,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AACxB,MAAI,WAAW;AACf,SAAO,cAAc,IAAI;AACzB,WAAS,YAAYJ,WAAU,QAAQ,CAAC,GAAG;AACvC;AACA,QAAI,aAAa,MAAM,QAAQ;AAE3B,YAAM,OAAO,QAAQ;AAAA,IACzB;AACA,UAAM,KAAK,CAACA,WAAU,KAAK,CAAC;AAAA,EAChC;AACA,WAAS,iBAAiB,IAAI,MAAM,EAAE,WAAW,MAAM,GAAG;AACtD,UAAM,OAAO;AAAA,MACT;AAAA,MACA;AAAA,MACA,MAAM,eAAe;AAAA,IACzB;AACA,eAAW,YAAY,WAAW;AAC9B,eAAS,IAAI,MAAM,IAAI;AAAA,IAC3B;AAAA,EACJ;AACA,QAAM,gBAAgB;AAAA;AAAA,IAElB,UAAU;AAAA;AAAA,IAEV,OAAO,CAAC;AAAA,IACR;AAAA,IACA,YAAY,WAAW,KAAK,MAAM,IAAI;AAAA,IACtC,QAAQ,IAAI,OAAO;AAEf,YAAM,OAAO,YAAY,CAAC;AAC1B,kBAAY,IAAI,KAAK;AAAA,IACzB;AAAA,IACA,KAAK,IAAI,OAAO;AACZ,kBAAY,IAAI,KAAK;AAAA,IACzB;AAAA,IACA,OAAO,UAAU;AACb,gBAAU,KAAK,QAAQ;AACvB,aAAO,MAAM;AACT,cAAM,QAAQ,UAAU,QAAQ,QAAQ;AACxC,YAAI,QAAQ;AACR,oBAAU,OAAO,OAAO,CAAC;AAAA,MACjC;AAAA,IACJ;AAAA,IACA,UAAU;AACN,kBAAY,CAAC;AACb,cAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AACpB,iBAAW;AAAA,IACf;AAAA,IACA,GAAG,OAAO,gBAAgB,MAAM;AAC5B,YAAM,OAAO,KAAK;AAClB,YAAM;AAAA;AAAA;AAAA;AAAA,QAIN,QAAQ,IAAI,oBAAoB,OAAO,oBAAoB;AAAA;AAC3D,iBAAW,KAAK,IAAI,GAAG,KAAK,IAAI,WAAW,OAAO,MAAM,SAAS,CAAC,CAAC;AACnE,UAAI,eAAe;AACf,yBAAiB,KAAK,UAAU,MAAM;AAAA,UAClC;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,eAAe,eAAe,YAAY;AAAA,IAC7C,YAAY;AAAA,IACZ,KAAK,MAAM,MAAM,QAAQ,EAAE,CAAC;AAAA,EAChC,CAAC;AACD,SAAO,eAAe,eAAe,SAAS;AAAA,IAC1C,YAAY;AAAA,IACZ,KAAK,MAAM,MAAM,QAAQ,EAAE,CAAC;AAAA,EAChC,CAAC;AACD,SAAO;AACX;AAwCA,SAAS,gBAAgB,OAAO;AAC5B,SAAO,OAAO,UAAU,YAAa,SAAS,OAAO,UAAU;AACnE;AACA,SAAS,YAAY,MAAM;AACvB,SAAO,OAAO,SAAS,YAAY,OAAO,SAAS;AACvD;AAEA,IAAM,0BAA0B,OAAQ,OAAyC,uBAAuB,EAAE;AAK1G,IAAI;AAAA,CACH,SAAUM,wBAAuB;AAK9B,EAAAA,uBAAsBA,uBAAsB,SAAS,IAAI,CAAC,IAAI;AAK9D,EAAAA,uBAAsBA,uBAAsB,WAAW,IAAI,CAAC,IAAI;AAKhE,EAAAA,uBAAsBA,uBAAsB,YAAY,IAAI,EAAE,IAAI;AACtE,GAAG,0BAA0B,wBAAwB,CAAC,EAAE;AAExD,IAAM,oBAAoB;AAAA,EACtB;AAAA,IAAC;AAAA;AAAA,EAAoC,EAAE,EAAE,UAAAC,WAAU,gBAAgB,GAAG;AAClE,WAAO;AAAA,GAAkB,KAAK,UAAUA,SAAQ,CAAC,GAAG,kBAC9C,uBAAuB,KAAK,UAAU,eAAe,IACrD,EAAE;AAAA,EACZ;AAAA,EACA;AAAA,IAAC;AAAA;AAAA,EAA4C,EAAE,EAAE,MAAM,GAAI,GAAG;AAC1D,WAAO,oBAAoB,KAAK,QAAQ,SAAS,eAAe,EAAE,CAAC;AAAA,EACvE;AAAA,EACA;AAAA,IAAC;AAAA;AAAA,EAAqC,EAAE,EAAE,MAAM,GAAG,GAAG;AAClD,WAAO,4BAA4B,KAAK,QAAQ,SAAS,GAAG,QAAQ;AAAA,EACxE;AAAA,EACA;AAAA,IAAC;AAAA;AAAA,EAAuC,EAAE,EAAE,MAAM,GAAG,GAAG;AACpD,WAAO,8BAA8B,KAAK,QAAQ,SAAS,GAAG,QAAQ;AAAA,EAC1E;AAAA,EACA;AAAA,IAAC;AAAA;AAAA,EAAyC,EAAE,EAAE,MAAM,GAAG,GAAG;AACtD,WAAO,sDAAsD,KAAK,QAAQ;AAAA,EAC9E;AACJ;AAOA,SAAS,kBAAkB,MAAM,QAAQ;AAErC,MAAK,MAAiD;AAClD,WAAO,OAAO,IAAI,MAAM,kBAAkB,IAAI,EAAE,MAAM,CAAC,GAAG;AAAA,MACtD;AAAA,MACA,CAAC,uBAAuB,GAAG;AAAA,IAC/B,GAAG,MAAM;AAAA,EACb,OACK;AACD,WAAO,OAAO,IAAI,MAAM,GAAG;AAAA,MACvB;AAAA,MACA,CAAC,uBAAuB,GAAG;AAAA,IAC/B,GAAG,MAAM;AAAA,EACb;AACJ;AACA,SAAS,oBAAoB,OAAO,MAAM;AACtC,SAAQ,iBAAiB,SACrB,2BAA2B,UAC1B,QAAQ,QAAQ,CAAC,EAAE,MAAM,OAAO;AACzC;AACA,IAAM,kBAAkB,CAAC,UAAU,SAAS,MAAM;AAClD,SAAS,eAAe,IAAI;AACxB,MAAI,OAAO,OAAO;AACd,WAAO;AACX,MAAI,GAAG,QAAQ;AACX,WAAO,GAAG;AACd,QAAMA,YAAW,CAAC;AAClB,aAAW,OAAO,iBAAiB;AAC/B,QAAI,OAAO;AACP,MAAAA,UAAS,GAAG,IAAI,GAAG,GAAG;AAAA,EAC9B;AACA,SAAO,KAAK,UAAUA,WAAU,MAAM,CAAC;AAC3C;AAGA,IAAM,qBAAqB;AAC3B,IAAM,2BAA2B;AAAA,EAC7B,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,KAAK;AACT;AAEA,IAAM,iBAAiB;AAQvB,SAAS,eAAe,UAAU,cAAc;AAC5C,QAAM,UAAU,OAAO,CAAC,GAAG,0BAA0B,YAAY;AAEjE,QAAM,QAAQ,CAAC;AAEf,MAAI,UAAU,QAAQ,QAAQ,MAAM;AAEpC,QAAM,OAAO,CAAC;AACd,aAAW,WAAW,UAAU;AAE5B,UAAM,gBAAgB,QAAQ,SAAS,CAAC,IAAI;AAAA,MAAC;AAAA;AAAA,IAAuB;AAEpE,QAAI,QAAQ,UAAU,CAAC,QAAQ;AAC3B,iBAAW;AACf,aAAS,aAAa,GAAG,aAAa,QAAQ,QAAQ,cAAc;AAChE,YAAM,QAAQ,QAAQ,UAAU;AAEhC,UAAI,kBAAkB,MACjB,QAAQ,YAAY,OAA0C;AACnE,UAAI,MAAM,SAAS,GAA0B;AAEzC,YAAI,CAAC;AACD,qBAAW;AACf,mBAAW,MAAM,MAAM,QAAQ,gBAAgB,MAAM;AACrD,2BAAmB;AAAA,MACvB,WACS,MAAM,SAAS,GAAyB;AAC7C,cAAM,EAAE,OAAO,YAAY,UAAU,OAAO,IAAI;AAChD,aAAK,KAAK;AAAA,UACN,MAAM;AAAA,UACN;AAAA,UACA;AAAA,QACJ,CAAC;AACD,cAAMC,MAAK,SAAS,SAAS;AAE7B,YAAIA,QAAO,oBAAoB;AAC3B,6BAAmB;AAEnB,cAAI;AACA,gBAAI,OAAO,IAAIA,GAAE,GAAG;AAAA,UACxB,SACO,KAAK;AACR,kBAAM,IAAI,MAAM,oCAAoC,KAAK,MAAMA,GAAE,QAC7D,IAAI,OAAO;AAAA,UACnB;AAAA,QACJ;AAEA,YAAI,aAAa,aAAa,OAAOA,GAAE,WAAWA,GAAE,SAAS,IAAIA,GAAE;AAEnE,YAAI,CAAC;AACD;AAAA;AAAA,UAGI,YAAY,QAAQ,SAAS,IACvB,OAAO,UAAU,MACjB,MAAM;AACpB,YAAI;AACA,wBAAc;AAClB,mBAAW;AACX,2BAAmB;AACnB,YAAI;AACA,6BAAmB;AACvB,YAAI;AACA,6BAAmB;AACvB,YAAIA,QAAO;AACP,6BAAmB;AAAA,MAC3B;AACA,oBAAc,KAAK,eAAe;AAAA,IACtC;AAGA,UAAM,KAAK,aAAa;AAAA,EAC5B;AAEA,MAAI,QAAQ,UAAU,QAAQ,KAAK;AAC/B,UAAM,IAAI,MAAM,SAAS;AACzB,UAAM,CAAC,EAAE,MAAM,CAAC,EAAE,SAAS,CAAC,KAAK;AAAA,EACrC;AAEA,MAAI,CAAC,QAAQ;AACT,eAAW;AACf,MAAI,QAAQ;AACR,eAAW;AAAA,WAEN,QAAQ,UAAU,CAAC,QAAQ,SAAS,GAAG;AAC5C,eAAW;AACf,QAAM,KAAK,IAAI,OAAO,SAAS,QAAQ,YAAY,KAAK,GAAG;AAC3D,WAAS,MAAM,MAAM;AACjB,UAAM,QAAQ,KAAK,MAAM,EAAE;AAC3B,UAAM,SAAS,CAAC;AAChB,QAAI,CAAC;AACD,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,YAAM,QAAQ,MAAM,CAAC,KAAK;AAC1B,YAAM,MAAM,KAAK,IAAI,CAAC;AACtB,aAAO,IAAI,IAAI,IAAI,SAAS,IAAI,aAAa,MAAM,MAAM,GAAG,IAAI;AAAA,IACpE;AACA,WAAO;AAAA,EACX;AACA,WAAS,UAAU,QAAQ;AACvB,QAAI,OAAO;AAEX,QAAI,uBAAuB;AAC3B,eAAW,WAAW,UAAU;AAC5B,UAAI,CAAC,wBAAwB,CAAC,KAAK,SAAS,GAAG;AAC3C,gBAAQ;AACZ,6BAAuB;AACvB,iBAAW,SAAS,SAAS;AACzB,YAAI,MAAM,SAAS,GAA0B;AACzC,kBAAQ,MAAM;AAAA,QAClB,WACS,MAAM,SAAS,GAAyB;AAC7C,gBAAM,EAAE,OAAO,YAAY,SAAS,IAAI;AACxC,gBAAM,QAAQ,SAAS,SAAS,OAAO,KAAK,IAAI;AAChD,cAAI,QAAQ,KAAK,KAAK,CAAC,YAAY;AAC/B,kBAAM,IAAI,MAAM,mBAAmB,KAAK,2DAA2D;AAAA,UACvG;AACA,gBAAM,OAAO,QAAQ,KAAK,IACpB,MAAM,KAAK,GAAG,IACd;AACN,cAAI,CAAC,MAAM;AACP,gBAAI,UAAU;AAEV,kBAAI,QAAQ,SAAS,GAAG;AAEpB,oBAAI,KAAK,SAAS,GAAG;AACjB,yBAAO,KAAK,MAAM,GAAG,EAAE;AAAA;AAGvB,yCAAuB;AAAA,cAC/B;AAAA,YACJ;AAEI,oBAAM,IAAI,MAAM,2BAA2B,KAAK,GAAG;AAAA,UAC3D;AACA,kBAAQ;AAAA,QACZ;AAAA,MACJ;AAAA,IACJ;AAEA,WAAO,QAAQ;AAAA,EACnB;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAUA,SAAS,kBAAkB,GAAG,GAAG;AAC7B,MAAI,IAAI;AACR,SAAO,IAAI,EAAE,UAAU,IAAI,EAAE,QAAQ;AACjC,UAAM,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC;AAEvB,QAAI;AACA,aAAO;AACX;AAAA,EACJ;AAGA,MAAI,EAAE,SAAS,EAAE,QAAQ;AACrB,WAAO,EAAE,WAAW,KAAK,EAAE,CAAC,MAAM,KAA4B,KACxD,KACA;AAAA,EACV,WACS,EAAE,SAAS,EAAE,QAAQ;AAC1B,WAAO,EAAE,WAAW,KAAK,EAAE,CAAC,MAAM,KAA4B,KACxD,IACA;AAAA,EACV;AACA,SAAO;AACX;AAQA,SAAS,uBAAuB,GAAG,GAAG;AAClC,MAAI,IAAI;AACR,QAAM,SAAS,EAAE;AACjB,QAAM,SAAS,EAAE;AACjB,SAAO,IAAI,OAAO,UAAU,IAAI,OAAO,QAAQ;AAC3C,UAAM,OAAO,kBAAkB,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAEnD,QAAI;AACA,aAAO;AACX;AAAA,EACJ;AACA,MAAI,KAAK,IAAI,OAAO,SAAS,OAAO,MAAM,MAAM,GAAG;AAC/C,QAAI,oBAAoB,MAAM;AAC1B,aAAO;AACX,QAAI,oBAAoB,MAAM;AAC1B,aAAO;AAAA,EACf;AAEA,SAAO,OAAO,SAAS,OAAO;AAOlC;AAOA,SAAS,oBAAoB,OAAO;AAChC,QAAM,OAAO,MAAM,MAAM,SAAS,CAAC;AACnC,SAAO,MAAM,SAAS,KAAK,KAAK,KAAK,SAAS,CAAC,IAAI;AACvD;AAEA,IAAM,aAAa;AAAA,EACf,MAAM;AAAA,EACN,OAAO;AACX;AACA,IAAM,iBAAiB;AAIvB,SAAS,aAAa,MAAM;AACxB,MAAI,CAAC;AACD,WAAO,CAAC,CAAC,CAAC;AACd,MAAI,SAAS;AACT,WAAO,CAAC,CAAC,UAAU,CAAC;AACxB,MAAI,CAAC,KAAK,WAAW,GAAG,GAAG;AACvB,UAAM,IAAI,MAAO,OACX,yCAAyC,IAAI,iBAAiB,IAAI,OAClE,iBAAiB,IAAI,GAAG;AAAA,EAClC;AAEA,WAAS,MAAM,SAAS;AACpB,UAAM,IAAI,MAAM,QAAQ,KAAK,MAAM,MAAM,MAAM,OAAO,EAAE;AAAA,EAC5D;AACA,MAAI,QAAQ;AACZ,MAAI,gBAAgB;AACpB,QAAM,SAAS,CAAC;AAGhB,MAAI;AACJ,WAAS,kBAAkB;AACvB,QAAI;AACA,aAAO,KAAK,OAAO;AACvB,cAAU,CAAC;AAAA,EACf;AAEA,MAAI,IAAI;AAER,MAAI;AAEJ,MAAI,SAAS;AAEb,MAAI,WAAW;AACf,WAAS,gBAAgB;AACrB,QAAI,CAAC;AACD;AACJ,QAAI,UAAU,GAA+B;AACzC,cAAQ,KAAK;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX,CAAC;AAAA,IACL,WACS,UAAU,KACf,UAAU,KACV,UAAU,GAAuC;AACjD,UAAI,QAAQ,SAAS,MAAM,SAAS,OAAO,SAAS;AAChD,cAAM,uBAAuB,MAAM,8CAA8C;AACrF,cAAQ,KAAK;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY,SAAS,OAAO,SAAS;AAAA,QACrC,UAAU,SAAS,OAAO,SAAS;AAAA,MACvC,CAAC;AAAA,IACL,OACK;AACD,YAAM,iCAAiC;AAAA,IAC3C;AACA,aAAS;AAAA,EACb;AACA,WAAS,kBAAkB;AACvB,cAAU;AAAA,EACd;AACA,SAAO,IAAI,KAAK,QAAQ;AACpB,WAAO,KAAK,GAAG;AACf,QAAI,SAAS,QAAQ,UAAU,GAAoC;AAC/D,sBAAgB;AAChB,cAAQ;AACR;AAAA,IACJ;AACA,YAAQ,OAAO;AAAA,MACX,KAAK;AACD,YAAI,SAAS,KAAK;AACd,cAAI,QAAQ;AACR,0BAAc;AAAA,UAClB;AACA,0BAAgB;AAAA,QACpB,WACS,SAAS,KAAK;AACnB,wBAAc;AACd,kBAAQ;AAAA,QACZ,OACK;AACD,0BAAgB;AAAA,QACpB;AACA;AAAA,MACJ,KAAK;AACD,wBAAgB;AAChB,gBAAQ;AACR;AAAA,MACJ,KAAK;AACD,YAAI,SAAS,KAAK;AACd,kBAAQ;AAAA,QACZ,WACS,eAAe,KAAK,IAAI,GAAG;AAChC,0BAAgB;AAAA,QACpB,OACK;AACD,wBAAc;AACd,kBAAQ;AAER,cAAI,SAAS,OAAO,SAAS,OAAO,SAAS;AACzC;AAAA,QACR;AACA;AAAA,MACJ,KAAK;AAMD,YAAI,SAAS,KAAK;AAEd,cAAI,SAAS,SAAS,SAAS,CAAC,KAAK;AACjC,uBAAW,SAAS,MAAM,GAAG,EAAE,IAAI;AAAA;AAEnC,oBAAQ;AAAA,QAChB,OACK;AACD,sBAAY;AAAA,QAChB;AACA;AAAA,MACJ,KAAK;AAED,sBAAc;AACd,gBAAQ;AAER,YAAI,SAAS,OAAO,SAAS,OAAO,SAAS;AACzC;AACJ,mBAAW;AACX;AAAA,MACJ;AACI,cAAM,eAAe;AACrB;AAAA,IACR;AAAA,EACJ;AACA,MAAI,UAAU;AACV,UAAM,uCAAuC,MAAM,GAAG;AAC1D,gBAAc;AACd,kBAAgB;AAEhB,SAAO;AACX;AAEA,SAAS,yBAAyB,QAAQ,QAAQ,SAAS;AACvD,QAAM,SAAS,eAAe,aAAa,OAAO,IAAI,GAAG,OAAO;AAEhE,MAAK,MAAwC;AACzC,UAAM,eAAe,oBAAI,IAAI;AAC7B,eAAW,OAAO,OAAO,MAAM;AAC3B,UAAI,aAAa,IAAI,IAAI,IAAI;AACzB,aAAK,sCAAsC,IAAI,IAAI,eAAe,OAAO,IAAI,4DAA4D;AAC7I,mBAAa,IAAI,IAAI,IAAI;AAAA,IAC7B;AAAA,EACJ;AACA,QAAM,UAAU,OAAO,QAAQ;AAAA,IAC3B;AAAA,IACA;AAAA;AAAA,IAEA,UAAU,CAAC;AAAA,IACX,OAAO,CAAC;AAAA,EACZ,CAAC;AACD,MAAI,QAAQ;AAIR,QAAI,CAAC,QAAQ,OAAO,YAAY,CAAC,OAAO,OAAO;AAC3C,aAAO,SAAS,KAAK,OAAO;AAAA,EACpC;AACA,SAAO;AACX;AASA,SAAS,oBAAoB,QAAQ,eAAe;AAEhD,QAAM,WAAW,CAAC;AAClB,QAAM,aAAa,oBAAI,IAAI;AAC3B,kBAAgB,aAAa,EAAE,QAAQ,OAAO,KAAK,MAAM,WAAW,MAAM,GAAG,aAAa;AAC1F,WAAS,iBAAiB,MAAM;AAC5B,WAAO,WAAW,IAAI,IAAI;AAAA,EAC9B;AACA,WAAS,SAAS,QAAQ,QAAQ,gBAAgB;AAE9C,UAAM,YAAY,CAAC;AACnB,UAAM,uBAAuB,qBAAqB,MAAM;AACxD,QAAK,MAAwC;AACzC,yCAAmC,sBAAsB,MAAM;AAAA,IACnE;AAEA,yBAAqB,UAAU,kBAAkB,eAAe;AAChE,UAAM,UAAU,aAAa,eAAe,MAAM;AAElD,UAAM,oBAAoB,CAAC,oBAAoB;AAC/C,QAAI,WAAW,QAAQ;AACnB,YAAM,UAAU,OAAO,OAAO,UAAU,WAAW,CAAC,OAAO,KAAK,IAAI,OAAO;AAC3E,iBAAW,SAAS,SAAS;AACzB,0BAAkB;AAAA;AAAA;AAAA,UAGlB,qBAAqB,OAAO,CAAC,GAAG,sBAAsB;AAAA;AAAA;AAAA,YAGlD,YAAY,iBACN,eAAe,OAAO,aACtB,qBAAqB;AAAA,YAC3B,MAAM;AAAA;AAAA,YAEN,SAAS,iBACH,eAAe,SACf;AAAA;AAAA;AAAA,UAGV,CAAC,CAAC;AAAA,QAAC;AAAA,MACP;AAAA,IACJ;AACA,QAAI;AACJ,QAAI;AACJ,eAAW,oBAAoB,mBAAmB;AAC9C,YAAM,EAAE,KAAK,IAAI;AAIjB,UAAI,UAAU,KAAK,CAAC,MAAM,KAAK;AAC3B,cAAM,aAAa,OAAO,OAAO;AACjC,cAAM,kBAAkB,WAAW,WAAW,SAAS,CAAC,MAAM,MAAM,KAAK;AACzE,yBAAiB,OACb,OAAO,OAAO,QAAQ,QAAQ,kBAAkB;AAAA,MACxD;AACA,UAA+C,iBAAiB,SAAS,KAAK;AAC1E,cAAM,IAAI,MAAM,yKAC6E;AAAA,MACjG;AAEA,gBAAU,yBAAyB,kBAAkB,QAAQ,OAAO;AACpE,UAA+C,UAAU,KAAK,CAAC,MAAM;AACjE,yCAAiC,SAAS,MAAM;AAGpD,UAAI,gBAAgB;AAChB,uBAAe,MAAM,KAAK,OAAO;AACjC,YAAK,MAAwC;AACzC,0BAAgB,gBAAgB,OAAO;AAAA,QAC3C;AAAA,MACJ,OACK;AAED,0BAAkB,mBAAmB;AACrC,YAAI,oBAAoB;AACpB,0BAAgB,MAAM,KAAK,OAAO;AAGtC,YAAI,aAAa,OAAO,QAAQ,CAAC,cAAc,OAAO,GAAG;AACrD,cAAK,MAAwC;AACzC,oCAAwB,QAAQ,MAAM;AAAA,UAC1C;AACA,sBAAY,OAAO,IAAI;AAAA,QAC3B;AAAA,MACJ;AAGA,UAAI,YAAY,OAAO,GAAG;AACtB,sBAAc,OAAO;AAAA,MACzB;AACA,UAAI,qBAAqB,UAAU;AAC/B,cAAM,WAAW,qBAAqB;AACtC,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,mBAAS,SAAS,CAAC,GAAG,SAAS,kBAAkB,eAAe,SAAS,CAAC,CAAC;AAAA,QAC/E;AAAA,MACJ;AAGA,uBAAiB,kBAAkB;AAAA,IAKvC;AACA,WAAO,kBACD,MAAM;AAEJ,kBAAY,eAAe;AAAA,IAC/B,IACE;AAAA,EACV;AACA,WAAS,YAAY,YAAY;AAC7B,QAAI,YAAY,UAAU,GAAG;AACzB,YAAM,UAAU,WAAW,IAAI,UAAU;AACzC,UAAI,SAAS;AACT,mBAAW,OAAO,UAAU;AAC5B,iBAAS,OAAO,SAAS,QAAQ,OAAO,GAAG,CAAC;AAC5C,gBAAQ,SAAS,QAAQ,WAAW;AACpC,gBAAQ,MAAM,QAAQ,WAAW;AAAA,MACrC;AAAA,IACJ,OACK;AACD,YAAM,QAAQ,SAAS,QAAQ,UAAU;AACzC,UAAI,QAAQ,IAAI;AACZ,iBAAS,OAAO,OAAO,CAAC;AACxB,YAAI,WAAW,OAAO;AAClB,qBAAW,OAAO,WAAW,OAAO,IAAI;AAC5C,mBAAW,SAAS,QAAQ,WAAW;AACvC,mBAAW,MAAM,QAAQ,WAAW;AAAA,MACxC;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,YAAY;AACjB,WAAO;AAAA,EACX;AACA,WAAS,cAAc,SAAS;AAC5B,UAAM,QAAQ,mBAAmB,SAAS,QAAQ;AAClD,aAAS,OAAO,OAAO,GAAG,OAAO;AAEjC,QAAI,QAAQ,OAAO,QAAQ,CAAC,cAAc,OAAO;AAC7C,iBAAW,IAAI,QAAQ,OAAO,MAAM,OAAO;AAAA,EACnD;AACA,WAAS,QAAQD,WAAU,iBAAiB;AACxC,QAAI;AACJ,QAAI,SAAS,CAAC;AACd,QAAI;AACJ,QAAI;AACJ,QAAI,UAAUA,aAAYA,UAAS,MAAM;AACrC,gBAAU,WAAW,IAAIA,UAAS,IAAI;AACtC,UAAI,CAAC;AACD,cAAM,kBAAkB,GAAsC;AAAA,UAC1D,UAAAA;AAAA,QACJ,CAAC;AAEL,UAAK,MAAwC;AACzC,cAAM,gBAAgB,OAAO,KAAKA,UAAS,UAAU,CAAC,CAAC,EAAE,OAAO,eAAa,CAAC,QAAQ,KAAK,KAAK,CAAAE,OAAKA,GAAE,SAAS,SAAS,CAAC;AAC1H,YAAI,cAAc,QAAQ;AACtB,eAAK,+BAA+B,cAAc,KAAK,MAAM,CAAC,gIAAgI;AAAA,QAClM;AAAA,MACJ;AACA,aAAO,QAAQ,OAAO;AACtB,eAAS;AAAA;AAAA,QAET;AAAA,UAAmB,gBAAgB;AAAA;AAAA;AAAA,UAGnC,QAAQ,KACH,OAAO,CAAAA,OAAK,CAACA,GAAE,QAAQ,EACvB,OAAO,QAAQ,SAAS,QAAQ,OAAO,KAAK,OAAO,CAAAA,OAAKA,GAAE,QAAQ,IAAI,CAAC,CAAC,EACxE,IAAI,CAAAA,OAAKA,GAAE,IAAI;AAAA,QAAC;AAAA;AAAA;AAAA,QAGrBF,UAAS,UACL,mBAAmBA,UAAS,QAAQ,QAAQ,KAAK,IAAI,CAAAE,OAAKA,GAAE,IAAI,CAAC;AAAA,MAAC;AAEtE,aAAO,QAAQ,UAAU,MAAM;AAAA,IACnC,WACSF,UAAS,QAAQ,MAAM;AAG5B,aAAOA,UAAS;AAChB,UAA+C,CAAC,KAAK,WAAW,GAAG,GAAG;AAClE,aAAK,2DAA2D,IAAI,oDAAoD,IAAI,wHAAwH;AAAA,MACxP;AACA,gBAAU,SAAS,KAAK,OAAK,EAAE,GAAG,KAAK,IAAI,CAAC;AAE5C,UAAI,SAAS;AAET,iBAAS,QAAQ,MAAM,IAAI;AAC3B,eAAO,QAAQ,OAAO;AAAA,MAC1B;AAAA,IAEJ,OACK;AAED,gBAAU,gBAAgB,OACpB,WAAW,IAAI,gBAAgB,IAAI,IACnC,SAAS,KAAK,OAAK,EAAE,GAAG,KAAK,gBAAgB,IAAI,CAAC;AACxD,UAAI,CAAC;AACD,cAAM,kBAAkB,GAAsC;AAAA,UAC1D,UAAAA;AAAA,UACA;AAAA,QACJ,CAAC;AACL,aAAO,QAAQ,OAAO;AAGtB,eAAS,OAAO,CAAC,GAAG,gBAAgB,QAAQA,UAAS,MAAM;AAC3D,aAAO,QAAQ,UAAU,MAAM;AAAA,IACnC;AACA,UAAM,UAAU,CAAC;AACjB,QAAI,gBAAgB;AACpB,WAAO,eAAe;AAElB,cAAQ,QAAQ,cAAc,MAAM;AACpC,sBAAgB,cAAc;AAAA,IAClC;AACA,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,gBAAgB,OAAO;AAAA,IACjC;AAAA,EACJ;AAEA,SAAO,QAAQ,WAAS,SAAS,KAAK,CAAC;AACvC,WAAS,cAAc;AACnB,aAAS,SAAS;AAClB,eAAW,MAAM;AAAA,EACrB;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,SAAS,mBAAmB,QAAQ,MAAM;AACtC,QAAM,YAAY,CAAC;AACnB,aAAW,OAAO,MAAM;AACpB,QAAI,OAAO;AACP,gBAAU,GAAG,IAAI,OAAO,GAAG;AAAA,EACnC;AACA,SAAO;AACX;AAOA,SAAS,qBAAqB,QAAQ;AAClC,QAAM,aAAa;AAAA,IACf,MAAM,OAAO;AAAA,IACb,UAAU,OAAO;AAAA,IACjB,MAAM,OAAO;AAAA,IACb,MAAM,OAAO,QAAQ,CAAC;AAAA,IACtB,SAAS,OAAO;AAAA,IAChB,aAAa,OAAO;AAAA,IACpB,OAAO,qBAAqB,MAAM;AAAA,IAClC,UAAU,OAAO,YAAY,CAAC;AAAA,IAC9B,WAAW,CAAC;AAAA,IACZ,aAAa,oBAAI,IAAI;AAAA,IACrB,cAAc,oBAAI,IAAI;AAAA,IACtB,gBAAgB,CAAC;AAAA;AAAA;AAAA,IAGjB,YAAY,gBAAgB,SACtB,OAAO,cAAc,OACrB,OAAO,aAAa,EAAE,SAAS,OAAO,UAAU;AAAA,EAC1D;AAIA,SAAO,eAAe,YAAY,QAAQ;AAAA,IACtC,OAAO,CAAC;AAAA,EACZ,CAAC;AACD,SAAO;AACX;AAMA,SAAS,qBAAqB,QAAQ;AAClC,QAAM,cAAc,CAAC;AAErB,QAAM,QAAQ,OAAO,SAAS;AAC9B,MAAI,eAAe,QAAQ;AACvB,gBAAY,UAAU;AAAA,EAC1B,OACK;AAGD,eAAW,QAAQ,OAAO;AACtB,kBAAY,IAAI,IAAI,OAAO,UAAU,WAAW,MAAM,IAAI,IAAI;AAAA,EACtE;AACA,SAAO;AACX;AAKA,SAAS,cAAc,QAAQ;AAC3B,SAAO,QAAQ;AACX,QAAI,OAAO,OAAO;AACd,aAAO;AACX,aAAS,OAAO;AAAA,EACpB;AACA,SAAO;AACX;AAMA,SAAS,gBAAgB,SAAS;AAC9B,SAAO,QAAQ,OAAO,CAAC,MAAM,WAAW,OAAO,MAAM,OAAO,IAAI,GAAG,CAAC,CAAC;AACzE;AACA,SAAS,aAAa,UAAU,gBAAgB;AAC5C,QAAM,UAAU,CAAC;AACjB,aAAW,OAAO,UAAU;AACxB,YAAQ,GAAG,IAAI,OAAO,iBAAiB,eAAe,GAAG,IAAI,SAAS,GAAG;AAAA,EAC7E;AACA,SAAO;AACX;AACA,SAAS,YAAY,GAAG,GAAG;AACvB,SAAQ,EAAE,SAAS,EAAE,QACjB,EAAE,aAAa,EAAE,YACjB,EAAE,eAAe,EAAE;AAC3B;AAOA,SAAS,gBAAgB,GAAG,GAAG;AAC3B,aAAW,OAAO,EAAE,MAAM;AACtB,QAAI,CAAC,IAAI,YAAY,CAAC,EAAE,KAAK,KAAK,YAAY,KAAK,MAAM,GAAG,CAAC;AACzD,aAAO,KAAK,UAAU,EAAE,OAAO,IAAI,+BAA+B,EAAE,OAAO,IAAI,2CAA2C,IAAI,IAAI,GAAG;AAAA,EAC7I;AACA,aAAW,OAAO,EAAE,MAAM;AACtB,QAAI,CAAC,IAAI,YAAY,CAAC,EAAE,KAAK,KAAK,YAAY,KAAK,MAAM,GAAG,CAAC;AACzD,aAAO,KAAK,UAAU,EAAE,OAAO,IAAI,+BAA+B,EAAE,OAAO,IAAI,2CAA2C,IAAI,IAAI,GAAG;AAAA,EAC7I;AACJ;AAOA,SAAS,mCAAmC,sBAAsB,QAAQ;AACtE,MAAI,UACA,OAAO,OAAO,QACd,CAAC,qBAAqB,QACtB,CAAC,qBAAqB,MAAM;AAC5B,SAAK,oBAAoB,OAAO,OAAO,OAAO,IAAI,CAAC,4OAA4O;AAAA,EACnS;AACJ;AACA,SAAS,wBAAwB,QAAQ,QAAQ;AAC7C,WAAS,WAAW,QAAQ,UAAU,WAAW,SAAS,QAAQ;AAC9D,QAAI,SAAS,OAAO,SAAS,OAAO,MAAM;AACtC,YAAM,IAAI,MAAM,kBAAkB,OAAO,OAAO,IAAI,CAAC,yBAAyB,WAAW,WAAW,UAAU,YAAY,wHAAwH;AAAA,IACtP;AAAA,EACJ;AACJ;AACA,SAAS,iCAAiC,QAAQ,QAAQ;AACtD,aAAW,OAAO,OAAO,MAAM;AAC3B,QAAI,CAAC,OAAO,KAAK,KAAK,YAAY,KAAK,MAAM,GAAG,CAAC;AAC7C,aAAO,KAAK,kBAAkB,OAAO,OAAO,IAAI,2CAA2C,IAAI,IAAI,oBAAoB,OAAO,OAAO,IAAI,IAAI;AAAA,EACrJ;AACJ;AAUA,SAAS,mBAAmB,SAAS,UAAU;AAE3C,MAAI,QAAQ;AACZ,MAAI,QAAQ,SAAS;AACrB,SAAO,UAAU,OAAO;AACpB,UAAM,MAAO,QAAQ,SAAU;AAC/B,UAAM,YAAY,uBAAuB,SAAS,SAAS,GAAG,CAAC;AAC/D,QAAI,YAAY,GAAG;AACf,cAAQ;AAAA,IACZ,OACK;AACD,cAAQ,MAAM;AAAA,IAClB;AAAA,EACJ;AAEA,QAAM,oBAAoB,qBAAqB,OAAO;AACtD,MAAI,mBAAmB;AACnB,YAAQ,SAAS,YAAY,mBAAmB,QAAQ,CAAC;AACzD,QAA+C,QAAQ,GAAG;AAEtD,WAAK,2BAA2B,kBAAkB,OAAO,IAAI,iBAAiB,QAAQ,OAAO,IAAI,GAAG;AAAA,IACxG;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,qBAAqB,SAAS;AACnC,MAAI,WAAW;AACf,SAAQ,WAAW,SAAS,QAAS;AACjC,QAAI,YAAY,QAAQ,KACpB,uBAAuB,SAAS,QAAQ,MAAM,GAAG;AACjD,aAAO;AAAA,IACX;AAAA,EACJ;AACA;AACJ;AAQA,SAAS,YAAY,EAAE,OAAO,GAAG;AAC7B,SAAO,CAAC,EAAE,OAAO,QACZ,OAAO,cAAc,OAAO,KAAK,OAAO,UAAU,EAAE,UACrD,OAAO;AACf;AAWA,SAAS,WAAW,QAAQ;AACxB,QAAM,QAAQ,CAAC;AAGf,MAAI,WAAW,MAAM,WAAW;AAC5B,WAAO;AACX,QAAM,eAAe,OAAO,CAAC,MAAM;AACnC,QAAM,gBAAgB,eAAe,OAAO,MAAM,CAAC,IAAI,QAAQ,MAAM,GAAG;AACxE,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,EAAE,GAAG;AAE1C,UAAM,cAAc,aAAa,CAAC,EAAE,QAAQ,SAAS,GAAG;AAExD,UAAM,QAAQ,YAAY,QAAQ,GAAG;AACrC,UAAM,MAAM,OAAO,QAAQ,IAAI,cAAc,YAAY,MAAM,GAAG,KAAK,CAAC;AACxE,UAAM,QAAQ,QAAQ,IAAI,OAAO,OAAO,YAAY,MAAM,QAAQ,CAAC,CAAC;AACpE,QAAI,OAAO,OAAO;AAEd,UAAI,eAAe,MAAM,GAAG;AAC5B,UAAI,CAAC,QAAQ,YAAY,GAAG;AACxB,uBAAe,MAAM,GAAG,IAAI,CAAC,YAAY;AAAA,MAC7C;AACA,mBAAa,KAAK,KAAK;AAAA,IAC3B,OACK;AACD,YAAM,GAAG,IAAI;AAAA,IACjB;AAAA,EACJ;AACA,SAAO;AACX;AAUA,SAAS,eAAe,OAAO;AAC3B,MAAI,SAAS;AACb,WAAS,OAAO,OAAO;AACnB,UAAM,QAAQ,MAAM,GAAG;AACvB,UAAM,eAAe,GAAG;AACxB,QAAI,SAAS,MAAM;AAEf,UAAI,UAAU,QAAW;AACrB,mBAAW,OAAO,SAAS,MAAM,MAAM;AAAA,MAC3C;AACA;AAAA,IACJ;AAEA,UAAM,SAAS,QAAQ,KAAK,IACtB,MAAM,IAAI,OAAK,KAAK,iBAAiB,CAAC,CAAC,IACvC,CAAC,SAAS,iBAAiB,KAAK,CAAC;AACvC,WAAO,QAAQ,CAAAG,WAAS;AAGpB,UAAIA,WAAU,QAAW;AAErB,mBAAW,OAAO,SAAS,MAAM,MAAM;AACvC,YAAIA,UAAS;AACT,oBAAU,MAAMA;AAAA,MACxB;AAAA,IACJ,CAAC;AAAA,EACL;AACA,SAAO;AACX;AASA,SAAS,eAAe,OAAO;AAC3B,QAAM,kBAAkB,CAAC;AACzB,aAAW,OAAO,OAAO;AACrB,UAAM,QAAQ,MAAM,GAAG;AACvB,QAAI,UAAU,QAAW;AACrB,sBAAgB,GAAG,IAAI,QAAQ,KAAK,IAC9B,MAAM,IAAI,OAAM,KAAK,OAAO,OAAO,KAAK,CAAE,IAC1C,SAAS,OACL,QACA,KAAK;AAAA,IACnB;AAAA,EACJ;AACA,SAAO;AACX;AASA,IAAM,kBAAkB,OAAQ,OAAyC,iCAAiC,EAAE;AAO5G,IAAM,eAAe,OAAQ,OAAyC,sBAAsB,EAAE;AAO9F,IAAM,YAAY,OAAQ,OAAyC,WAAW,EAAE;AAOhF,IAAM,mBAAmB,OAAQ,OAAyC,mBAAmB,EAAE;AAO/F,IAAM,wBAAwB,OAAQ,OAAyC,yBAAyB,EAAE;AAK1G,SAAS,eAAe;AACpB,MAAI,WAAW,CAAC;AAChB,WAAS,IAAI,SAAS;AAClB,aAAS,KAAK,OAAO;AACrB,WAAO,MAAM;AACT,YAAM,IAAI,SAAS,QAAQ,OAAO;AAClC,UAAI,IAAI;AACJ,iBAAS,OAAO,GAAG,CAAC;AAAA,IAC5B;AAAA,EACJ;AACA,WAAS,QAAQ;AACb,eAAW,CAAC;AAAA,EAChB;AACA,SAAO;AAAA,IACH;AAAA,IACA,MAAM,MAAM,SAAS,MAAM;AAAA,IAC3B;AAAA,EACJ;AACJ;AAyDA,SAAS,iBAAiB,OAAO,IAAI,MAAM,QAAQ,MAAM,iBAAiB,QAAM,GAAG,GAAG;AAElF,QAAM,qBAAqB;AAAA,GAEtB,OAAO,eAAe,IAAI,IAAI,OAAO,eAAe,IAAI,KAAK,CAAC;AACnE,SAAO,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC1C,UAAM,OAAO,CAAC,UAAU;AACpB,UAAI,UAAU,OAAO;AACjB,eAAO,kBAAkB,GAAuC;AAAA,UAC5D;AAAA,UACA;AAAA,QACJ,CAAC,CAAC;AAAA,MACN,WACS,iBAAiB,OAAO;AAC7B,eAAO,KAAK;AAAA,MAChB,WACS,gBAAgB,KAAK,GAAG;AAC7B,eAAO,kBAAkB,GAA8C;AAAA,UACnE,MAAM;AAAA,UACN,IAAI;AAAA,QACR,CAAC,CAAC;AAAA,MACN,OACK;AACD,YAAI;AAAA,QAEA,OAAO,eAAe,IAAI,MAAM,sBAChC,OAAO,UAAU,YAAY;AAC7B,6BAAmB,KAAK,KAAK;AAAA,QACjC;AACA,gBAAQ;AAAA,MACZ;AAAA,IACJ;AAEA,UAAM,cAAc,eAAe,MAAM,MAAM,KAAK,UAAU,OAAO,UAAU,IAAI,GAAG,IAAI,MAAO,OAAyC,oBAAoB,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC;AACrL,QAAI,YAAY,QAAQ,QAAQ,WAAW;AAC3C,QAAI,MAAM,SAAS;AACf,kBAAY,UAAU,KAAK,IAAI;AACnC,QAA+C,MAAM,SAAS,GAAG;AAC7D,YAAM,UAAU,kDAAkD,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM,EAAE;AAAA,EAAM,MAAM,SAAS,CAAC;AAAA;AAChI,UAAI,OAAO,gBAAgB,YAAY,UAAU,aAAa;AAC1D,oBAAY,UAAU,KAAK,mBAAiB;AAExC,cAAI,CAAC,KAAK,SAAS;AACf,iBAAK,OAAO;AACZ,mBAAO,QAAQ,OAAO,IAAI,MAAM,0BAA0B,CAAC;AAAA,UAC/D;AACA,iBAAO;AAAA,QACX,CAAC;AAAA,MACL,WACS,gBAAgB,QAAW;AAEhC,YAAI,CAAC,KAAK,SAAS;AACf,eAAK,OAAO;AACZ,iBAAO,IAAI,MAAM,0BAA0B,CAAC;AAC5C;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,cAAU,MAAM,SAAO,OAAO,GAAG,CAAC;AAAA,EACtC,CAAC;AACL;AACA,SAAS,oBAAoB,MAAM,IAAI,MAAM;AACzC,MAAI,SAAS;AACb,SAAO,WAAY;AACf,QAAI,aAAa;AACb,WAAK,0FAA0F,KAAK,QAAQ,SAAS,GAAG,QAAQ,iGAAiG;AAErO,SAAK,UAAU;AACf,QAAI,WAAW;AACX,WAAK,MAAM,MAAM,SAAS;AAAA,EAClC;AACJ;AACA,SAAS,wBAAwB,SAAS,WAAW,IAAI,MAAM,iBAAiB,QAAM,GAAG,GAAG;AACxF,QAAM,SAAS,CAAC;AAChB,aAAW,UAAU,SAAS;AAC1B,QAA+C,CAAC,OAAO,cAAc,CAAC,OAAO,SAAS,QAAQ;AAC1F,WAAK,qBAAqB,OAAO,IAAI,8DACP;AAAA,IAClC;AACA,eAAW,QAAQ,OAAO,YAAY;AAClC,UAAI,eAAe,OAAO,WAAW,IAAI;AACzC,UAAK,MAAwC;AACzC,YAAI,CAAC,gBACA,OAAO,iBAAiB,YACrB,OAAO,iBAAiB,YAAa;AACzC,eAAK,cAAc,IAAI,0BAA0B,OAAO,IAAI,yCACvB,OAAO,YAAY,CAAC,IAAI;AAG7D,gBAAM,IAAI,MAAM,yBAAyB;AAAA,QAC7C,WACS,UAAU,cAAc;AAG7B,eAAK,cAAc,IAAI,0BAA0B,OAAO,IAAI,6LAI9B;AAC9B,gBAAM,UAAU;AAChB,yBAAe,MAAM;AAAA,QACzB,WACS,aAAa;AAAA,QAElB,CAAC,aAAa,qBAAqB;AACnC,uBAAa,sBAAsB;AACnC,eAAK,cAAc,IAAI,0BAA0B,OAAO,IAAI,oJAGD;AAAA,QAC/D;AAAA,MACJ;AAEA,UAAI,cAAc,sBAAsB,CAAC,OAAO,UAAU,IAAI;AAC1D;AACJ,UAAI,iBAAiB,YAAY,GAAG;AAEhC,cAAM,UAAU,aAAa,aAAa;AAC1C,cAAM,QAAQ,QAAQ,SAAS;AAC/B,iBACI,OAAO,KAAK,iBAAiB,OAAO,IAAI,MAAM,QAAQ,MAAM,cAAc,CAAC;AAAA,MACnF,OACK;AAED,YAAI,mBAAmB,aAAa;AACpC,YAA+C,EAAE,WAAW,mBAAmB;AAC3E,eAAK,cAAc,IAAI,0BAA0B,OAAO,IAAI,4LAA4L;AACxP,6BAAmB,QAAQ,QAAQ,gBAAgB;AAAA,QACvD;AACA,eAAO,KAAK,MAAM,iBAAiB,KAAK,cAAY;AAChD,cAAI,CAAC;AACD,kBAAM,IAAI,MAAM,+BAA+B,IAAI,SAAS,OAAO,IAAI,GAAG;AAC9E,gBAAM,oBAAoB,WAAW,QAAQ,IACvC,SAAS,UACT;AAEN,iBAAO,KAAK,IAAI,IAAI;AAGpB,iBAAO,WAAW,IAAI,IAAI;AAE1B,gBAAM,UAAU,kBAAkB,aAAa;AAC/C,gBAAM,QAAQ,QAAQ,SAAS;AAC/B,iBAAQ,SACJ,iBAAiB,OAAO,IAAI,MAAM,QAAQ,MAAM,cAAc,EAAE;AAAA,QACxE,CAAC,CAAC;AAAA,MACN;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAuCA,SAAS,QAAQ,OAAO;AACpB,QAAM,SAAS,OAAO,SAAS;AAC/B,QAAM,eAAe,OAAO,gBAAgB;AAC5C,MAAI,cAAc;AAClB,MAAI,aAAa;AACjB,QAAM,QAAQ,SAAS,MAAM;AACzB,UAAM,KAAK,MAAM,MAAM,EAAE;AACzB,QAAgD,CAAC,eAAe,OAAO,YAAa;AAChF,UAAI,CAAC,gBAAgB,EAAE,GAAG;AACtB,YAAI,aAAa;AACb,eAAK;AAAA,QAAmD,IAAI;AAAA,iBAAoB,YAAY;AAAA,WAAc,KAAK;AAAA,QACnH,OACK;AACD,eAAK;AAAA,QAAmD,IAAI;AAAA,WAAc,KAAK;AAAA,QACnF;AAAA,MACJ;AACA,mBAAa;AACb,oBAAc;AAAA,IAClB;AACA,WAAO,OAAO,QAAQ,EAAE;AAAA,EAC5B,CAAC;AACD,QAAM,oBAAoB,SAAS,MAAM;AACrC,UAAM,EAAE,QAAQ,IAAI,MAAM;AAC1B,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,eAAe,QAAQ,SAAS,CAAC;AACvC,UAAM,iBAAiB,aAAa;AACpC,QAAI,CAAC,gBAAgB,CAAC,eAAe;AACjC,aAAO;AACX,UAAM,QAAQ,eAAe,UAAU,kBAAkB,KAAK,MAAM,YAAY,CAAC;AACjF,QAAI,QAAQ;AACR,aAAO;AAEX,UAAM,mBAAmB,gBAAgB,QAAQ,SAAS,CAAC,CAAC;AAC5D;AAAA;AAAA,MAEA,SAAS;AAAA;AAAA;AAAA,MAIL,gBAAgB,YAAY,MAAM;AAAA,MAElC,eAAe,eAAe,SAAS,CAAC,EAAE,SAAS,mBACjD,eAAe,UAAU,kBAAkB,KAAK,MAAM,QAAQ,SAAS,CAAC,CAAC,CAAC,IAC1E;AAAA;AAAA,EACV,CAAC;AACD,QAAM,WAAW,SAAS,MAAM,kBAAkB,QAAQ,MACtD,eAAe,aAAa,QAAQ,MAAM,MAAM,MAAM,CAAC;AAC3D,QAAM,gBAAgB,SAAS,MAAM,kBAAkB,QAAQ,MAC3D,kBAAkB,UAAU,aAAa,QAAQ,SAAS,KAC1D,0BAA0B,aAAa,QAAQ,MAAM,MAAM,MAAM,CAAC;AACtE,WAAS,SAAS,IAAI,CAAC,GAAG;AACtB,QAAI,WAAW,CAAC,GAAG;AACf,YAAMC,KAAI,OAAO,MAAM,MAAM,OAAO,IAAI,YAAY,MAAM;AAAA,QAAE,MAAM,MAAM,EAAE;AAAA;AAAA,MAE1E,EAAE,MAAM,IAAI;AACZ,UAAI,MAAM,kBACN,OAAO,aAAa,eACpB,yBAAyB,UAAU;AACnC,iBAAS,oBAAoB,MAAMA,EAAC;AAAA,MACxC;AACA,aAAOA;AAAA,IACX;AACA,WAAO,QAAQ,QAAQ;AAAA,EAC3B;AAEA,MAA0E,WAAW;AACjF,UAAM,WAAW,mBAAmB;AACpC,QAAI,UAAU;AACV,YAAM,sBAAsB;AAAA,QACxB,OAAO,MAAM;AAAA,QACb,UAAU,SAAS;AAAA,QACnB,eAAe,cAAc;AAAA,QAC7B,OAAO;AAAA,MACX;AAEA,eAAS,iBAAiB,SAAS,kBAAkB,CAAC;AAEtD,eAAS,eAAe,KAAK,mBAAmB;AAChD,kBAAY,MAAM;AACd,4BAAoB,QAAQ,MAAM;AAClC,4BAAoB,WAAW,SAAS;AACxC,4BAAoB,gBAAgB,cAAc;AAClD,4BAAoB,QAAQ,gBAAgB,MAAM,MAAM,EAAE,CAAC,IACrD,OACA;AAAA,MACV,GAAG,EAAE,OAAO,OAAO,CAAC;AAAA,IACxB;AAAA,EACJ;AAIA,SAAO;AAAA,IACH;AAAA,IACA,MAAM,SAAS,MAAM,MAAM,MAAM,IAAI;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,SAAS,kBAAkB,QAAQ;AAC/B,SAAO,OAAO,WAAW,IAAI,OAAO,CAAC,IAAI;AAC7C;AACA,IAAM,iBAA+B,gBAAgB;AAAA,EACjD,MAAM;AAAA,EACN,cAAc,EAAE,MAAM,EAAE;AAAA,EACxB,OAAO;AAAA,IACH,IAAI;AAAA,MACA,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,UAAU;AAAA,IACd;AAAA,IACA,SAAS;AAAA,IACT,aAAa;AAAA;AAAA,IAEb,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,kBAAkB;AAAA,MACd,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,gBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA,MAAM,OAAO,EAAE,MAAM,GAAG;AACpB,UAAM,OAAO,SAAS,QAAQ,KAAK,CAAC;AACpC,UAAM,EAAE,QAAQ,IAAI,OAAO,SAAS;AACpC,UAAM,UAAU,SAAS,OAAO;AAAA,MAC5B,CAAC,aAAa,MAAM,aAAa,QAAQ,iBAAiB,oBAAoB,CAAC,GAAG,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMvF,CAAC,aAAa,MAAM,kBAAkB,QAAQ,sBAAsB,0BAA0B,CAAC,GAAG,KAAK;AAAA,IAC3G,EAAE;AACF,WAAO,MAAM;AACT,YAAM,WAAW,MAAM,WAAW,kBAAkB,MAAM,QAAQ,IAAI,CAAC;AACvE,aAAO,MAAM,SACP,WACA,EAAE,KAAK;AAAA,QACL,gBAAgB,KAAK,gBACf,MAAM,mBACN;AAAA,QACN,MAAM,KAAK;AAAA;AAAA;AAAA,QAGX,SAAS,KAAK;AAAA,QACd,OAAO,QAAQ;AAAA,MACnB,GAAG,QAAQ;AAAA,IACnB;AAAA,EACJ;AACJ,CAAC;AAMD,IAAM,aAAa;AACnB,SAAS,WAAW,GAAG;AAEnB,MAAI,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE;AACxC;AAEJ,MAAI,EAAE;AACF;AAEJ,MAAI,EAAE,WAAW,UAAa,EAAE,WAAW;AACvC;AAGJ,MAAI,EAAE,iBAAiB,EAAE,cAAc,cAAc;AAEjD,UAAM,SAAS,EAAE,cAAc,aAAa,QAAQ;AACpD,QAAI,cAAc,KAAK,MAAM;AACzB;AAAA,EACR;AAEA,MAAI,EAAE;AACF,MAAE,eAAe;AACrB,SAAO;AACX;AACA,SAAS,eAAe,OAAO,OAAO;AAClC,aAAW,OAAO,OAAO;AACrB,UAAM,aAAa,MAAM,GAAG;AAC5B,UAAM,aAAa,MAAM,GAAG;AAC5B,QAAI,OAAO,eAAe,UAAU;AAChC,UAAI,eAAe;AACf,eAAO;AAAA,IACf,OACK;AACD,UAAI,CAAC,QAAQ,UAAU,KACnB,WAAW,WAAW,WAAW,UACjC,WAAW,KAAK,CAAC,OAAO,MAAM,UAAU,WAAW,CAAC,CAAC;AACrD,eAAO;AAAA,IACf;AAAA,EACJ;AACA,SAAO;AACX;AAKA,SAAS,gBAAgB,QAAQ;AAC7B,SAAO,SAAU,OAAO,UAAU,OAAO,QAAQ,OAAO,OAAO,OAAQ;AAC3E;AAOA,IAAM,eAAe,CAAC,WAAW,aAAa,iBAAiB,aAAa,OACtE,YACA,eAAe,OACX,cACA;AAEV,IAAM,iBAA+B,gBAAgB;AAAA,EACjD,MAAM;AAAA;AAAA,EAEN,cAAc;AAAA,EACd,OAAO;AAAA,IACH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS;AAAA,IACb;AAAA,IACA,OAAO;AAAA,EACX;AAAA;AAAA;AAAA,EAGA,cAAc,EAAE,MAAM,EAAE;AAAA,EACxB,MAAM,OAAO,EAAE,OAAO,MAAM,GAAG;AAC3B,IAA2C,oBAAoB;AAC/D,UAAM,gBAAgB,OAAO,qBAAqB;AAClD,UAAM,iBAAiB,SAAS,MAAM,MAAM,SAAS,cAAc,KAAK;AACxE,UAAM,gBAAgB,OAAO,cAAc,CAAC;AAG5C,UAAM,QAAQ,SAAS,MAAM;AACzB,UAAI,eAAe,MAAM,aAAa;AACtC,YAAM,EAAE,QAAQ,IAAI,eAAe;AACnC,UAAI;AACJ,cAAQ,eAAe,QAAQ,YAAY,MACvC,CAAC,aAAa,YAAY;AAC1B;AAAA,MACJ;AACA,aAAO;AAAA,IACX,CAAC;AACD,UAAM,kBAAkB,SAAS,MAAM,eAAe,MAAM,QAAQ,MAAM,KAAK,CAAC;AAChF,YAAQ,cAAc,SAAS,MAAM,MAAM,QAAQ,CAAC,CAAC;AACrD,YAAQ,iBAAiB,eAAe;AACxC,YAAQ,uBAAuB,cAAc;AAC7C,UAAM,UAAU,IAAI;AAGpB,UAAM,MAAM,CAAC,QAAQ,OAAO,gBAAgB,OAAO,MAAM,IAAI,GAAG,CAAC,CAAC,UAAU,IAAI,IAAI,GAAG,CAAC,aAAa,MAAM,OAAO,MAAM;AAEpH,UAAI,IAAI;AAGJ,WAAG,UAAU,IAAI,IAAI;AAOrB,YAAI,QAAQ,SAAS,MAAM,YAAY,aAAa,aAAa;AAC7D,cAAI,CAAC,GAAG,YAAY,MAAM;AACtB,eAAG,cAAc,KAAK;AAAA,UAC1B;AACA,cAAI,CAAC,GAAG,aAAa,MAAM;AACvB,eAAG,eAAe,KAAK;AAAA,UAC3B;AAAA,QACJ;AAAA,MACJ;AAEA,UAAI,YACA;AAAA;AAAA,OAGC,CAAC,QAAQ,CAAC,kBAAkB,IAAI,IAAI,KAAK,CAAC,cAAc;AACzD,SAAC,GAAG,eAAe,IAAI,KAAK,CAAC,GAAG,QAAQ,cAAY,SAAS,QAAQ,CAAC;AAAA,MAC1E;AAAA,IACJ,GAAG,EAAE,OAAO,OAAO,CAAC;AACpB,WAAO,MAAM;AACT,YAAM,QAAQ,eAAe;AAG7B,YAAM,cAAc,MAAM;AAC1B,YAAM,eAAe,gBAAgB;AACrC,YAAM,gBAAgB,gBAAgB,aAAa,WAAW,WAAW;AACzE,UAAI,CAAC,eAAe;AAChB,eAAO,cAAc,MAAM,SAAS,EAAE,WAAW,eAAe,MAAM,CAAC;AAAA,MAC3E;AAEA,YAAM,mBAAmB,aAAa,MAAM,WAAW;AACvD,YAAM,aAAa,mBACb,qBAAqB,OACjB,MAAM,SACN,OAAO,qBAAqB,aACxB,iBAAiB,KAAK,IACtB,mBACR;AACN,YAAM,mBAAmB,WAAS;AAE9B,YAAI,MAAM,UAAU,aAAa;AAC7B,uBAAa,UAAU,WAAW,IAAI;AAAA,QAC1C;AAAA,MACJ;AACA,YAAM,YAAY,EAAE,eAAe,OAAO,CAAC,GAAG,YAAY,OAAO;AAAA,QAC7D;AAAA,QACA,KAAK;AAAA,MACT,CAAC,CAAC;AACF,UACI,aACA,UAAU,KAAK;AAEf,cAAM,OAAO;AAAA,UACT,OAAO,MAAM;AAAA,UACb,MAAM,aAAa;AAAA,UACnB,MAAM,aAAa;AAAA,UACnB,MAAM,aAAa;AAAA,QACvB;AACA,cAAM,oBAAoB,QAAQ,UAAU,GAAG,IACzC,UAAU,IAAI,IAAI,OAAK,EAAE,CAAC,IAC1B,CAAC,UAAU,IAAI,CAAC;AACtB,0BAAkB,QAAQ,cAAY;AAElC,mBAAS,iBAAiB;AAAA,QAC9B,CAAC;AAAA,MACL;AACA;AAAA;AAAA;AAAA,QAGA,cAAc,MAAM,SAAS,EAAE,WAAW,WAAW,MAAM,CAAC,KACxD;AAAA;AAAA,IACR;AAAA,EACJ;AACJ,CAAC;AACD,SAAS,cAAc,MAAM,MAAM;AAC/B,MAAI,CAAC;AACD,WAAO;AACX,QAAM,cAAc,KAAK,IAAI;AAC7B,SAAO,YAAY,WAAW,IAAI,YAAY,CAAC,IAAI;AACvD;AAMA,IAAM,aAAa;AAGnB,SAAS,sBAAsB;AAC3B,QAAM,WAAW,mBAAmB;AACpC,QAAM,aAAa,SAAS,UAAU,SAAS,OAAO,KAAK;AAC3D,QAAM,oBAAoB,SAAS,UAAU,SAAS,OAAO,WAAW,SAAS,OAAO,QAAQ;AAChG,MAAI,eACC,eAAe,eAAe,WAAW,SAAS,YAAY,MAC/D,OAAO,sBAAsB,YAC7B,kBAAkB,SAAS,cAAc;AACzC,UAAM,OAAO,eAAe,cAAc,eAAe;AACzD,SAAK;AAAA;AAAA;AAAA;AAAA,KAGK,IAAI;AAAA;AAAA,MAEH,IAAI;AAAA,eACK;AAAA,EACxB;AACJ;AASA,SAAS,oBAAoB,eAAe,SAAS;AACjD,QAAM,OAAO,OAAO,CAAC,GAAG,eAAe;AAAA;AAAA,IAEnC,SAAS,cAAc,QAAQ,IAAI,aAAW,KAAK,SAAS,CAAC,aAAa,YAAY,SAAS,CAAC,CAAC;AAAA,EACrG,CAAC;AACD,SAAO;AAAA,IACH,SAAS;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS,cAAc;AAAA,MACvB;AAAA,MACA,OAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,SAAS,cAAc,SAAS;AAC5B,SAAO;AAAA,IACH,SAAS;AAAA,MACL;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,IAAI,WAAW;AACf,SAAS,YAAY,KAAK,QAAQ,SAAS;AAGvC,MAAI,OAAO;AACP;AACJ,SAAO,gBAAgB;AAEvB,QAAM,KAAK;AACX,sBAAoB;AAAA,IAChB,IAAI,sBAAsB,KAAK,MAAM,KAAK;AAAA,IAC1C,OAAO;AAAA,IACP,aAAa;AAAA,IACb,UAAU;AAAA,IACV,MAAM;AAAA,IACN,qBAAqB,CAAC,SAAS;AAAA,IAC/B;AAAA,EACJ,GAAG,SAAO;AACN,QAAI,OAAO,IAAI,QAAQ,YAAY;AAC/B,cAAQ,KAAK,uNAAuN;AAAA,IACxO;AAEA,QAAI,GAAG,iBAAiB,CAAC,SAAS,QAAQ;AACtC,UAAI,QAAQ,cAAc;AACtB,gBAAQ,aAAa,MAAM,KAAK;AAAA,UAC5B,MAAM;AAAA,UACN,KAAK;AAAA,UACL,UAAU;AAAA,UACV,OAAO,oBAAoB,OAAO,aAAa,OAAO,eAAe;AAAA,QACzE,CAAC;AAAA,MACL;AAAA,IACJ,CAAC;AAED,QAAI,GAAG,mBAAmB,CAAC,EAAE,UAAU,MAAM,kBAAkB,MAAM;AACjE,UAAI,kBAAkB,gBAAgB;AAClC,cAAM,OAAO,kBAAkB;AAC/B,aAAK,KAAK,KAAK;AAAA,UACX,QAAQ,KAAK,OAAO,GAAG,KAAK,KAAK,SAAS,CAAC,OAAO,MAAM,KAAK;AAAA,UAC7D,WAAW;AAAA,UACX,SAAS;AAAA,UACT,iBAAiB;AAAA,QACrB,CAAC;AAAA,MACL;AAEA,UAAI,QAAQ,kBAAkB,cAAc,GAAG;AAC3C,0BAAkB,gBAAgB;AAClC,0BAAkB,eAAe,QAAQ,kBAAgB;AACrD,cAAI,QAAQ,aAAa,MAAM;AAC/B,cAAI,kBAAkB;AACtB,cAAI,UAAU;AACd,cAAI,YAAY;AAChB,cAAI,aAAa,OAAO;AACpB,oBAAQ,aAAa;AACrB,8BAAkB;AAClB,wBAAY;AAAA,UAChB,WACS,aAAa,eAAe;AACjC,8BAAkB;AAClB,sBAAU;AAAA,UACd,WACS,aAAa,UAAU;AAC5B,8BAAkB;AAClB,sBAAU;AAAA,UACd;AACA,eAAK,KAAK,KAAK;AAAA,YACX;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACJ,CAAC;AAAA,QACL,CAAC;AAAA,MACL;AAAA,IACJ,CAAC;AACD,UAAM,OAAO,cAAc,MAAM;AAE7B,wBAAkB;AAClB,UAAI,sBAAsB;AAC1B,UAAI,kBAAkB,iBAAiB;AACvC,UAAI,mBAAmB,iBAAiB;AAAA,IAC5C,CAAC;AACD,UAAM,qBAAqB,wBAAwB;AACnD,QAAI,iBAAiB;AAAA,MACjB,IAAI;AAAA,MACJ,OAAO,SAAS,KAAK,MAAM,KAAK,EAAE;AAAA,MAClC,OAAO;AAAA,IACX,CAAC;AAOD,WAAO,QAAQ,CAAC,OAAO,OAAO;AAC1B,UAAI,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,OAAO;AAAA,UACH,OAAO;AAAA,UACP,UAAU,GAAG;AAAA,UACb,SAAS;AAAA,UACT,MAAM,IAAI,IAAI;AAAA,UACd,MAAM,EAAE,MAAM;AAAA,UACd,SAAS,GAAG,KAAK;AAAA,QACrB;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAED,QAAI,eAAe;AACnB,WAAO,WAAW,CAAC,IAAI,SAAS;AAC5B,YAAM,OAAO;AAAA,QACT,OAAO,cAAc,YAAY;AAAA,QACjC,MAAM,oBAAoB,MAAM,yCAAyC;AAAA,QACzE,IAAI,oBAAoB,IAAI,iBAAiB;AAAA,MACjD;AAEA,aAAO,eAAe,GAAG,MAAM,kBAAkB;AAAA,QAC7C,OAAO;AAAA,MACX,CAAC;AACD,UAAI,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,OAAO;AAAA,UACH,MAAM,IAAI,IAAI;AAAA,UACd,OAAO;AAAA,UACP,UAAU,GAAG;AAAA,UACb;AAAA,UACA,SAAS,GAAG,KAAK;AAAA,QACrB;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AACD,WAAO,UAAU,CAAC,IAAI,MAAM,YAAY;AACpC,YAAM,OAAO;AAAA,QACT,OAAO,cAAc,WAAW;AAAA,MACpC;AACA,UAAI,SAAS;AACT,aAAK,UAAU;AAAA,UACX,SAAS;AAAA,YACL,MAAM;AAAA,YACN,UAAU;AAAA,YACV,SAAS,UAAU,QAAQ,UAAU;AAAA,YACrC,SAAS;AAAA,YACT,OAAO;AAAA,UACX;AAAA,QACJ;AACA,aAAK,SAAS,cAAc,GAAG;AAAA,MACnC,OACK;AACD,aAAK,SAAS,cAAc,GAAG;AAAA,MACnC;AAEA,WAAK,OAAO,oBAAoB,MAAM,yCAAyC;AAC/E,WAAK,KAAK,oBAAoB,IAAI,iBAAiB;AACnD,UAAI,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,OAAO;AAAA,UACH,OAAO;AAAA,UACP,UAAU,GAAG;AAAA,UACb,MAAM,IAAI,IAAI;AAAA,UACd;AAAA,UACA,SAAS,UAAU,YAAY;AAAA,UAC/B,SAAS,GAAG,KAAK;AAAA,QACrB;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAID,UAAM,oBAAoB,sBAAsB;AAChD,QAAI,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO,YAAY,KAAK,MAAM,KAAK;AAAA,MACnC,MAAM;AAAA,MACN,uBAAuB;AAAA,IAC3B,CAAC;AACD,aAAS,oBAAoB;AAEzB,UAAI,CAAC;AACD;AACJ,YAAM,UAAU;AAEhB,UAAI,SAAS,QAAQ,UAAU,EAAE,OAAO,WAAS,CAAC,MAAM;AAAA;AAAA,MAGpD,CAAC,MAAM,OAAO,OAAO,UAAU;AAEnC,aAAO,QAAQ,4BAA4B;AAE3C,UAAI,QAAQ,QAAQ;AAChB,iBAAS,OAAO,OAAO;AAAA;AAAA,UAEvB,gBAAgB,OAAO,QAAQ,OAAO,YAAY,CAAC;AAAA,SAAC;AAAA,MACxD;AAEA,aAAO,QAAQ,WAAS,sBAAsB,OAAO,OAAO,aAAa,KAAK,CAAC;AAC/E,cAAQ,YAAY,OAAO,IAAI,6BAA6B;AAAA,IAChE;AACA,QAAI;AACJ,QAAI,GAAG,iBAAiB,aAAW;AAC/B,4BAAsB;AACtB,UAAI,QAAQ,QAAQ,OAAO,QAAQ,gBAAgB,mBAAmB;AAClE,0BAAkB;AAAA,MACtB;AAAA,IACJ,CAAC;AAID,QAAI,GAAG,kBAAkB,aAAW;AAChC,UAAI,QAAQ,QAAQ,OAAO,QAAQ,gBAAgB,mBAAmB;AAClE,cAAM,SAAS,QAAQ,UAAU;AACjC,cAAM,QAAQ,OAAO,KAAK,CAAAC,WAASA,OAAM,OAAO,YAAY,QAAQ,MAAM;AAC1E,YAAI,OAAO;AACP,kBAAQ,QAAQ;AAAA,YACZ,SAAS,0CAA0C,KAAK;AAAA,UAC5D;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,QAAI,kBAAkB,iBAAiB;AACvC,QAAI,mBAAmB,iBAAiB;AAAA,EAC5C,CAAC;AACL;AACA,SAAS,eAAe,KAAK;AACzB,MAAI,IAAI,UAAU;AACd,WAAO,IAAI,aAAa,MAAM;AAAA,EAClC,OACK;AACD,WAAO,IAAI,aAAa,MAAM;AAAA,EAClC;AACJ;AACA,SAAS,0CAA0C,OAAO;AACtD,QAAM,EAAE,OAAO,IAAI;AACnB,QAAM,SAAS;AAAA,IACX,EAAE,UAAU,OAAO,KAAK,QAAQ,OAAO,OAAO,KAAK;AAAA,EACvD;AACA,MAAI,OAAO,QAAQ,MAAM;AACrB,WAAO,KAAK;AAAA,MACR,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO,OAAO;AAAA,IAClB,CAAC;AAAA,EACL;AACA,SAAO,KAAK,EAAE,UAAU,OAAO,KAAK,UAAU,OAAO,MAAM,GAAG,CAAC;AAC/D,MAAI,MAAM,KAAK,QAAQ;AACnB,WAAO,KAAK;AAAA,MACR,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,QACH,SAAS;AAAA,UACL,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS,MAAM,KACV,IAAI,SAAO,GAAG,IAAI,IAAI,GAAG,eAAe,GAAG,CAAC,EAAE,EAC9C,KAAK,GAAG;AAAA,UACb,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,QACjB;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AACA,MAAI,OAAO,YAAY,MAAM;AACzB,WAAO,KAAK;AAAA,MACR,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO,OAAO;AAAA,IAClB,CAAC;AAAA,EACL;AACA,MAAI,MAAM,MAAM,QAAQ;AACpB,WAAO,KAAK;AAAA,MACR,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO,MAAM,MAAM,IAAI,WAAS,MAAM,OAAO,IAAI;AAAA,IACrD,CAAC;AAAA,EACL;AACA,MAAI,OAAO,KAAK,MAAM,OAAO,IAAI,EAAE,QAAQ;AACvC,WAAO,KAAK;AAAA,MACR,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO,MAAM,OAAO;AAAA,IACxB,CAAC;AAAA,EACL;AACA,SAAO,KAAK;AAAA,IACR,KAAK;AAAA,IACL,UAAU;AAAA,IACV,OAAO;AAAA,MACH,SAAS;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS,MAAM,MAAM,IAAI,WAAS,MAAM,KAAK,IAAI,CAAC,EAAE,KAAK,KAAK;AAAA,QAC9D,SAAS;AAAA,QACT,OAAO,MAAM;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAIA,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,aAAa;AAEnB,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,SAAS,8BAA8B,OAAO;AAC1C,QAAM,OAAO,CAAC;AACd,QAAM,EAAE,OAAO,IAAI;AACnB,MAAI,OAAO,QAAQ,MAAM;AACrB,SAAK,KAAK;AAAA,MACN,OAAO,OAAO,OAAO,IAAI;AAAA,MACzB,WAAW;AAAA,MACX,iBAAiB;AAAA,IACrB,CAAC;AAAA,EACL;AACA,MAAI,OAAO,SAAS;AAChB,SAAK,KAAK;AAAA,MACN,OAAO;AAAA,MACP,WAAW;AAAA,MACX,iBAAiB;AAAA,IACrB,CAAC;AAAA,EACL;AACA,MAAI,MAAM,YAAY;AAClB,SAAK,KAAK;AAAA,MACN,OAAO;AAAA,MACP,WAAW;AAAA,MACX,iBAAiB;AAAA,IACrB,CAAC;AAAA,EACL;AACA,MAAI,MAAM,kBAAkB;AACxB,SAAK,KAAK;AAAA,MACN,OAAO;AAAA,MACP,WAAW;AAAA,MACX,iBAAiB;AAAA,IACrB,CAAC;AAAA,EACL;AACA,MAAI,MAAM,aAAa;AACnB,SAAK,KAAK;AAAA,MACN,OAAO;AAAA,MACP,WAAW;AAAA,MACX,iBAAiB;AAAA,IACrB,CAAC;AAAA,EACL;AACA,MAAI,OAAO,UAAU;AACjB,SAAK,KAAK;AAAA,MACN,OAAO,OAAO,OAAO,aAAa,WAC5B,aAAa,OAAO,QAAQ,KAC5B;AAAA,MACN,WAAW;AAAA,MACX,iBAAiB;AAAA,IACrB,CAAC;AAAA,EACL;AAGA,MAAI,KAAK,OAAO;AAChB,MAAI,MAAM,MAAM;AACZ,SAAK,OAAO,eAAe;AAC3B,WAAO,UAAU;AAAA,EACrB;AACA,SAAO;AAAA,IACH;AAAA,IACA,OAAO,OAAO;AAAA,IACd;AAAA,IACA,UAAU,MAAM,SAAS,IAAI,6BAA6B;AAAA,EAC9D;AACJ;AAEA,IAAI,gBAAgB;AACpB,IAAM,oBAAoB;AAC1B,SAAS,sBAAsB,OAAO,cAAc;AAGhD,QAAM,gBAAgB,aAAa,QAAQ,UACvC,kBAAkB,aAAa,QAAQ,aAAa,QAAQ,SAAS,CAAC,GAAG,MAAM,MAAM;AACzF,QAAM,mBAAmB,MAAM,cAAc;AAC7C,MAAI,CAAC,eAAe;AAChB,UAAM,cAAc,aAAa,QAAQ,KAAK,WAAS,kBAAkB,OAAO,MAAM,MAAM,CAAC;AAAA,EACjG;AACA,QAAM,SAAS,QAAQ,gBAAc,sBAAsB,YAAY,YAAY,CAAC;AACxF;AACA,SAAS,6BAA6B,OAAO;AACzC,QAAM,aAAa;AACnB,QAAM,SAAS,QAAQ,4BAA4B;AACvD;AACA,SAAS,gBAAgB,OAAO,QAAQ;AACpC,QAAM,QAAQ,OAAO,MAAM,EAAE,EAAE,MAAM,iBAAiB;AACtD,QAAM,aAAa;AACnB,MAAI,CAAC,SAAS,MAAM,SAAS,GAAG;AAC5B,WAAO;AAAA,EACX;AAEA,QAAM,cAAc,IAAI,OAAO,MAAM,CAAC,EAAE,QAAQ,OAAO,EAAE,GAAG,MAAM,CAAC,CAAC;AACpE,MAAI,YAAY,KAAK,MAAM,GAAG;AAE1B,UAAM,SAAS,QAAQ,WAAS,gBAAgB,OAAO,MAAM,CAAC;AAE9D,QAAI,MAAM,OAAO,SAAS,OAAO,WAAW,KAAK;AAC7C,YAAM,aAAa,MAAM,GAAG,KAAK,MAAM;AACvC,aAAO;AAAA,IACX;AAEA,WAAO;AAAA,EACX;AACA,QAAM,OAAO,MAAM,OAAO,KAAK,YAAY;AAC3C,QAAM,cAAc,OAAO,IAAI;AAE/B,MAAI,CAAC,OAAO,WAAW,GAAG,MACrB,YAAY,SAAS,MAAM,KAAK,KAAK,SAAS,MAAM;AACrD,WAAO;AACX,MAAI,YAAY,WAAW,MAAM,KAAK,KAAK,WAAW,MAAM;AACxD,WAAO;AACX,MAAI,MAAM,OAAO,QAAQ,OAAO,MAAM,OAAO,IAAI,EAAE,SAAS,MAAM;AAC9D,WAAO;AACX,SAAO,MAAM,SAAS,KAAK,WAAS,gBAAgB,OAAO,MAAM,CAAC;AACtE;AACA,SAAS,KAAK,KAAK,MAAM;AACrB,QAAM,MAAM,CAAC;AACb,aAAW,OAAO,KAAK;AACnB,QAAI,CAAC,KAAK,SAAS,GAAG,GAAG;AAErB,UAAI,GAAG,IAAI,IAAI,GAAG;AAAA,IACtB;AAAA,EACJ;AACA,SAAO;AACX;AAOA,SAAS,aAAa,SAAS;AAC3B,QAAM,UAAU,oBAAoB,QAAQ,QAAQ,OAAO;AAC3D,QAAM,eAAe,QAAQ,cAAc;AAC3C,QAAM,mBAAmB,QAAQ,kBAAkB;AACnD,QAAM,gBAAgB,QAAQ;AAC9B,MAA+C,CAAC;AAC5C,UAAM,IAAI,MAAM,gIACyD;AAC7E,QAAM,eAAe,aAAa;AAClC,QAAM,sBAAsB,aAAa;AACzC,QAAM,cAAc,aAAa;AACjC,QAAM,eAAe,WAAW,yBAAyB;AACzD,MAAI,kBAAkB;AAEtB,MAAI,aAAa,QAAQ,kBAAkB,uBAAuB,SAAS;AACvE,YAAQ,oBAAoB;AAAA,EAChC;AACA,QAAM,kBAAkB,cAAc,KAAK,MAAM,gBAAc,KAAK,UAAU;AAC9E,QAAM,eAAe,cAAc,KAAK,MAAM,WAAW;AACzD,QAAM;AAAA;AAAA,IAEN,cAAc,KAAK,MAAM,MAAM;AAAA;AAC/B,WAAS,SAAS,eAAe,OAAO;AACpC,QAAI;AACJ,QAAI;AACJ,QAAI,YAAY,aAAa,GAAG;AAC5B,eAAS,QAAQ,iBAAiB,aAAa;AAC/C,UAA+C,CAAC,QAAQ;AACpD,aAAK,iBAAiB,OAAO,aAAa,CAAC,uCAAuC,KAAK;AAAA,MAC3F;AACA,eAAS;AAAA,IACb,OACK;AACD,eAAS;AAAA,IACb;AACA,WAAO,QAAQ,SAAS,QAAQ,MAAM;AAAA,EAC1C;AACA,WAAS,YAAY,MAAM;AACvB,UAAM,gBAAgB,QAAQ,iBAAiB,IAAI;AACnD,QAAI,eAAe;AACf,cAAQ,YAAY,aAAa;AAAA,IACrC,WACU,MAAwC;AAC9C,WAAK,qCAAqC,OAAO,IAAI,CAAC,GAAG;AAAA,IAC7D;AAAA,EACJ;AACA,WAAS,YAAY;AACjB,WAAO,QAAQ,UAAU,EAAE,IAAI,kBAAgB,aAAa,MAAM;AAAA,EACtE;AACA,WAAS,SAAS,MAAM;AACpB,WAAO,CAAC,CAAC,QAAQ,iBAAiB,IAAI;AAAA,EAC1C;AACA,WAAS,QAAQ,aAAa,iBAAiB;AAI3C,sBAAkB,OAAO,CAAC,GAAG,mBAAmB,aAAa,KAAK;AAClE,QAAI,OAAO,gBAAgB,UAAU;AACjC,YAAM,qBAAqB,SAAS,cAAc,aAAa,gBAAgB,IAAI;AACnF,YAAMC,gBAAe,QAAQ,QAAQ,EAAE,MAAM,mBAAmB,KAAK,GAAG,eAAe;AACvF,YAAMC,QAAO,cAAc,WAAW,mBAAmB,QAAQ;AACjE,UAAK,MAAwC;AACzC,YAAIA,MAAK,WAAW,IAAI;AACpB,eAAK,aAAa,WAAW,kBAAkBA,KAAI,4DAA4D;AAAA,iBAC1G,CAACD,cAAa,QAAQ,QAAQ;AACnC,eAAK,0CAA0C,WAAW,GAAG;AAAA,QACjE;AAAA,MACJ;AAEA,aAAO,OAAO,oBAAoBA,eAAc;AAAA,QAC5C,QAAQ,aAAaA,cAAa,MAAM;AAAA,QACxC,MAAM,OAAO,mBAAmB,IAAI;AAAA,QACpC,gBAAgB;AAAA,QAChB,MAAAC;AAAA,MACJ,CAAC;AAAA,IACL;AACA,QAA+C,CAAC,gBAAgB,WAAW,GAAG;AAC1E,WAAK;AAAA,cAA+F,WAAW;AAC/G,aAAO,QAAQ,CAAC,CAAC;AAAA,IACrB;AACA,QAAI;AAEJ,QAAI,YAAY,QAAQ,MAAM;AAC1B,UACI,YAAY,eACZ,EAAE,UAAU;AAAA,MAEZ,OAAO,KAAK,YAAY,MAAM,EAAE,QAAQ;AACxC,aAAK,SAAS,YAAY,IAAI,gGAAgG;AAAA,MAClI;AACA,wBAAkB,OAAO,CAAC,GAAG,aAAa;AAAA,QACtC,MAAM,SAAS,cAAc,YAAY,MAAM,gBAAgB,IAAI,EAAE;AAAA,MACzE,CAAC;AAAA,IACL,OACK;AAED,YAAM,eAAe,OAAO,CAAC,GAAG,YAAY,MAAM;AAClD,iBAAW,OAAO,cAAc;AAC5B,YAAI,aAAa,GAAG,KAAK,MAAM;AAC3B,iBAAO,aAAa,GAAG;AAAA,QAC3B;AAAA,MACJ;AAEA,wBAAkB,OAAO,CAAC,GAAG,aAAa;AAAA,QACtC,QAAQ,aAAa,YAAY;AAAA,MACrC,CAAC;AAGD,sBAAgB,SAAS,aAAa,gBAAgB,MAAM;AAAA,IAChE;AACA,UAAM,eAAe,QAAQ,QAAQ,iBAAiB,eAAe;AACrE,UAAM,OAAO,YAAY,QAAQ;AACjC,QAA+C,QAAQ,CAAC,KAAK,WAAW,GAAG,GAAG;AAC1E,WAAK,mEAAmE,IAAI,YAAY,IAAI,IAAI;AAAA,IACpG;AAGA,iBAAa,SAAS,gBAAgB,aAAa,aAAa,MAAM,CAAC;AACvE,UAAM,WAAW,aAAa,kBAAkB,OAAO,CAAC,GAAG,aAAa;AAAA,MACpE,MAAM,WAAW,IAAI;AAAA,MACrB,MAAM,aAAa;AAAA,IACvB,CAAC,CAAC;AACF,UAAM,OAAO,cAAc,WAAW,QAAQ;AAC9C,QAAK,MAAwC;AACzC,UAAI,KAAK,WAAW,IAAI,GAAG;AACvB,aAAK,aAAa,WAAW,kBAAkB,IAAI,4DAA4D;AAAA,MACnH,WACS,CAAC,aAAa,QAAQ,QAAQ;AACnC,aAAK,0CAA0C,YAAY,QAAQ,OAAO,YAAY,OAAO,WAAW,GAAG;AAAA,MAC/G;AAAA,IACJ;AACA,WAAO,OAAO;AAAA,MACV;AAAA;AAAA;AAAA,MAGA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMA,qBAAqB,iBACf,eAAe,YAAY,KAAK,IAC/B,YAAY,SAAS,CAAC;AAAA;AAAA,IACjC,GAAG,cAAc;AAAA,MACb,gBAAgB;AAAA,MAChB;AAAA,IACJ,CAAC;AAAA,EACL;AACA,WAAS,iBAAiB,IAAI;AAC1B,WAAO,OAAO,OAAO,WACf,SAAS,cAAc,IAAI,aAAa,MAAM,IAAI,IAClD,OAAO,CAAC,GAAG,EAAE;AAAA,EACvB;AACA,WAAS,wBAAwB,IAAI,MAAM;AACvC,QAAI,oBAAoB,IAAI;AACxB,aAAO,kBAAkB,GAAyC;AAAA,QAC9D;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACA,WAAS,KAAK,IAAI;AACd,WAAO,iBAAiB,EAAE;AAAA,EAC9B;AACA,WAAS,QAAQ,IAAI;AACjB,WAAO,KAAK,OAAO,iBAAiB,EAAE,GAAG,EAAE,SAAS,KAAK,CAAC,CAAC;AAAA,EAC/D;AACA,WAAS,qBAAqB,IAAI;AAC9B,UAAM,cAAc,GAAG,QAAQ,GAAG,QAAQ,SAAS,CAAC;AACpD,QAAI,eAAe,YAAY,UAAU;AACrC,YAAM,EAAE,SAAS,IAAI;AACrB,UAAI,oBAAoB,OAAO,aAAa,aAAa,SAAS,EAAE,IAAI;AACxE,UAAI,OAAO,sBAAsB,UAAU;AACvC,4BACI,kBAAkB,SAAS,GAAG,KAAK,kBAAkB,SAAS,GAAG,IAC1D,oBAAoB,iBAAiB,iBAAiB;AAAA;AAAA,UAErD,EAAE,MAAM,kBAAkB;AAAA;AAGtC,0BAAkB,SAAS,CAAC;AAAA,MAChC;AACA,UACI,kBAAkB,QAAQ,QAC1B,EAAE,UAAU,oBAAoB;AAChC,aAAK;AAAA,EAA4B,KAAK,UAAU,mBAAmB,MAAM,CAAC,CAAC;AAAA,uBAA0B,GAAG,QAAQ,2EAA2E;AAC3L,cAAM,IAAI,MAAM,kBAAkB;AAAA,MACtC;AACA,aAAO,OAAO;AAAA,QACV,OAAO,GAAG;AAAA,QACV,MAAM,GAAG;AAAA;AAAA,QAET,QAAQ,kBAAkB,QAAQ,OAAO,CAAC,IAAI,GAAG;AAAA,MACrD,GAAG,iBAAiB;AAAA,IACxB;AAAA,EACJ;AACA,WAAS,iBAAiB,IAAI,gBAAgB;AAC1C,UAAM,iBAAkB,kBAAkB,QAAQ,EAAE;AACpD,UAAM,OAAO,aAAa;AAC1B,UAAM,OAAO,GAAG;AAChB,UAAM,QAAQ,GAAG;AAEjB,UAAMC,WAAU,GAAG,YAAY;AAC/B,UAAM,iBAAiB,qBAAqB,cAAc;AAC1D,QAAI;AACA,aAAO;AAAA,QAAiB,OAAO,iBAAiB,cAAc,GAAG;AAAA,UAC7D,OAAO,OAAO,mBAAmB,WAC3B,OAAO,CAAC,GAAG,MAAM,eAAe,KAAK,IACrC;AAAA,UACN;AAAA,UACA,SAAAA;AAAA,QACJ,CAAC;AAAA;AAAA,QAED,kBAAkB;AAAA,MAAc;AAEpC,UAAM,aAAa;AACnB,eAAW,iBAAiB;AAC5B,QAAI;AACJ,QAAI,CAAC,SAAS,oBAAoB,kBAAkB,MAAM,cAAc,GAAG;AACvE,gBAAU,kBAAkB,IAA2C,EAAE,IAAI,YAAY,KAAK,CAAC;AAE/F;AAAA,QAAa;AAAA,QAAM;AAAA;AAAA;AAAA,QAGnB;AAAA;AAAA;AAAA,QAGA;AAAA,MAAK;AAAA,IACT;AACA,YAAQ,UAAU,QAAQ,QAAQ,OAAO,IAAI,SAAS,YAAY,IAAI,GACjE,MAAM,CAAC,UAAU,oBAAoB,KAAK;AAAA;AAAA,MAEvC;AAAA,QAAoB;AAAA,QAAO;AAAA;AAAA,MAA4C,IACjE,QACA,YAAY,KAAK;AAAA;AAAA;AAAA,MAEvB,aAAa,OAAO,YAAY,IAAI;AAAA,KAAC,EACxC,KAAK,CAACC,aAAY;AACnB,UAAIA,UAAS;AACT,YAAI;AAAA,UAAoBA;AAAA,UAAS;AAAA;AAAA,QAA4C,GAAG;AAC5E;AAAA,UAEI,oBAAoB,kBAAkB,QAAQA,SAAQ,EAAE,GAAG,UAAU;AAAA,UAErE;AAAA,WAEC,eAAe,SAAS,eAAe;AAAA;AAAA,YAEhC,eAAe,SAAS;AAAA,cAC1B,KAAK,IAAI;AACf,iBAAK,mFAAmF,KAAK,QAAQ,SAAS,WAAW,QAAQ;AAAA,gNAAyP;AAC1X,mBAAO,QAAQ,OAAO,IAAI,MAAM,uCAAuC,CAAC;AAAA,UAC5E;AACA,iBAAO;AAAA;AAAA,YAEP,OAAO;AAAA;AAAA,cAEH,SAAAD;AAAA,YACJ,GAAG,iBAAiBC,SAAQ,EAAE,GAAG;AAAA,cAC7B,OAAO,OAAOA,SAAQ,OAAO,WACvB,OAAO,CAAC,GAAG,MAAMA,SAAQ,GAAG,KAAK,IACjC;AAAA,cACN;AAAA,YACJ,CAAC;AAAA;AAAA,YAED,kBAAkB;AAAA,UAAU;AAAA,QAChC;AAAA,MACJ,OACK;AAED,QAAAA,WAAU,mBAAmB,YAAY,MAAM,MAAMD,UAAS,IAAI;AAAA,MACtE;AACA,uBAAiB,YAAY,MAAMC,QAAO;AAC1C,aAAOA;AAAA,IACX,CAAC;AAAA,EACL;AAMA,WAAS,iCAAiC,IAAI,MAAM;AAChD,UAAM,QAAQ,wBAAwB,IAAI,IAAI;AAC9C,WAAO,QAAQ,QAAQ,OAAO,KAAK,IAAI,QAAQ,QAAQ;AAAA,EAC3D;AACA,WAAS,eAAe,IAAI;AACxB,UAAM,MAAM,cAAc,OAAO,EAAE,KAAK,EAAE;AAE1C,WAAO,OAAO,OAAO,IAAI,mBAAmB,aACtC,IAAI,eAAe,EAAE,IACrB,GAAG;AAAA,EACb;AAEA,WAAS,SAAS,IAAI,MAAM;AACxB,QAAI;AACJ,UAAM,CAAC,gBAAgB,iBAAiB,eAAe,IAAI,uBAAuB,IAAI,IAAI;AAE1F,aAAS,wBAAwB,eAAe,QAAQ,GAAG,oBAAoB,IAAI,IAAI;AAEvF,eAAW,UAAU,gBAAgB;AACjC,aAAO,YAAY,QAAQ,WAAS;AAChC,eAAO,KAAK,iBAAiB,OAAO,IAAI,IAAI,CAAC;AAAA,MACjD,CAAC;AAAA,IACL;AACA,UAAM,0BAA0B,iCAAiC,KAAK,MAAM,IAAI,IAAI;AACpF,WAAO,KAAK,uBAAuB;AAEnC,WAAQ,cAAc,MAAM,EACvB,KAAK,MAAM;AAEZ,eAAS,CAAC;AACV,iBAAW,SAAS,aAAa,KAAK,GAAG;AACrC,eAAO,KAAK,iBAAiB,OAAO,IAAI,IAAI,CAAC;AAAA,MACjD;AACA,aAAO,KAAK,uBAAuB;AACnC,aAAO,cAAc,MAAM;AAAA,IAC/B,CAAC,EACI,KAAK,MAAM;AAEZ,eAAS,wBAAwB,iBAAiB,qBAAqB,IAAI,IAAI;AAC/E,iBAAW,UAAU,iBAAiB;AAClC,eAAO,aAAa,QAAQ,WAAS;AACjC,iBAAO,KAAK,iBAAiB,OAAO,IAAI,IAAI,CAAC;AAAA,QACjD,CAAC;AAAA,MACL;AACA,aAAO,KAAK,uBAAuB;AAEnC,aAAO,cAAc,MAAM;AAAA,IAC/B,CAAC,EACI,KAAK,MAAM;AAEZ,eAAS,CAAC;AACV,iBAAW,UAAU,iBAAiB;AAElC,YAAI,OAAO,aAAa;AACpB,cAAI,QAAQ,OAAO,WAAW,GAAG;AAC7B,uBAAW,eAAe,OAAO;AAC7B,qBAAO,KAAK,iBAAiB,aAAa,IAAI,IAAI,CAAC;AAAA,UAC3D,OACK;AACD,mBAAO,KAAK,iBAAiB,OAAO,aAAa,IAAI,IAAI,CAAC;AAAA,UAC9D;AAAA,QACJ;AAAA,MACJ;AACA,aAAO,KAAK,uBAAuB;AAEnC,aAAO,cAAc,MAAM;AAAA,IAC/B,CAAC,EACI,KAAK,MAAM;AAGZ,SAAG,QAAQ,QAAQ,YAAW,OAAO,iBAAiB,CAAC,CAAE;AAEzD,eAAS,wBAAwB,iBAAiB,oBAAoB,IAAI,MAAM,cAAc;AAC9F,aAAO,KAAK,uBAAuB;AAEnC,aAAO,cAAc,MAAM;AAAA,IAC/B,CAAC,EACI,KAAK,MAAM;AAEZ,eAAS,CAAC;AACV,iBAAW,SAAS,oBAAoB,KAAK,GAAG;AAC5C,eAAO,KAAK,iBAAiB,OAAO,IAAI,IAAI,CAAC;AAAA,MACjD;AACA,aAAO,KAAK,uBAAuB;AACnC,aAAO,cAAc,MAAM;AAAA,IAC/B,CAAC,EAEI,MAAM,SAAO;AAAA,MAAoB;AAAA,MAAK;AAAA;AAAA,IAAuC,IAC5E,MACA,QAAQ,OAAO,GAAG,CAAC;AAAA,EAC7B;AACA,WAAS,iBAAiB,IAAI,MAAM,SAAS;AAGzC,gBACK,KAAK,EACL,QAAQ,WAAS,eAAe,MAAM,MAAM,IAAI,MAAM,OAAO,CAAC,CAAC;AAAA,EACxE;AAMA,WAAS,mBAAmB,YAAY,MAAM,QAAQD,UAAS,MAAM;AAEjE,UAAM,QAAQ,wBAAwB,YAAY,IAAI;AACtD,QAAI;AACA,aAAO;AAEX,UAAM,oBAAoB,SAAS;AACnC,UAAM,QAAQ,CAAC,YAAY,CAAC,IAAI,QAAQ;AAGxC,QAAI,QAAQ;AAGR,UAAIA,YAAW;AACX,sBAAc,QAAQ,WAAW,UAAU,OAAO;AAAA,UAC9C,QAAQ,qBAAqB,SAAS,MAAM;AAAA,QAChD,GAAG,IAAI,CAAC;AAAA;AAER,sBAAc,KAAK,WAAW,UAAU,IAAI;AAAA,IACpD;AAEA,iBAAa,QAAQ;AACrB,iBAAa,YAAY,MAAM,QAAQ,iBAAiB;AACxD,gBAAY;AAAA,EAChB;AACA,MAAI;AAEJ,WAAS,iBAAiB;AAEtB,QAAI;AACA;AACJ,4BAAwB,cAAc,OAAO,CAAC,IAAI,OAAO,SAAS;AAC9D,UAAI,CAAC,OAAO;AACR;AAEJ,YAAM,aAAa,QAAQ,EAAE;AAI7B,YAAM,iBAAiB,qBAAqB,UAAU;AACtD,UAAI,gBAAgB;AAChB,yBAAiB,OAAO,gBAAgB,EAAE,SAAS,MAAM,OAAO,KAAK,CAAC,GAAG,UAAU,EAAE,MAAM,IAAI;AAC/F;AAAA,MACJ;AACA,wBAAkB;AAClB,YAAM,OAAO,aAAa;AAE1B,UAAI,WAAW;AACX,2BAAmB,aAAa,KAAK,UAAU,KAAK,KAAK,GAAG,sBAAsB,CAAC;AAAA,MACvF;AACA,eAAS,YAAY,IAAI,EACpB,MAAM,CAAC,UAAU;AAClB,YAAI;AAAA,UAAoB;AAAA,UAAO,IAAwC;AAAA;AAAA,QAAuC,GAAG;AAC7G,iBAAO;AAAA,QACX;AACA,YAAI;AAAA,UAAoB;AAAA,UAAO;AAAA;AAAA,QAA4C,GAAG;AAU1E;AAAA,YAAiB,OAAO,iBAAiB,MAAM,EAAE,GAAG;AAAA,cAChD,OAAO;AAAA,YACX,CAAC;AAAA,YAAG;AAAA;AAAA,UAEJ,EACK,KAAK,aAAW;AAIjB,gBAAI;AAAA,cAAoB;AAAA,cAAS,IAC7B;AAAA;AAAA,YAAyC,KACzC,CAAC,KAAK,SACN,KAAK,SAAS,eAAe,KAAK;AAClC,4BAAc,GAAG,IAAI,KAAK;AAAA,YAC9B;AAAA,UACJ,CAAC,EACI,MAAM,IAAI;AAEf,iBAAO,QAAQ,OAAO;AAAA,QAC1B;AAEA,YAAI,KAAK,OAAO;AACZ,wBAAc,GAAG,CAAC,KAAK,OAAO,KAAK;AAAA,QACvC;AAEA,eAAO,aAAa,OAAO,YAAY,IAAI;AAAA,MAC/C,CAAC,EACI,KAAK,CAAC,YAAY;AACnB,kBACI,WACI;AAAA;AAAA,UAEA;AAAA,UAAY;AAAA,UAAM;AAAA,QAAK;AAE/B,YAAI,SAAS;AACT,cAAI,KAAK;AAAA;AAAA,UAGL,CAAC;AAAA,YAAoB;AAAA,YAAS;AAAA;AAAA,UAAuC,GAAG;AACxE,0BAAc,GAAG,CAAC,KAAK,OAAO,KAAK;AAAA,UACvC,WACS,KAAK,SAAS,eAAe,OAClC;AAAA,YAAoB;AAAA,YAAS,IAAwC;AAAA;AAAA,UAAyC,GAAG;AAGjH,0BAAc,GAAG,IAAI,KAAK;AAAA,UAC9B;AAAA,QACJ;AACA,yBAAiB,YAAY,MAAM,OAAO;AAAA,MAC9C,CAAC,EAEI,MAAM,IAAI;AAAA,IACnB,CAAC;AAAA,EACL;AAEA,MAAI,gBAAgB,aAAa;AACjC,MAAI,iBAAiB,aAAa;AAClC,MAAI;AASJ,WAAS,aAAa,OAAO,IAAI,MAAM;AACnC,gBAAY,KAAK;AACjB,UAAM,OAAO,eAAe,KAAK;AACjC,QAAI,KAAK,QAAQ;AACb,WAAK,QAAQ,aAAW,QAAQ,OAAO,IAAI,IAAI,CAAC;AAAA,IACpD,OACK;AACD,UAAK,MAAwC;AACzC,aAAK,yCAAyC;AAAA,MAClD;AACA,cAAQ,MAAM,KAAK;AAAA,IACvB;AAEA,WAAO,QAAQ,OAAO,KAAK;AAAA,EAC/B;AACA,WAAS,UAAU;AACf,QAAI,SAAS,aAAa,UAAU;AAChC,aAAO,QAAQ,QAAQ;AAC3B,WAAO,IAAI,QAAQ,CAACE,UAAS,WAAW;AACpC,oBAAc,IAAI,CAACA,UAAS,MAAM,CAAC;AAAA,IACvC,CAAC;AAAA,EACL;AACA,WAAS,YAAY,KAAK;AACtB,QAAI,CAAC,OAAO;AAER,cAAQ,CAAC;AACT,qBAAe;AACf,oBACK,KAAK,EACL,QAAQ,CAAC,CAACA,UAAS,MAAM,MAAO,MAAM,OAAO,GAAG,IAAIA,SAAQ,CAAE;AACnE,oBAAc,MAAM;AAAA,IACxB;AACA,WAAO;AAAA,EACX;AAEA,WAAS,aAAa,IAAI,MAAM,QAAQ,mBAAmB;AACvD,UAAM,EAAE,eAAe,IAAI;AAC3B,QAAI,CAAC,aAAa,CAAC;AACf,aAAO,QAAQ,QAAQ;AAC3B,UAAM,iBAAkB,CAAC,UAAU,uBAAuB,aAAa,GAAG,UAAU,CAAC,CAAC,MAChF,qBAAqB,CAAC,WACpB,QAAQ,SACR,QAAQ,MAAM,UAClB;AACJ,WAAO,SAAS,EACX,KAAK,MAAM,eAAe,IAAI,MAAM,cAAc,CAAC,EACnD,KAAK,cAAY,YAAY,iBAAiB,QAAQ,CAAC,EACvD,MAAM,SAAO,aAAa,KAAK,IAAI,IAAI,CAAC;AAAA,EACjD;AACA,QAAM,KAAK,CAAC,UAAU,cAAc,GAAG,KAAK;AAC5C,MAAI;AACJ,QAAM,gBAAgB,oBAAI,IAAI;AAC9B,QAAM,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA,aAAa,QAAQ;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM,MAAM,GAAG,EAAE;AAAA,IACjB,SAAS,MAAM,GAAG,CAAC;AAAA,IACnB,YAAY,aAAa;AAAA,IACzB,eAAe,oBAAoB;AAAA,IACnC,WAAW,YAAY;AAAA,IACvB,SAAS,eAAe;AAAA,IACxB;AAAA,IACA,QAAQ,KAAK;AACT,YAAMC,UAAS;AACf,UAAI,UAAU,cAAc,UAAU;AACtC,UAAI,UAAU,cAAc,UAAU;AACtC,UAAI,OAAO,iBAAiB,UAAUA;AACtC,aAAO,eAAe,IAAI,OAAO,kBAAkB,UAAU;AAAA,QACzD,YAAY;AAAA,QACZ,KAAK,MAAM,MAAM,YAAY;AAAA,MACjC,CAAC;AAID,UAAI;AAAA;AAAA,MAGA,CAAC,WACD,aAAa,UAAU,2BAA2B;AAElD,kBAAU;AACV,aAAK,cAAc,QAAQ,EAAE,MAAM,SAAO;AACtC,cAAK;AACD,iBAAK,8CAA8C,GAAG;AAAA,QAC9D,CAAC;AAAA,MACL;AACA,YAAM,gBAAgB,CAAC;AACvB,iBAAW,OAAO,2BAA2B;AACzC,eAAO,eAAe,eAAe,KAAK;AAAA,UACtC,KAAK,MAAM,aAAa,MAAM,GAAG;AAAA,UACjC,YAAY;AAAA,QAChB,CAAC;AAAA,MACL;AACA,UAAI,QAAQ,WAAWA,OAAM;AAC7B,UAAI,QAAQ,kBAAkB,gBAAgB,aAAa,CAAC;AAC5D,UAAI,QAAQ,uBAAuB,YAAY;AAC/C,YAAM,aAAa,IAAI;AACvB,oBAAc,IAAI,GAAG;AACrB,UAAI,UAAU,WAAY;AACtB,sBAAc,OAAO,GAAG;AAExB,YAAI,cAAc,OAAO,GAAG;AAExB,4BAAkB;AAClB,mCAAyB,sBAAsB;AAC/C,kCAAwB;AACxB,uBAAa,QAAQ;AACrB,oBAAU;AACV,kBAAQ;AAAA,QACZ;AACA,mBAAW;AAAA,MACf;AAEA,UAA0E,WAAW;AACjF,oBAAY,KAAKA,SAAQ,OAAO;AAAA,MACpC;AAAA,IACJ;AAAA,EACJ;AAEA,WAAS,cAAc,QAAQ;AAC3B,WAAO,OAAO,OAAO,CAAC,SAAS,UAAU,QAAQ,KAAK,MAAM,eAAe,KAAK,CAAC,GAAG,QAAQ,QAAQ,CAAC;AAAA,EACzG;AACA,SAAO;AACX;AACA,SAAS,uBAAuB,IAAI,MAAM;AACtC,QAAM,iBAAiB,CAAC;AACxB,QAAM,kBAAkB,CAAC;AACzB,QAAM,kBAAkB,CAAC;AACzB,QAAM,MAAM,KAAK,IAAI,KAAK,QAAQ,QAAQ,GAAG,QAAQ,MAAM;AAC3D,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,UAAM,aAAa,KAAK,QAAQ,CAAC;AACjC,QAAI,YAAY;AACZ,UAAI,GAAG,QAAQ,KAAK,YAAU,kBAAkB,QAAQ,UAAU,CAAC;AAC/D,wBAAgB,KAAK,UAAU;AAAA;AAE/B,uBAAe,KAAK,UAAU;AAAA,IACtC;AACA,UAAM,WAAW,GAAG,QAAQ,CAAC;AAC7B,QAAI,UAAU;AAEV,UAAI,CAAC,KAAK,QAAQ,KAAK,YAAU,kBAAkB,QAAQ,QAAQ,CAAC,GAAG;AACnE,wBAAgB,KAAK,QAAQ;AAAA,MACjC;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,CAAC,gBAAgB,iBAAiB,eAAe;AAC5D;;;ACtrHA,SAAS,cAAc,cAAc;AACnC,MAAI,SAAS,eAAe,WAAW;AACrC,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAS,iBAAiB,oBAAoB,MAAM,QAAQ,YAAY,CAAC;AAAA,IAC3E,CAAC;AAAA,EACH;AACA,SAAO,QAAQ,QAAQ,YAAY;AACrC;AAEA,IAAM,aAAa,gBAAgB;AAAA,EACjC,MAAM,OAAO,EAAE,MAAM,GAAG;AACtB,UAAM,UAAU,IAAI,KAAK;AACzB,cAAU,MAAM,QAAQ,QAAQ,IAAI;AACpC,WAAO,MAAM;AACX,UAAI,CAAC,QAAQ;AACX,eAAO,MAAM,eAAe,MAAM,YAAY,CAAC,CAAC;AAClD,aAAO,MAAM,WAAW,MAAM,QAAQ,CAAC,CAAC;AAAA,IAC1C;AAAA,EACF;AACF,CAAC;;;ACGD,SAAS,iBAAiB,OAAO;AAC/B,MAAI;AACF,WAAO,KAAK,MAAM,SAAS,IAAI;AAAA,EACjC,SAAS,OAAO;AACd,YAAQ,MAAM,oCAAoC,OAAO,KAAK;AAC9D,WAAO,CAAC;AAAA,EACV;AACF;;;ACzBA,SAAS,QAAQ,KAAK,eAAe,IAAI,UAAU,CAAC,GAAG;AACrD,QAAM;AAAA,IACJ;AAAA,IACA,qBAAqB;AAAA,IACrB,SAAAC,WAAU;AAAA,IACV,gBAAgB;AAAA,EAClB,IAAI;AACJ,QAAM,WAAW,OAAO,WAAW;AACnC,iBAAe,YAAY,SAAS,OAAO,WAAW;AACpD,UAAM,MAAM,SAAS,UAAU,GAAG,IAAI,aAAa,GAAG;AACtD,QAAI;AACJ,QAAIA,UAAS;AACX,aAAOC,YAAW;AAClB,UAAI,IAAI,IAAI;AAAA,IACd;AACA,UAAM,SAAS,aAAa;AAAA,MAC1B,SAAS,SAAS,iBAAiB,cAAc,IAAI,IAAI,oBAAoB,cAAc,IAAI;AAAA,MAC/F,GAAG;AAAA,IACL,CAAC;AACD,UAAM,EAAE,OAAO,IAAI;AACnB,QAAI;AACF,UAAI,UAAU,cAAc,UAAU;AACxC,UAAM,qBAAqB,CAAC;AAC5B,UAAM,mBAAmB,SAAS,MAAM;AAAA,IACxC,IAAI,CAAC,OAAO,mBAAmB,KAAK,EAAE;AACtC,UAAM,0BAA0B,MAAM;AACpC,aAAO,QAAQ,IAAI,mBAAmB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;AAAA,IACzD;AACA,UAAM,UAAU;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,CAAC;AAAA,MACf;AAAA,MACA;AAAA,IACF;AACA,QAAI,QAAQ;AACV,YAAM,cAAc;AACpB,cAAQ,gBAAe,iDAAiB,OAAO,qBAAqB,CAAC,OAAM,iBAAiB,OAAO,iBAAiB;AAAA,IACtH;AACA,WAAM,yBAAK;AACX,QAAI,IAAI,MAAM;AACd,QAAI;AACJ,QAAI,eAAe;AACnB,WAAO,WAAW,CAAC,IAAI,MAAM,SAAS;AACpC,UAAI,gBAAgB,kBAAkB,mBAAmB,GAAG,MAAM;AAChE,uBAAe;AACf,yBAAiB,GAAG;AACpB,WAAG,KAAK,QAAQ,QAAQ;AAAA,MAC1B;AACA,WAAK;AAAA,IACP,CAAC;AACD,QAAI,CAAC,QAAQ;AACX,YAAM,QAAQ,QAAQ,aAAa;AACnC,aAAO,KAAK,KAAK;AACjB,YAAM,OAAO,QAAQ;AACrB,cAAQ,eAAe,OAAO,aAAa,MAAM,KAAK,SAAS,CAAC;AAAA,IAClE;AACA,UAAM,eAAe,QAAQ;AAC7B,WAAO;AAAA,MACL,GAAG;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACA,MAAI,UAAU;AACZ,KAAC,YAAY;AACX,YAAM,EAAE,KAAK,OAAO,IAAI,MAAM,YAAY,IAAI;AAC9C,YAAM,OAAO,QAAQ;AACrB,UAAI,MAAM,eAAe,IAAI;AAAA,IAC/B,GAAG;AAAA,EACL;AACA,SAAO;AACT;", "names": ["h", "k", "p", "k", "_a", "k2", "k", "p", "p", "p2", "ref", "k", "createHead", "parse<PERSON><PERSON>y", "location", "stringifyQuery", "NavigationType", "NavigationDirection", "history", "replace", "NavigationFailureType", "location", "re", "k", "value", "p", "route", "matchedRoute", "href", "replace", "failure", "resolve", "router", "useHead", "createHead"]}