<template>
  <div class="basic-data-preparation">
    <h2>基础数据准备情况表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-width="120px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="数据开始日期">
              <el-date-picker
                v-model="queryForm.startDate"
                type="month"
                placeholder="选择开始月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="数据结束日期">
              <el-date-picker
                v-model="queryForm.endDate"
                type="month"
                placeholder="选择结束月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="数据状态">
              <el-select
                v-model="queryForm.dataStatus"
                placeholder="请选择数据状态"
                clearable
                style="width: 100%"
              >
                <el-option label="未采集" value="0" />
                <el-option label="已采集" value="1" />
                <el-option label="未录入" value="2" />
                <el-option label="已录入" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button class="query-btn" @click="handleQuery">查询</el-button>
              <el-button class="export-btn" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" border style="width: 100%" v-loading="loading" size="small">
        <el-table-column prop="period" label="数据日期" width="100" />
        <el-table-column prop="task_source_type" label="数据来源类型" width="120">
          <template #default="scope">
            {{ formatSourceType(scope.row.task_source_type) }}
          </template>
        </el-table-column>
        <el-table-column prop="source_system" label="源系统" width="120" />
        <el-table-column prop="report_name" label="报表名称" min-width="200" />
        <el-table-column prop="row_nums" label="数据条目数" width="120" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.row_nums) }}
          </template>
        </el-table-column>
        <el-table-column prop="department" label="部门" width="150" />
        <el-table-column prop="data_status" label="数据状态" width="100">
          <template #default="scope">
            <el-tag
              :type="getStatusTagType(scope.row.data_status)"
              size="small"
            >
              {{ formatDataStatus(scope.row.data_status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="准备人" width="100" />
        <el-table-column prop="auditor" label="审核人" width="100" />
        <el-table-column prop="table_en_name" label="表英文名" min-width="200" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as XLSX from 'xlsx'

// 查询表单
const queryForm = reactive({
  startDate: '',
  endDate: '',
  dataStatus: ''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 格式化数据来源类型
const formatSourceType = (type) => {
  switch (type) {
    case 'sys':
      return '系统采集'
    case 'manu':
      return '人工录入'
    default:
      return type
  }
}

// 格式化数据状态
const formatDataStatus = (status) => {
  switch (status) {
    case '0':
      return '未采集'
    case '1':
      return '已采集'
    case '2':
      return '未录入'
    case '3':
      return '已录入'
    default:
      return status
  }
}

// 格式化数字
const formatNumber = (num) => {
  if (num === null || num === undefined) return '0'
  return Number(num).toLocaleString()
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  switch (status) {
    case '3': // 已录入
    case '1': // 已采集
      return 'success'
    case '2': // 未录入
    case '0': // 未采集
      return 'danger'
    default:
      return 'info'
  }
}

// 初始化数据
onMounted(() => {
  handleQuery()
})

// 查询
const handleQuery = async () => {
  loading.value = true

  try {
    // 构建查询参数
    const params = new URLSearchParams()

    // 添加分页参数
    const offset = (currentPage.value - 1) * pageSize.value
    params.append('limit', pageSize.value.toString())
    params.append('offset', offset.toString())

    // 添加排序参数
    params.append('order', 'period.desc,task_id.desc')

    // 添加查询条件
    if (queryForm.startDate) {
      params.append('period', `gte.${queryForm.startDate}`)
    }

    if (queryForm.endDate) {
      params.append('period', `lte.${queryForm.endDate}`)
    }

    if (queryForm.dataStatus) {
      params.append('data_status', `eq.${queryForm.dataStatus}`)
    }

    // 构建完整的URL
    const url = `http://10.242.194.62:7300/data_task_check_result?${params.toString()}`

    // 发送请求
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept-Profile': 'mkt_mang',
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()

    // 获取总数（需要单独请求）
    try {
      const countParams = new URLSearchParams()
      countParams.append('select', 'count')

      // 添加相同的过滤条件
      if (queryForm.startDate) {
        countParams.append('period', `gte.${queryForm.startDate}`)
      }

      if (queryForm.endDate) {
        countParams.append('period', `lte.${queryForm.endDate}`)
      }

      if (queryForm.dataStatus) {
        countParams.append('data_status', `eq.${queryForm.dataStatus}`)
      }

      const countResponse = await fetch(`http://10.242.194.62:7300/data_task_check_result?${countParams.toString()}`, {
        headers: {
          'Accept-Profile': 'mkt_mang',
          'Content-Type': 'application/json',
          'Prefer': 'count=exact'
        }
      })

      if (countResponse.ok) {
        const countData = await countResponse.json()
        totalCount.value = parseInt(countData[0]?.count || 0)
      } else {
        // 如果count查询失败，使用Content-Range头获取总数
        const rangeHeader = response.headers.get('Content-Range')
        if (rangeHeader) {
          const match = rangeHeader.match(/\/(\d+)$/)
          totalCount.value = match ? parseInt(match[1]) : data.length
        } else {
          totalCount.value = data.length
        }
      }
    } catch (countError) {
      console.warn('获取总数失败:', countError)
      totalCount.value = data.length
    }

    tableData.value = data

  } catch (error) {
    console.error('查询数据时发生错误:', error)
    ElMessage.error('查询数据失败，请检查网络连接')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 导出
const handleExport = async () => {
  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建查询参数，获取所有数据（不分页）
    const params = new URLSearchParams()
    params.append('order', 'period.desc,task_id.desc')

    // 添加查询条件
    if (queryForm.startDate) {
      params.append('period', `gte.${queryForm.startDate}`)
    }

    if (queryForm.endDate) {
      params.append('period', `lte.${queryForm.endDate}`)
    }

    if (queryForm.dataStatus) {
      params.append('data_status', `eq.${queryForm.dataStatus}`)
    }

    // 获取所有数据
    const response = await fetch(`http://10.242.194.62:7300/data_task_check_result?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Accept-Profile': 'mkt_mang',
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const allData = await response.json()

    if (allData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        '数据日期', '数据来源类型', '源系统', '报表名称', '数据条目数',
        '部门', '数据状态', '准备人', '审核人', '表英文名'
      ],
      // 数据行
      ...allData.map(item => [
        item.period,
        formatSourceType(item.task_source_type),
        item.source_system || '',
        item.report_name || '',
        formatNumber(item.row_nums),
        item.department || '',
        formatDataStatus(item.data_status),
        item.creator || '',
        item.auditor || '',
        item.table_en_name || ''
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 12 }, // 数据日期
      { wch: 15 }, // 数据来源类型
      { wch: 15 }, // 源系统
      { wch: 30 }, // 报表名称
      { wch: 15 }, // 数据条目数
      { wch: 20 }, // 部门
      { wch: 12 }, // 数据状态
      { wch: 12 }, // 准备人
      { wch: 12 }, // 审核人
      { wch: 35 }  // 表英文名
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '基础数据准备情况')

    // 生成文件名
    const now = new Date()
    const fileName = `基础数据准备情况表_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}
</script>

<style scoped>
.basic-data-preparation {
  padding: 20px;
  width: 100%;
  min-height: 100vh;
  background-color: var(--ep-bg-color);
  box-sizing: border-box;
  text-align: left;
}

.query-card {
  margin-bottom: 20px;
}

.query-form {
  padding: 10px 0;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  gap: 10px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 自定义按钮颜色 */
.query-btn {
  background-color: #16D585 !important;
  border-color: #16D585 !important;
  color: white !important;
  padding: 8px 16px;
  min-width: auto;
}

.query-btn:hover {
  background-color: #14c578 !important;
  border-color: #14c578 !important;
}

.export-btn {
  background-color: #F4CD33 !important;
  border-color: #F4CD33 !important;
  color: #333 !important;
  padding: 8px 16px;
  min-width: auto;
}

.export-btn:hover {
  background-color: #f3c620 !important;
  border-color: #f3c620 !important;
}

/* 表格样式优化 */
.el-table {
  font-size: 12px;
}

.el-table th {
  background-color: #f5f7fa;
  font-weight: bold;
}

.el-table td {
  padding: 8px 0;
}

/* 状态标签样式 */
.el-tag {
  font-weight: bold;
}
</style>
