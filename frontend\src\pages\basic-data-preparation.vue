<template>
  <div class="basic-data-preparation">
    <h2>基础数据准备情况表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-width="120px" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="数据开始日期">
              <el-date-picker
                v-model="queryForm.startDate"
                type="month"
                placeholder="选择开始月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="数据结束日期">
              <el-date-picker
                v-model="queryForm.endDate"
                type="month"
                placeholder="选择结束月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="数据状态">
              <el-select
                v-model="queryForm.dataStatus"
                placeholder="请选择数据状态"
                clearable
                style="width: 100%"
              >
                <el-option label="未录入" value="未录入" />
                <el-option label="待审核" value="待审核" />
                <el-option label="已审核" value="已审核" />
                <el-option label="未采集" value="未采集" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button class="query-btn" @click="handleQuery">查询</el-button>
              <el-button class="export-btn" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" border style="width: 100%" v-loading="loading" size="small">
        <el-table-column prop="dataDate" label="数据日期" width="100" />
        <el-table-column prop="dataSourceType" label="数据来源类型" width="120" />
        <el-table-column prop="sourceSystem" label="源系统" width="120" />
        <el-table-column prop="reportName" label="报表名称" min-width="200" />
        <el-table-column prop="dataCount" label="数据条目数" width="120" align="right" />
        <el-table-column prop="department" label="部门" width="150" />
        <el-table-column prop="dataStatus" label="数据状态" width="100">
          <template #default="scope">
            <el-tag
              :type="getStatusTagType(scope.row.dataStatus)"
              size="small"
            >
              {{ scope.row.dataStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="preparer" label="准备人" width="100" />
        <el-table-column prop="auditor" label="审核人" width="100" />
        <el-table-column prop="tableEnglishName" label="表英文名" min-width="200" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 查询表单
const queryForm = reactive({
  startDate: '',
  endDate: '',
  dataStatus: ''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 生成模拟数据
const generateMockData = () => {
  const data = []
  const dataSourceTypes = ['人工录入', '系统采集']
  const sourceSystems = ['集团ERP', 'CTP', '核心系统', '风控系统', '营销系统']
  const reportNames = [
    '分支机构考核计息利率录入',
    'FIND机构信息表',
    '利润相关数据录入表',
    '客户基础信息表',
    '产品信息维护表',
    '风险评级数据表',
    '营销活动统计表',
    '业务流水明细表',
    '资产负债表数据',
    '现金流量表数据',
    '损益表数据',
    '监管报表数据',
    '客户交易明细',
    '产品收益统计',
    '风险敞口数据'
  ]
  const departments = ['财务部', '结算与风控部', '营销管理总部', '信息技术部', '合规部', '运营部']
  const dataStatuses = ['未录入', '待审核', '已审核', '未采集']
  const preparers = ['xurr1', 'zhouyc1', 'admin', 'user1', 'XX系统']
  const auditors = ['XX', 'admin', 'auditor1', 'manager1']
  const tableNames = [
    'finregu.dwd_crse_bal',
    'finregu.find_dim_cust',
    'mkt_base.temp_init_fin_rptl',
    'risk.customer_rating',
    'core.product_info',
    'mkt.campaign_stats',
    'trade.transaction_detail',
    'fin.balance_sheet',
    'fin.cash_flow',
    'fin.income_statement'
  ]

  for (let i = 0; i < 20; i++) {
    const month = String(Math.floor(Math.random() * 12) + 1).padStart(2, '0')
    const year = 2024 + Math.floor(i / 12)
    const dataCount = Math.floor(Math.random() * 100000) + 1000

    data.push({
      id: i + 1,
      dataDate: `${year}-${month}`,
      dataSourceType: dataSourceTypes[i % dataSourceTypes.length],
      sourceSystem: sourceSystems[i % sourceSystems.length],
      reportName: reportNames[i % reportNames.length],
      dataCount: dataCount.toLocaleString(),
      department: departments[i % departments.length],
      dataStatus: dataStatuses[i % dataStatuses.length],
      preparer: preparers[i % preparers.length],
      auditor: auditors[i % auditors.length],
      tableEnglishName: tableNames[i % tableNames.length]
    })
  }

  return data
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  switch (status) {
    case '已审核':
      return 'success'
    case '待审核':
      return 'warning'
    case '未录入':
    case '未采集':
      return 'danger'
    default:
      return 'info'
  }
}

// 初始化数据
onMounted(() => {
  handleQuery()
})

// 查询
const handleQuery = () => {
  loading.value = true

  // 模拟API调用
  setTimeout(() => {
    let allData = generateMockData()

    // 根据查询条件过滤数据
    if (queryForm.startDate || queryForm.endDate || queryForm.dataStatus) {
      allData = allData.filter(item => {
        let matchStart = true
        let matchEnd = true
        let matchStatus = true

        if (queryForm.startDate) {
          matchStart = item.dataDate >= queryForm.startDate
        }
        if (queryForm.endDate) {
          matchEnd = item.dataDate <= queryForm.endDate
        }
        if (queryForm.dataStatus) {
          matchStatus = item.dataStatus === queryForm.dataStatus
        }

        return matchStart && matchEnd && matchStatus
      })
    }

    // 设置总数
    totalCount.value = allData.length

    // 计算分页数据
    const startIndex = (currentPage.value - 1) * pageSize.value
    const endIndex = startIndex + pageSize.value
    tableData.value = allData.slice(startIndex, endIndex)

    loading.value = false
  }, 500)
}

// 导出
const handleExport = () => {
  ElMessage.success('数据导出成功')
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}
</script>

<style scoped>
.basic-data-preparation {
  padding: 20px;
  width: 100%;
  min-height: 100vh;
  background-color: var(--ep-bg-color);
  box-sizing: border-box;
  text-align: left;
}

.query-card {
  margin-bottom: 20px;
}

.query-form {
  padding: 10px 0;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  gap: 10px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 自定义按钮颜色 */
.query-btn {
  background-color: #16D585 !important;
  border-color: #16D585 !important;
  color: white !important;
  padding: 8px 16px;
  min-width: auto;
}

.query-btn:hover {
  background-color: #14c578 !important;
  border-color: #14c578 !important;
}

.export-btn {
  background-color: #F4CD33 !important;
  border-color: #F4CD33 !important;
  color: #333 !important;
  padding: 8px 16px;
  min-width: auto;
}

.export-btn:hover {
  background-color: #f3c620 !important;
  border-color: #f3c620 !important;
}

/* 表格样式优化 */
.el-table {
  font-size: 12px;
}

.el-table th {
  background-color: #f5f7fa;
  font-weight: bold;
}

.el-table td {
  padding: 8px 0;
}

/* 状态标签样式 */
.el-tag {
  font-weight: bold;
}
</style>
