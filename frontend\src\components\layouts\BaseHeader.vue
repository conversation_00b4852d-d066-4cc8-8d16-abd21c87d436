<script setup>
import { toggleDark } from '~/composables'

// 营销管理系统的GitHub仓库地址
const repository = {
  url: 'https://github.com/marketing-management/marketing-management-pub'
}
</script>

<template>
  <el-menu class="el-menu-demo" mode="horizontal" :ellipsis="false" router>
    <el-menu-item index="/">
      <div class="flex items-center justify-center gap-2">
        <div class="text-xl" i-ep-element-plus />
        <span>营销管理系统</span>
      </div>
    </el-menu-item>
    <el-sub-menu index="2">
      <template #title>
        分支机构管理
      </template>
      <el-menu-item index="/branch-rating-setting">
        分类评级设置
      </el-menu-item>
      <el-menu-item index="/financial-data-entry">
        初始财务报表数据录入
      </el-menu-item>
      <el-menu-item index="/basic-data-preparation">
        基础数据准备情况表
      </el-menu-item>
      <el-menu-item index="2-2">
        机构信息管理
      </el-menu-item>
      <el-menu-item index="2-3">
        评级历史查询
      </el-menu-item>
    </el-sub-menu>
    <el-menu-item index="3">
      系统管理
    </el-menu-item>
    <el-menu-item index="4">
      数据统计
    </el-menu-item>

    <el-menu-item h="full" @click="toggleDark()">
      <button
        class="w-full cursor-pointer border-none bg-transparent"
        style="height: var(--ep-menu-item-height)"
      >
        <i inline-flex i="dark:ep-moon ep-sunny" />
      </button>
    </el-menu-item>

    <el-menu-item h="full">
      <a class="size-full flex items-center justify-center" :href="repository.url" target="_blank">
        <div i-ri-github-fill />
      </a>
    </el-menu-item>
  </el-menu>
</template>

<style lang="scss">
.el-menu-demo {
  &.ep-menu--horizontal > .ep-menu-item:nth-child(1) {
    margin-right: auto;
  }
}
</style>
