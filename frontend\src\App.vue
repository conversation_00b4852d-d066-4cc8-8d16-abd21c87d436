<template>
  <el-config-provider namespace="ep">
    <!-- 独立页面：不显示导航栏和侧边栏 -->
    <div v-if="isStandalonePage" class="standalone-page">
      <BaseHeader />
      <RouterView />
    </div>
    <!-- 普通页面：显示完整布局 -->
    <div v-else>
      <BaseHeader />
      <div class="main-container flex">
        <BaseSide />
        <div w="full" py="4">
          <RouterView />
        </div>
      </div>
    </div>
  </el-config-provider>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

// 定义需要独立显示的页面路径
const standalonePages = ['/branch-rating-setting', '/financial-data-entry', '/basic-data-preparation', '/initial-financial-report', '/system-data-audit']

// 判断当前页面是否为独立页面
const isStandalonePage = computed(() => {
  return standalonePages.includes(route.path)
})
</script>

<style>
#app {
  text-align: center;
  color: var(--ep-text-color-primary);
}

.main-container {
  height: calc(100vh - var(--ep-menu-item-height) - 4px);
}

.standalone-page {
  width: 100%;
  height: 100vh;
  overflow: auto;
}
</style>
