<template>
  <div class="branch-rating-setting">
    <h2>分支机构考核指标权重设置</h2>
    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" ref="form" inline>
        <el-form-item label="分支机构等级:">
          <el-select
            v-model="queryForm.brch_grad"
            placeholder="请选择分支机构等级"
            clearable
            @clear="handleClear"
            style="width: 180px">
            <el-option label="A" value="A" />
            <el-option label="B" value="B" />
            <el-option label="C" value="C" />
          </el-select>
        </el-form-item>
        <el-form-item label="指标代码:">
          <el-select
            v-model="queryForm.indx_cd"
            placeholder="请选择指标代码"
            clearable
            @clear="handleClear"
            style="width: 180px">
            <el-option label="001" value="001" />
            <el-option label="002" value="002" />
            <el-option label="003" value="003" />
          </el-select>
        </el-form-item>
      </el-form>
      <div class="button-group" style="margin-left: 1000px;">
        <el-button type="success" @click="handleQuery">查询</el-button>
        <el-button type="primary" @click="handleAdd">新增</el-button>
        <el-button type="warning" @click="handleExport">导出</el-button>
      </div>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" border style="width: 100%" v-loading="loading">
        <el-table-column prop="tect_strt_date" label="生效开始日期" align="center" width="120px"/>
        <el-table-column prop="tect_end_date" label="生效结束日期" align="center" width="120px"/>
        <el-table-column prop="brch_grad" label="分支机构等级" align="center" width="120px"/>
        <el-table-column prop="indx_cd" label="指标代码" align="center" width="100px"/>
        <el-table-column prop="indx_name" label="指标名称" align="center" width="120px"/>
        <el-table-column prop="exam_indx_wght" label="考核指标权重"  align="center" width="120px"/>
        <el-table-column prop="crt_time" label="创建时间"  align="center"/>
        <el-table-column prop="upd_time" label="更新时间" align="center"/>
        <el-table-column label="操作" align="center" width="260" fixed="right">
          <template #default="scope">
            <el-button type="success" size="small" @click="handleHistory(scope.row)">历史记录</el-button>
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">修改</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/修改对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="700px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生效开始日期" prop="tect_strt_date">
              <el-date-picker
                v-model="formData.tect_strt_date"
                type="month"
                placeholder="选择生效开始日期"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效结束日期" prop="tect_end_date">
              <el-date-picker
                v-model="formData.tect_end_date"
                type="month"
                placeholder="选择生效结束日期"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分支机构等级" prop="brch_grad">
              <el-select v-model="formData.brch_grad" placeholder="请选择分支机构等级" style="width: 100%" :disabled="isEdit">
                <el-option label="A" value="A" />
                <el-option label="B" value="B" />
                <el-option label="C" value="C" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指标代码" prop="indx_cd">
              <el-input v-model="formData.indx_cd" :disabled="isEdit"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="指标名称" prop="indx_name">
              <el-input v-model="formData.indx_name" :disabled="isEdit"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指标权重" prop="exam_indx_wght">
              <el-input type="number" min="0" max="100" v-model="formData.exam_indx_wght" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 历史记录对话框 -->
    <el-dialog
      v-model="historyDialogVisible"
      title="历史记录"
      width="1000px"
    >
      <el-table :data="historyData" border style="width: 100%">
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期" />
        <el-table-column prop="brch_grad" label="分支机构等级"/>
        <el-table-column prop="indx_cd" label="指标代码" />
        <el-table-column prop="indx_name" label="指标名称"  />
        <el-table-column prop="exam_indx_wght" label="指标权重"  />
        <el-table-column prop="upd_time" label="更新时间"/>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="historyDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'
import { format } from 'date-fns';
import {now} from "@vueuse/core";
import * as XLSX from 'xlsx';

// 查询表单
const queryForm = reactive({
  brch_grad:'',
  indx_cd:'',
  indx_name:'',
})

const formData = reactive({
  id: '',
  tect_strt_date: '',
  tect_end_date: '',
  brch_grad: '',
  indx_cd: '',
  indx_name: '',
  exam_indx_wght: '',
  upd_time: ''
})

// 表单验证规则
const formRules = {
  tect_strt_date: [
    { required: true, message: '请选择生效开始日期', trigger: 'change' }
  ],
  tect_end_date: [
    { required: true, message: '请选择生效结束日期', trigger: 'change' }
  ],
  brch_grad: [
    { required: true, message: '请选择分支机构等级', trigger: 'change' }
  ],
  indx_cd: [
    { required: true, message: '请输入指标代码', trigger: 'blur' }
  ],
  exam_indx_wght: [
    { required: true, message: '请输入指标权重', trigger: 'blur' }
  ]
}


// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()
const isEdit = ref(false)

// 历史记录对话框
const historyDialogVisible = ref(false)
const historyData = ref([])

// 初始化数据
onMounted(() => {
  // 初始化表格数据，使用查询方法以应用分页逻辑
  handleQuery()
})


//清空操作设置为空串
const handleClear = () => {
  queryForm.brch_grad = '',
  queryForm.indx_cd = ''
}

// 查询
const handleQuery = () => {
  loading.value = true

  //调用接口
  let url = "http://10.242.194.62:7300/brch_exam_indx_wght_set_view";
  axios.get(url,{
    headers:{
      'Accept-Profile':'mkt_base',
      'Prefer': 'count=exact'
    },
    params:{
      'brch_grad':'like.%' + queryForm.brch_grad+'%',
      'indx_cd': 'like.%'+ queryForm.indx_cd+'%',
      'last_flag':'eq.1',
      'limit': pageSize.value,
      'offset': (currentPage.value - 1) * pageSize.value
    }
  }).then(response=> {
    tableData.value = response.data
    tableData.value.forEach(item=>{
      item.crt_time = format(item.crt_time, 'yyyy-MM-dd HH:mm:ss')
      if (item.upd_time) {
        item.upd_time = format(item.upd_time, 'yyyy-MM-dd HH:mm:ss')
      }
    })
    const rangeHeader = response.headers.get('Content-Range')
    if (rangeHeader) {
      const match = rangeHeader.match(/\/(\d+)$/)
      totalCount.value = match ? parseInt(match[1]) : response.data.length
    } else {
      totalCount.value = response.data.length
    }
    loading.value = false
  })
}


// 新增
const handleAdd = () => {
  dialogTitle.value = '新增指标权重设置'
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 修改
const handleEdit = (row) => {
  dialogTitle.value = '修改指标权重设置'
  isEdit.value = true
  resetForm()

  // 填充表单数据
  Object.assign(formData, row)

  dialogVisible.value = true
}

const handleDelete = async (row) => {
  await ElMessageBox.confirm('确认删除该条记录？', '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })

  const requestData = {
    i_request: {
      optionflg: "3",
      oacode: urlParams.oacde,
      uuid: row.uuid
    }
  }

  const response = await fetch('http://10.242.194.62:7300/rpc/p_brch_exam_indx_wght_set_e', {
    method: 'POST',
    headers: {
      'Content-Profile': 'mkt_base',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(requestData)
  })
  if (response.ok) {
    const result = await response.json()

    if (result.o_status === 0) {
      ElMessage.success('删除成功')
      handleQuery() // 重新查询数据
    } else {
      ElMessage.error(result.o_msg || '删除失败')
    }
  } else {
    ElMessage.error('删除失败，请检查网络连接')
  }
}

// 查看历史记录
const handleHistory = (row) => {
  // 模拟获取历史记录
  let url = "http://10.242.194.62:7300/brch_exam_indx_wght_set_view";
  axios.get(url, {
    headers: {
      'Accept-Profile': 'mkt_base',
      'Prefer': 'count=exact'
    },
    params: {
      'brch_grad': 'eq.'+row.brch_grad,
      'indx_cd': 'eq.'+row.indx_cd,
      'last_flag': 'eq.0'
    }
  }).then(response => {
    historyData.value = response.data;
    historyData.value.forEach(item => {
      item.upd_time = format(item.upd_time, 'yyyy-MM-dd HH:mm:ss')
    })
    historyDialogVisible.value = true
  })
}

// 导出
const handleExport = () => {
  ElMessage.info('正在导出数据，请稍候...')
  // 获取所有数据
  axios.get('http://10.242.194.62:7300/brch_exam_indx_wght_set_view',{
    headers:{
      'Accept-Profile':'mkt_base',
      'Content-Type': 'application/json'
    },
    params: {
      'last_flag': 'eq.1'
    }
  }).then(response=> {
    console.log(response)
    if (response.status != 200) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const allData = response.data

    if (allData.length === 0) {
      ElMessage.warning('没有数据可导出')
    }
    // 准备导出数据
    const exportData = [

      // 表头
      [
        '生效开始日期', '生效结束日期', '分支机构等级', '指标代码', '指标名称',
        '考核指标权重', '创建时间', '创建人','更新时间', '更新人', '最新标识',
      ],
      // 数据行
      ...allData.map(item => [
        item.tect_strt_date,
        item.tect_end_date,
        item.brch_grad ,
        item.indx_cd ,
        item.indx_name,
        item.exam_indx_wght ,
        format(item.crt_time, 'yyyy-MM-dd HH:mm:ss'),
        item.creator || '',
        format(item.upd_time, 'yyyy-MM-dd HH:mm:ss')|| '',
        item.upd_oper || '',
        item.last_flag || ''
      ])
    ]
    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)
    // 设置列宽
    const colWidths = [
      { wch: 12 }, // 数据日期
      { wch: 15 }, // 数据来源类型
      { wch: 15 }, // 源系统
      { wch: 15 }, // 报表名称
      { wch: 15 }, // 数据条目数
      { wch: 15 }, // 部门
      { wch: 12 }, // 数据状态
      { wch: 12 }, // 准备人
      { wch: 12 }, // 审核人
      { wch: 15 },  // 表英文名
      { wch: 15 }  // 表英文名
    ]
    ws['!cols'] = colWidths
    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '分支机构考核指标权重设置')

    // 生成文件名
    const now = new Date()
    const fileName = `分支机构考核指标权重设置表_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`)
  })

}

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacde: params.get('oacde') || 'current_user',
    roleid: params.get('roleid') || '001'
  }
}

const urlParams = getUrlParams()

// 提交表单
const handleSubmit = async() => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      const requestData = {
        i_request: {
          optionflg: isEdit.value ? "2" : "1",
          oacode: urlParams.oacde,
          tect_strt_date:formData.tect_strt_date,
          tect_end_date: formData.tect_end_date,
          brch_grad:  formData.brch_grad,
          indx_cd:formData.indx_cd,
          indx_name: formData.indx_name,
          exam_indx_wght: formData.exam_indx_wght,
          upd_time:format(now(), 'yyyy-MM-dd HH:mm:ss')
        }
      };

      // 如果是编辑，需要添加uuid
      if (isEdit.value && formData.uuid) {
        requestData.i_request.uuid = formData.uuid
      }

      const response = await fetch('http://10.242.194.62:7300/rpc/p_brch_exam_indx_wght_set_e', {
        method: 'POST',
        headers: {
          'Accept-Profile': 'mkt_base',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })
      console.log(response)
      if (response.ok) {
        const result = await response.json()
        if (result.o_status === 0) {
          ElMessage.success(isEdit.value ? '修改成功' : '新增成功')
          dialogVisible.value = false
          handleQuery() // 重新查询数据
        } else {
          ElMessage.error(result.o_msg || '操作失败')
        }
      } else {
        ElMessage.error('操作失败，请检查网络连接')
      }
      dialogVisible.value = false
    }
  })
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(formData, {
    tect_strt_date: '',
    tect_end_date: '',
    brch_grad: '',
    indx_cd: '',
    indx_name: '',
    exam_indx_wght: ''
  })
}

// 处理对话框关闭
const handleDialogClose = () => {
  resetForm()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}
</script>

<style scoped>
.branch-rating-setting {
  padding: 20px;
  width: 100%;
  min-height: 100vh;
  background-color: var(--ep-bg-color);
  box-sizing: border-box;
  text-align: left;
}

.query-card {
  margin-bottom: 20px;
}

.query-form {
  padding: 10px 0;
}

.button-group {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.button-group .el-button {
  margin: 0 10px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>